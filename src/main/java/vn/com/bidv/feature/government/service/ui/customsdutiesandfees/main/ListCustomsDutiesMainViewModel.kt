package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.main

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class ListCustomsDutiesMainViewModel @Inject constructor() : ViewModelIBankBase<
        ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState,
        ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEvent,
        ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewEffect>(
    initialState = ListCustomsDutiesMainReducer.CustomsDutiesAndFeesMainViewState(
        tabLayoutIndex = 1
    ),
    reducer = ListCustomsDutiesMainReducer()
)