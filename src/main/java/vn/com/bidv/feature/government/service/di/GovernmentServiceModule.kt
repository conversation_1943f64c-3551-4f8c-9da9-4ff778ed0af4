package vn.com.bidv.feature.government.service.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoSet
import retrofit2.Retrofit
import vn.com.bidv.feature.common.data.masterdata.apis.MasterDataApi
import vn.com.bidv.feature.government.service.data.GovernmentServiceRepository
import vn.com.bidv.feature.government.service.data.MasterDataRepository
import vn.com.bidv.feature.government.service.data.governmentservice.apis.GovernmentServiceApi
import vn.com.bidv.feature.government.service.navigation.GovernmentServiceNavigation
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class GovernmentServiceModule {
    @Provides
    @Singleton
    fun provideGovernmentServiceRepository(service: GovernmentServiceApi): GovernmentServiceRepository =
        GovernmentServiceRepository(service)

    @Provides
    @Singleton
    fun provideMasterDataRepository(service: MasterDataApi): MasterDataRepository =
        MasterDataRepository(service)

    @Singleton
    @Provides
    @IntoSet
    fun provideGovernmentServiceFeatureGraphBuilder(): FeatureGraphBuilder {
        return GovernmentServiceNavigation()
    }

    @Provides
    @Singleton
    fun provideGovernmentServiceApi(retrofit: Retrofit): GovernmentServiceApi {
        return retrofit.create(GovernmentServiceApi::class.java)
    }
} 
