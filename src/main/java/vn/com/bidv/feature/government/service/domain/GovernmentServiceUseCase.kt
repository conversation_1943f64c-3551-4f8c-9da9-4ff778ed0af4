package vn.com.bidv.feature.government.service.domain

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import vn.com.bidv.feature.government.service.constants.Constants
import vn.com.bidv.feature.government.service.data.GovernmentServiceRepository
import vn.com.bidv.feature.government.service.data.governmentservice.model.InquiryCustomsDutyReq
import vn.com.bidv.feature.government.service.data.governmentservice.model.Page
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnPendingListReq
import vn.com.bidv.feature.government.service.domain.model.BaseDataListDMO
import vn.com.bidv.feature.government.service.domain.model.ChapterDMO
import vn.com.bidv.feature.government.service.domain.model.CustomsCurrencyDMO
import vn.com.bidv.feature.government.service.domain.model.DataListInquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.domain.model.DataListTxnPendingDMO
import vn.com.bidv.feature.government.service.domain.model.EconomicContentDMO
import vn.com.bidv.feature.government.service.domain.model.ExportImportTypeDMO
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.domain.model.TaxTypeDMO
import vn.com.bidv.feature.government.service.model.AddPaymentDTO
import vn.com.bidv.feature.government.service.model.ListCustomsDutiesRuleFilter
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ShareDataDTO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.TransactionStatusBase
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import vn.com.bidv.sdkbase.utils.convert
import vn.com.bidv.sdkbase.utils.exts.formatDateToString
import java.lang.reflect.Type
import javax.inject.Inject

class GovernmentServiceUseCase @Inject constructor(
    private val governmentServiceRepository: GovernmentServiceRepository,
    private val localRepository: LocalRepository
) {

    suspend fun getListPendingTransaction(
        rule: ListCustomsDutiesRuleFilter?,
        pageIndex: Int,
        pageSize: Int
    ): DomainResult<DataListTxnPendingDMO> {

        val request = TxnPendingListReq(
            page = Page(
                pageNum = pageIndex,
                pageSize = pageSize
            ),
            search = rule?.search,
            startDate = rule?.startDate?.formatDateToString(SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD),
            endDate = rule?.endDate?.formatDateToString(SdkBaseConstants.DateTimeConstants.FORMAT_YYYY_MM_DD),
            minAmount = rule?.minAmount,
            maxAmount = rule?.maxAmount,
            ccy = "VND",
            statuses = rule?.listStatusSelected,
            debitAccNo = rule?.debit,
            taxCode = rule?.tax,
            declarationNo = rule?.declaration,
            batchNo = rule?.batch
        )

        val result = governmentServiceRepository.getListPendingTransaction(request)
        return result.convert(DataListTxnPendingDMO::class.java)
    }

    fun getListCurrency(): DomainResult<List<String>> {
        return DomainResult.Success(listOf("VND"))
    }

    fun getListStatus(): DomainResult<List<TransactionStatusBase>> {
        return DomainResult.Success(
            listOf(TransactionStatusBase.INIT, TransactionStatusBase.REJECTED)
        )
    }

    suspend fun inquiryTransaction(taxId: String, declarationNo: String?, year: Int?): DomainResult<DataListInquiryCustomsDutyDMO> {
        val result = governmentServiceRepository.inquiryTransaction(
            InquiryCustomsDutyReq(
                taxCode = taxId,
                declarationNo = declarationNo,
                declarationYear = year?.toString()
            )
        )
        return result.convert {
            DataListInquiryCustomsDutyDMO(
                items = this.items!!.map {
                    InquiryCustomsDutyDMO(
                        eiTypeCode = it.eiTypeCode,
                        taxTypeCode = it.taxTypeCode,
                        ccCode = it.ccCode,
                        chapterCode = it.chapterCode,
                        ecCode = it.ecCode,
                        amount = it.amount,
                        ccy = it.ccy,
                        declarationDate = it.declarationDate,
                        declarationNo = it.declarationNo,
                        transDesc = it.transDesc,
                        payerType = it.payerType,
                        ccName = it.ccName,
                        chapterName = it.chapterName,
                        ecName = it.ecName,
                        eiTypeName = it.eiTypeName,
                        taxTypeName = it.taxTypeName,
                        treasuryCode = it.treasuryCode,
                        treasuryName = it.treasuryName,
                        admAreaCode = it.admAreaCode,
                        admAreaName = it.admAreaName,
                        revAccCode = it.revAccCode,
                        revAccName = it.revAccName,
                        revAuthCode = it.revAuthCode,
                        revAuthName = it.revAuthName,
                        payerTypeName = it.payerTypeName,
                    )
                }
            )
        }
    }

    suspend fun getListChapterCode(): DomainResult<BaseDataListDMO<ChapterDMO>> {
        val result = governmentServiceRepository.getListChapterCode()
        val type: Type = object : TypeToken<BaseDataListDMO<ChapterDMO>>() {}.type

        return result.convert(type)
    }

    suspend fun getListEconomicCode(): DomainResult<BaseDataListDMO<EconomicContentDMO>> {
        val result = governmentServiceRepository.getListEconomicCode()
        val type: Type = object : TypeToken<BaseDataListDMO<EconomicContentDMO>>() {}.type

        return result.convert(type)
    }

    fun getListCurrencyType(): List<String> {
        return listOf("VND")
    }

    suspend fun getListTaxType(): DomainResult<BaseDataListDMO<TaxTypeDMO>> {
        val result = governmentServiceRepository.getListTaxType()
        val type: Type = object : TypeToken<BaseDataListDMO<TaxTypeDMO>>() {}.type

        return result.convert(type)
    }

    suspend fun getListCustomsCurrency(): DomainResult<BaseDataListDMO<CustomsCurrencyDMO>> {
        val result = governmentServiceRepository.getListCustomsCurrency()
        val type: Type = object : TypeToken<BaseDataListDMO<CustomsCurrencyDMO>>() {}.type

        return result.convert(type)
    }

    suspend fun getListTradeType(): DomainResult<BaseDataListDMO<ExportImportTypeDMO>> {
        val result = governmentServiceRepository.getListTradeType()
        val type: Type = object : TypeToken<BaseDataListDMO<ExportImportTypeDMO>>() {}.type

        return result.convert(type)
    }

    suspend fun sendDataToMain(dto: AddPaymentDTO?) {
        val json = Gson().toJson(dto)
        localRepository.shareDataTo(
            Constants.ADD_PAYMENT_DATA,
            ShareDataDTO(Constants.ADD_PAYMENT_DATA, json)
        )
    }
}