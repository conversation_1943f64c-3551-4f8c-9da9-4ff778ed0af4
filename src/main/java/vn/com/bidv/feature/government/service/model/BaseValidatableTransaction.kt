package vn.com.bidv.feature.government.service.model

/**
 * Thể hiện thông tin để đưa cho backend. Sử dụng cho: API Xác thực khoản nộp và tính phí
 */
abstract class BaseValidatableTransaction {
    abstract val declarationNo: String
    abstract val declarationDate: String
    abstract val amount: String
    abstract val ccy: String
    abstract val chapterCode: String
    abstract val ecCode: String
    abstract val transDesc: String
    abstract val taxTypeCode: String
    abstract val ccCode: String
    abstract val eiTypeCode: String

    abstract val uniqueId: String
}