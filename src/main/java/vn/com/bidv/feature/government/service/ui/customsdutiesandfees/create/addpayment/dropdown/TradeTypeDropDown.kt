package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.dropdown

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import vn.com.bidv.designsystem.component.IBankContextMenu
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownBaseV2
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownTypeData
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankSearchDialog
import vn.com.bidv.designsystem.component.feedback.bottomsheet.SearchDialogState
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.feature.government.service.domain.model.CustomsCurrencyDMO
import vn.com.bidv.feature.government.service.domain.model.ExportImportTypeDMO
import vn.com.bidv.feature.government.service.model.FieldStatus
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentItem
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.AddPaymentReducer
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
fun TradeTypeDropDown(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
) {

    val messageError: String

    val fieldStatus =
        uiState.fieldError[AddPaymentItem.DropDownItem.TradeType] ?: FieldStatus.VALID
    val state =
        if (fieldStatus == FieldStatus.INVALID) {
            messageError =
                stringResource(R.string.truong_s_bat_buoc_nhap, stringResource(R.string.loai_hinh_xnk))
            IBFrameState.ERROR(LocalColorScheme.current)
        } else {
            messageError = ""
            IBFrameState.DEFAULT(LocalColorScheme.current)
        }

    val code = uiState.tradeTypeSelected?.eiTypeCode ?: ""
    val name = uiState.tradeTypeSelected?.eiTypeName ?: ""
    var displayName = ""

    if (code.isNotEmpty() && name.isNotEmpty()) {
        displayName = "$code - $name"
    }

    IBankInputDropdownBaseV2(
        labelText = stringResource(R.string.loai_hinh_xnk),
        required = true,
        typeData = IBankInputDropdownTypeData.Select(text = displayName),
        iconEnd = vn.com.bidv.designsystem.R.drawable.arrow_bottom_outline,
        hintTextStart = messageError,
        state = state,
        onClickEnd = {
            onEvent(
                AddPaymentReducer.AddPaymentEvent.TradeTypeEvent.ShowTradeTypeBottomSheet(
                    true
                )
            )
        },
        onClickClear = { onEvent(AddPaymentReducer.AddPaymentEvent.TradeTypeEvent.ClearTradeType) }
    )
}

@Composable
fun ShowTradeTypeBottomSheet(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
    onValueChange: (field: AddPaymentItem.DropDownItem.TradeType, value: ExportImportTypeDMO?) -> Unit
) {
    if (uiState.showTradeTypeBottomSheet) {
        IBankSearchDialog(
            title = stringResource(R.string.loai_hinh_xnk),
            itemSelected = uiState.tradeTypeSelected,
            compareKey = { it },
            showSearchBox = uiState.listTradeType != null && uiState.listTradeType.size > 10,
            listData = uiState.listTradeType,
            searchFilter = { item, query ->
                val search = "${item.eiTypeCode}${item.eiTypeName}"
                VNCharacterUtil.removeAccent(search)
                    .contains(VNCharacterUtil.removeAccent(query), ignoreCase = true)
            },
            state = SearchDialogState.CONTENT,
            onRequestDismiss = {
                onEvent(
                    AddPaymentReducer.AddPaymentEvent.TradeTypeEvent.ShowTradeTypeBottomSheet(
                        false
                    )
                )
                onValueChange(AddPaymentItem.DropDownItem.TradeType, it)
            },
            listSearchFilterText = listOf(),
            errorView = {
                IBankEmptyState(
                    modifier = Modifier.fillMaxSize(),
                    supportingText = stringResource(R.string.khong_tai_duoc_du_lieu),
                    textButton = stringResource(R.string.thu_lai)
                )
            }
        ) { _, searchItem ->
            val isSelected = searchItem.data == uiState.tradeTypeSelected
            IBankContextMenu(
                isSelected = isSelected,
                title = searchItem.data.eiTypeName.orEmpty()
            )
        }
    }
}
