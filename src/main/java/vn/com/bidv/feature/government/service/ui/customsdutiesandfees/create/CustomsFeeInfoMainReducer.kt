package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class CustomsFeeInfoMainReducer: Reducer<CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewState, CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEvent, CustomsFeeInfoMainReducer.CustomsFeeInfoMainViewEffect> {

    @Immutable
    data class CustomsFeeInfoMainViewState(
        val currentStep: Int
    ): ViewState

    @Immutable
    sealed class CustomsFeeInfoMainViewEvent: ViewEvent {
        data class ChangeStep(val step: Int): CustomsFeeInfoMainViewEvent()
    }

    @Immutable
    sealed class CustomsFeeInfoMainViewEffect(): SideEffect

    override fun reduce(
        previousState: CustomsFeeInfoMainViewState,
        event: CustomsFeeInfoMainViewEvent
    ): Pair<CustomsFeeInfoMainViewState, CustomsFeeInfoMainViewEffect?> {

        return when(event) {
            is CustomsFeeInfoMainViewEvent.ChangeStep -> {
                previousState.copy(currentStep = event.step) to null
            }
        }
    }

}