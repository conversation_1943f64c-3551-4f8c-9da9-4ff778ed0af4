package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step1

import timber.log.Timber
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.domain.model.TaxPayerInfoDMO
import vn.com.bidv.feature.government.service.model.BaseTransaction
import vn.com.bidv.feature.government.service.model.TaxPayerInfo
import vn.com.bidv.feature.government.service.model.TaxPayerInfoField

class TaxPayerInfoReducer :
    Reducer<TaxPayerInfoReducer.TaxPaymentViewState, TaxPayerInfoReducer.TaxPaymentViewEvent, TaxPayerInfoReducer.TaxPaymentSideEffect> {
    data class TaxPaymentViewState(
        val isInitialized: Boolean = false,
        val fetchedTaxId: List<ModelCheckAble<BaseTransaction>> = listOf(),

        val businessTaxManualEntries: List<ModelCheckAble<BaseTransaction>> = listOf(),
        val onBehalfTaxManualEntries: List<ModelCheckAble<BaseTransaction>> = listOf(),
        val modeDelegate: Boolean = false,
        val taxDelegatorInfo: TaxPayerInfo = TaxPayerInfo("", "", ""),
        val taxPayerInfo: TaxPayerInfo = TaxPayerInfo("", "", ""),

        val actualPayerInfo: TaxPayerInfo = TaxPayerInfo("", "", ""),
    ) : ViewState

    sealed class TaxPaymentViewEvent : ViewEvent {
        data class ChangeModeDelegate(val modeDelegate: Boolean) : TaxPaymentViewEvent()
        data class UpdateTaxPayerInfo(val field: TaxPayerInfoField, val value: String) :
            TaxPaymentViewEvent()

        data class UpdateTaxDelegatorInfo(val field: TaxPayerInfoField, val value: String) :
            TaxPaymentViewEvent()

        data class SelectTaxEntry(val taxEntry: ModelCheckAble<out BaseTransaction>) :
            TaxPaymentViewEvent()

        data class AddInquiryItems(val inquiryItems: List<BaseTransaction>) :
            TaxPaymentViewEvent()

        data class AddManualItems(val manualItems: List<BaseTransaction>):
            TaxPaymentViewEvent()

        data object InitScreen: TaxPaymentViewEvent()

        data class InitScreenSuccess(
            val taxPayerInfo: TaxPayerInfoDMO,
            val transactions: List<InquiryCustomsDutyDMO>,
        ) : TaxPaymentViewEvent()
    }

    sealed class TaxPaymentSideEffect : SideEffect {
        data object InitScreen : TaxPaymentSideEffect()
        data object ToastAddInquiryItemsSuccessfully : TaxPaymentSideEffect()
        data object ToastAddInquiryItemsDuplicated : TaxPaymentSideEffect()
    }

    override fun reduce(
        previousState: TaxPaymentViewState,
        event: TaxPaymentViewEvent
    ): Pair<TaxPaymentViewState, TaxPaymentSideEffect?> {
        return when (event) {
            is TaxPaymentViewEvent.InitScreen -> {
                previousState to TaxPaymentSideEffect.InitScreen
            }

            is TaxPaymentViewEvent.ChangeModeDelegate -> {
                if (event.modeDelegate) {
                    previousState.copy(
                        modeDelegate = true,
                        taxDelegatorInfo = previousState.actualPayerInfo,
                        taxPayerInfo = TaxPayerInfo("", "", "")
                    ) to null
                } else {
                    previousState.copy(
                        modeDelegate = false,
                        taxPayerInfo = previousState.actualPayerInfo
                    ) to null
                }
            }

            is TaxPaymentViewEvent.UpdateTaxPayerInfo -> {
                val updatedTaxPayerInfo = previousState.taxPayerInfo.copy(event.field, event.value)

                previousState.copy(
                    taxPayerInfo = updatedTaxPayerInfo,
                    actualPayerInfo = if (!previousState.modeDelegate) updatedTaxPayerInfo else previousState.actualPayerInfo
                ) to null
            }

            is TaxPaymentViewEvent.UpdateTaxDelegatorInfo -> {
                val updatedTaxDelegatorInfo = previousState.taxDelegatorInfo.copy(event.field, event.value)

                previousState.copy(
                    taxDelegatorInfo = updatedTaxDelegatorInfo,
                    actualPayerInfo = updatedTaxDelegatorInfo
                ) to null
            }

            is TaxPaymentViewEvent.SelectTaxEntry -> {
                if (!event.taxEntry.isChecked && countChecked(previousState) == 5) {
                    return previousState to null
                }
                if (previousState.modeDelegate) {
                    val updatedOnBehalfEntries = previousState.onBehalfTaxManualEntries.changeItemCheck(event.taxEntry.data)
                    previousState.copy(
                        onBehalfTaxManualEntries = updatedOnBehalfEntries
                    ) to null
                } else {
                    val searchResultOnFetchedList = previousState.fetchedTaxId.find { it.data.uniqueId == event.taxEntry.data.uniqueId }
                    if (searchResultOnFetchedList != null) {
                        previousState.copy(
                            fetchedTaxId = previousState.fetchedTaxId.changeItemCheck(event.taxEntry.data)
                        ) to null
                    } else {
                        val updatedBusinessEntries = previousState.businessTaxManualEntries.changeItemCheck(event.taxEntry.data)
                        previousState.copy(
                            businessTaxManualEntries = updatedBusinessEntries
                        ) to null
                    }
                }
            }

            is TaxPaymentViewEvent.AddInquiryItems -> {
                val result = mutableListOf<ModelCheckAble<BaseTransaction>>()
                for (item in event.inquiryItems) {
                    if (previousState.businessTaxManualEntries.any {
                            it.data == item
                        } || previousState.fetchedTaxId.any {
                            it.data == item
                        }
                    ) {
                        continue
                    }
                    result += ModelCheckAble(item)
                }
                Timber.d("Added ${result.size} record(s)")

                val updatedBusinessEntries = previousState.businessTaxManualEntries + result
                previousState.copy(
                    businessTaxManualEntries = updatedBusinessEntries
                ) to null
            }

            is TaxPaymentViewEvent.AddManualItems -> {
                val result = mutableListOf<ModelCheckAble<BaseTransaction>>()
                if (!previousState.modeDelegate){
                    for (item in event.manualItems) {
                        if (previousState.businessTaxManualEntries.any {
                                it.data == item
                            } || previousState.fetchedTaxId.any {
                                it.data == item
                            }
                        ) {
                            continue
                        }
                        result += ModelCheckAble(item)
                    }
                    val sideEffectResult = if (result.size == event.manualItems.size) {
                        TaxPaymentSideEffect.ToastAddInquiryItemsSuccessfully
                    } else {
                        TaxPaymentSideEffect.ToastAddInquiryItemsDuplicated
                    }

                    val updatedBusinessEntries = previousState.businessTaxManualEntries + result
                    previousState.copy(
                        businessTaxManualEntries = updatedBusinessEntries
                    ) to sideEffectResult
                } else {
                    for (item in event.manualItems) {
                        if (previousState.onBehalfTaxManualEntries.any {
                                it.data == item
                            }
                        ) {
                            continue
                        }
                        result += ModelCheckAble(item)
                    }
                    val sideEffectResult = if (result.size == event.manualItems.size) {
                        TaxPaymentSideEffect.ToastAddInquiryItemsSuccessfully
                    } else {
                        TaxPaymentSideEffect.ToastAddInquiryItemsDuplicated
                    }

                    val updatedOnBehalfEntries = previousState.onBehalfTaxManualEntries + result
                    previousState.copy(
                        onBehalfTaxManualEntries = updatedOnBehalfEntries
                    ) to sideEffectResult
                }
            }

            is TaxPaymentViewEvent.InitScreenSuccess -> {
                val apiTaxPayerInfo = TaxPayerInfo(
                    event.taxPayerInfo.taxCode,
                    event.taxPayerInfo.payerName,
                    event.taxPayerInfo.payerAddress
                )

                previousState.copy(
                    isInitialized = true,
                    actualPayerInfo = apiTaxPayerInfo,
                    taxPayerInfo = if (!previousState.modeDelegate) apiTaxPayerInfo else previousState.taxPayerInfo,
                    taxDelegatorInfo = if (previousState.modeDelegate) apiTaxPayerInfo else previousState.taxDelegatorInfo,
                    fetchedTaxId = event.transactions.map {
                        ModelCheckAble(it)
                    }
                ) to null
            }
        }
    }

    private fun countChecked(viewState: TaxPaymentViewState): Int {
        return if (viewState.modeDelegate) {
            viewState.onBehalfTaxManualEntries.count { it.isChecked }
        } else {
            (viewState.businessTaxManualEntries + viewState.fetchedTaxId).count { it.isChecked }
        }
    }

    private fun List<ModelCheckAble<BaseTransaction>>.changeItemCheck(item: BaseTransaction): List<ModelCheckAble<BaseTransaction>> {
        return this.map {
            if (it.data.uniqueId == item.uniqueId) {
                it.copy(isChecked = !it.isChecked)
            } else {
                it
            }
        }
    }

    private fun TaxPayerInfo.copy(field: TaxPayerInfoField, value: String): TaxPayerInfo {
        return when (field) {
            TaxPayerInfoField.TAX_ID -> copy(taxId = value)
            TaxPayerInfoField.NAME -> copy(name = value)
            TaxPayerInfoField.ADDRESS -> copy(address = value)
        }
    }

}