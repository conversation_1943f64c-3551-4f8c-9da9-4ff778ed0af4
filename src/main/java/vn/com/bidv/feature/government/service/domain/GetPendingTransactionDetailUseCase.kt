package vn.com.bidv.feature.government.service.domain

import vn.com.bidv.feature.government.service.data.GovernmentServiceRepository
import vn.com.bidv.feature.government.service.data.governmentservice.model.TxnDetailReq
import vn.com.bidv.feature.government.service.model.TransactionDetailDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class GetPendingTransactionDetailUseCase @Inject constructor(
    private val governmentServiceRepository: GovernmentServiceRepository
) {
    suspend operator fun invoke(transactionId: String): DomainResult<TransactionDetailDMO> {
        val detailRequest = TxnDetailReq(txnId = transactionId)
        val networkResult = governmentServiceRepository.getPendingTransactionDetail(detailRequest)
        return networkResult.convert(TransactionDetailDMO::class.java)
    }
}