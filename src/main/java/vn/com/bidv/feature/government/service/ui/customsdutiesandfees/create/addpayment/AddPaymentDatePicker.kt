package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment

import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownBaseV2
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownTypeData
import vn.com.bidv.designsystem.component.datepicker.IBankDatePickerDialog
import vn.com.bidv.designsystem.component.datepicker.model.DatePickerConfig
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.feature.government.service.model.FieldStatus
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.AddPaymentReducer
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.exts.dateToString
import java.util.Date

@Composable
fun AddPaymentDatePicker(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
    showDatePicker: MutableState<Boolean>
) {

    val date = uiState.date?.dateToString() ?: ""
    val displayDateText = if (uiState.date != null) date else ""

    val messageError: String

    val fieldStatus =
        uiState.fieldError[AddPaymentItem.Date] ?: FieldStatus.VALID
    val state =
        if (fieldStatus == FieldStatus.INVALID) {
            messageError =
                stringResource(
                    R.string.truong_s_bat_buoc_nhap,
                    stringResource(R.string.ngay_to_khaiqdtb)
                )
            IBFrameState.ERROR(LocalColorScheme.current)
        } else {
            messageError = ""
            IBFrameState.DEFAULT(LocalColorScheme.current)
        }



    IBankInputDropdownBaseV2(
        onClickEnd = {
            showDatePicker.value = true
        },
        required = true,
        labelText = stringResource(R.string.ngay_to_khaiqdtb),
        typeData = IBankInputDropdownTypeData.Select(text = displayDateText),
        iconEnd = vn.com.bidv.designsystem.R.drawable.calendar_outline,
        state = state,
        hintTextStart = messageError,
        onClickClear = {
            onEvent(AddPaymentReducer.AddPaymentEvent.ClearDate)
        })
}

@Composable
fun ShowDatePickerBottomSheet(
    showDatePicker: MutableState<Boolean>,
    onValueChange: (field: AddPaymentItem.Date, value: Date?) -> Unit
) {
    if (showDatePicker.value) {
        IBankDatePickerDialog(
            modifier = Modifier,
            config = DatePickerConfig.build().copy(maxDate = Date()),
            dateSelected = Date(),
            title = stringResource(R.string.chon_ngay),
            negativeButtonText = stringResource(R.string.huy),
            onDateSelected = {
                onValueChange(AddPaymentItem.Date, it)
            },
            onDismissRequest = {
                showDatePicker.value = false
            },
            onNegativeAction = {
                showDatePicker.value = false
            }
        )
    }
}