package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.detail.pendingtransaction

import IBGradient
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.card.IBankCardTransfer
import vn.com.bidv.designsystem.component.datadisplay.badge.IBankBadgeLabel
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelColor
import vn.com.bidv.designsystem.component.datadisplay.badge.LabelSize
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.IBankSectionHeader
import vn.com.bidv.designsystem.component.datadisplay.sectionheader.LeadingType
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBColorScheme
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBSpacing.spacing2xs
import vn.com.bidv.designsystem.theme.IBSpacing.spacing4xl
import vn.com.bidv.designsystem.theme.IBSpacing.spacingNone
import vn.com.bidv.designsystem.theme.IBSpacing.spacingXs
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model.ActionType
import vn.com.bidv.feature.government.service.navigation.GovernmentServiceRoute
import vn.com.bidv.feature.government.service.model.BankCardInfoDMO
import vn.com.bidv.feature.government.service.model.FeeMethod
import vn.com.bidv.feature.government.service.model.TaxItemDMO
import vn.com.bidv.feature.government.service.model.TransactionDetailDMO
import vn.com.bidv.feature.government.service.util.TransactionMenuActions
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.utils.TransactionStatusBase
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import vn.com.bidv.sdkbase.utils.exts.toFormattedDate
import vn.com.bidv.designsystem.R as DesignSystemR
import vn.com.bidv.sdkbase.utils.formatMoney

@Composable
fun PendingTransactionDetailScreen(
    transactionId: String,
    navController: NavHostController,
) {
    val curLocalColorScheme = LocalColorScheme.current

    var showConfirmDialog by remember { mutableStateOf(false) }
    var showSuccessMessage by remember { mutableStateOf<String?>(null) }
    var showErrorMessage by remember { mutableStateOf<String?>(null) }
    var menuExpanded by remember { mutableStateOf(false) }
    var showDeleteConfirmDialog by remember { mutableStateOf(false) }
    val viewModel = hiltViewModel<PendingTransactionDetailViewModel>()
    val (topMenuActions, bottomButtonAction) = with(TransactionMenuActions.PendingTransactionsScreenActions) {
        getTopMenuActions() to getBottomMenuActions().first()
    }

    val invokeAction = { actionType: ActionType ->
        when (actionType) {
            ActionType.Delete -> showDeleteConfirmDialog = true
            ActionType.EDIT -> viewModel.sendEvent(
                PendingTransactionDetailReducer.PendingTransactionDetailViewEvent.EditTransaction
            )

            ActionType.Print_Document -> viewModel.sendEvent(
                PendingTransactionDetailReducer.PendingTransactionDetailViewEvent.PrintTransaction
            )

            ActionType.History_Impact -> viewModel.sendEvent(
                PendingTransactionDetailReducer.PendingTransactionDetailViewEvent.ViewTransactionHistory
            )

            ActionType.Push_Approval -> { showConfirmDialog = true }

            else -> { /* do nothing with the rest */ }
        }
    }

    BaseScreen(
        viewModel = viewModel,
        navController = navController,
        renderContent = { uiState, onEvent ->
            LaunchedEffect(transactionId) {
                onEvent(
                    PendingTransactionDetailReducer.PendingTransactionDetailViewEvent.LoadTransactionDetail(
                        transactionId
                    )
                )
            }

            // Delete confirmation dialog
            if (showDeleteConfirmDialog) {
                IBankModalConfirm(
                    modalConfirmType = ModalConfirmType.Question,
                    title = stringResource(R.string.xac_nhan_xoa),
                    supportingText = stringResource(R.string.quy_khach_co_chac_chan_xoa_giao_dich_nay),
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.dong_y),
                            onClick = {
                                onEvent(PendingTransactionDetailReducer.PendingTransactionDetailViewEvent.DeleteTransaction)
                                showDeleteConfirmDialog = false
                            }
                        ),
                        DialogButtonInfo(
                            label = stringResource(R.string.huy),
                            onClick = { showDeleteConfirmDialog = false }
                        )
                    ),
                    onDismissRequest = { showDeleteConfirmDialog = false }
                )
            }

            ConfirmationDialogs(
                showConfirmDialog = showConfirmDialog,
                showSuccessMessage = showSuccessMessage,
                showErrorMessage = showErrorMessage,
                onConfirmDialogDismiss = { showConfirmDialog = false },
                onSuccessMessageDismiss = { showSuccessMessage = null },
                onErrorMessageDismiss = { showErrorMessage = null },
                onConfirmPush = {
                    viewModel.sendEvent(PendingTransactionDetailReducer.PendingTransactionDetailViewEvent.PushTransaction)
                    showConfirmDialog = false
                }
            )

            PendingTransactionContent(
                uiState = uiState,
                bottomButtonAction
            ) { invokeAction(bottomButtonAction) }
        },
        topAppBarConfig = TopAppBarConfig(
            isShowTopAppBar = true,
            isShowNavigationIcon = true,
            titleTopAppBar = stringResource(R.string.chi_tiet_giao_dich),
            titleTopAppBarColor = curLocalColorScheme.bgNon_opaqueInverse_pressed,
            showHomeIcon = false,
            actionItems = {
                ActionBarContent(
                    { menuExpanded },
                    { menuExpanded = it },
                    curLocalColorScheme,
                    topMenuActions,
                    invokeAction
                )
            }
        ),
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.ShowSuccessMessage -> {
                    showSuccessMessage = sideEffect.message
                }

                is PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.ShowErrorMessage -> {
                    showErrorMessage = sideEffect.message
                }

                is PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.NavigateBack -> {
                    navController.popBackStack()
                }

                is PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.EditTransaction -> {
                    navController.navigate(
                        GovernmentServiceRoute.PendingTransactionEditRoute.createRoute(sideEffect.transactionId)
                    )
                }

                is PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.ViewTransactionHistory -> {
                    navController.navigate(
                        route = IBankMainRouting.CommonRoute.CommonActionHistoryRoute.routeWithArgument.replace(
                            "{${IBankMainRouting.CommonRoute.TXN_ID}}",
                            transactionId
                        ).replace(
                            "{${IBankMainRouting.CommonRoute.TXN_CODE}}", "LOAN_TRANS_CODE"
                        )
                    )
                }

                is PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.FetchTransactionDetail -> TODO()
                is PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.PushTransaction -> TODO()
                is PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.PrintTransaction -> TODO()
                is PendingTransactionDetailReducer.PendingTransactionDetailViewEffect.DeleteTransaction -> TODO()
            }
        }
    )
}

@Composable
private fun ActionBarContent(
    getMenuVisibility: () -> Boolean,
    setMenuVisibility: (Boolean) -> Unit,
    curLocalColorScheme: IBColorScheme,
    topMenuActions: List<ActionType>,
    invokeAction: (actionType: ActionType) -> Unit,
) {
    Box {
        IconButton(
            onClick = { setMenuVisibility(true) },
            modifier = Modifier
                .size(IBSpacing.spacing3xl)
                .clip(RoundedCornerShape(spacingXs))
                .padding(spacing2xs)
        ) {
            Icon(
                painter = painterResource(id = vn.com.bidv.designsystem.R.drawable.more_vertical),
                contentDescription = null,
                tint = curLocalColorScheme.contentMainPrimary,
                modifier = Modifier.size(IBSpacing.spacingL),
            )
        }
        DropdownMenu(
            expanded = getMenuVisibility(),
            onDismissRequest = { setMenuVisibility(false) }
        ) {
            topMenuActions.forEach { action ->
                DropdownMenuItem(
                    text = { Text(stringResource(action.titleRes)) },
                    onClick = {
                        setMenuVisibility(false)
                        invokeAction(action)
                    },
                    leadingIcon = {
                        Icon(
                            painter = painterResource(id = action.resID),
                            contentDescription = null
                        )
                    }
                )
            }
        }
    }
}

@Composable
private fun ConfirmationDialogs(
    showConfirmDialog: Boolean,
    showSuccessMessage: String?,
    showErrorMessage: String?,
    onConfirmDialogDismiss: () -> Unit,
    onSuccessMessageDismiss: () -> Unit,
    onErrorMessageDismiss: () -> Unit,
    onConfirmPush: () -> Unit
) {
    if (showConfirmDialog) {
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Question,
            title = stringResource(R.string.xac_nhan_day_duyet),
            supportingText = stringResource(R.string.ban_co_chac_chan_day_duyet_giao_dich),
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(R.string.dong_y),
                    onClick = onConfirmPush
                ),
                DialogButtonInfo(
                    label = stringResource(R.string.huy),
                    onClick = onConfirmDialogDismiss
                )
            ),
            onDismissRequest = onConfirmDialogDismiss
        )
    }

    showSuccessMessage?.let { message ->
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Success,
            title = stringResource(R.string.thong_bao),
            supportingText = message,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(R.string.dong_y),
                    onClick = onSuccessMessageDismiss
                )
            ),
            onDismissRequest = onSuccessMessageDismiss
        )
    }

    showErrorMessage?.let { message ->
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Error,
            title = stringResource(R.string.loi),
            supportingText = message,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(R.string.dong_y),
                    onClick = onErrorMessageDismiss
                )
            ),
            onDismissRequest = onErrorMessageDismiss
        )
    }
}

@Composable
private fun PendingTransactionContent(
    uiState: PendingTransactionDetailReducer.PendingTransactionDetailViewState,
    actionType: ActionType,
    onInvokeAction: (ActionType) -> Unit
) {
    Box(modifier = Modifier.fillMaxSize()) {
        uiState.transactionData?.let { data ->
            TransactionContent(
                transactionData = data,
                actionType,
                onInvokeAction
            )
        }
    }
}

@Composable
private fun TransactionContent(
    transactionData: TransactionDetailDMO,
    actionType: ActionType,
    onInvokeAction: (ActionType) ->  Unit
) {
    val curLocalTypography = LocalTypography.current
    val curLocalColorScheme = LocalColorScheme.current

    Box(modifier = Modifier.fillMaxSize()) {
        androidx.compose.foundation.lazy.LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(IBSpacing.spacingM)
        ) {
            item {
                BankCardSection(transactionData)
                Spacer(modifier = Modifier.height(IBSpacing.spacingS))
            }

            item {
                AmountSection(
                    amount = transactionData.paymentAmount.formatMoney(transactionData.ccy, isShowCurrCode = true),
                    amountText = "${transactionData.paymentAmountText}"
                )
                Spacer(modifier = Modifier.height(IBSpacing.spacingL))
            }

            item {
                TransactionDetailCard(transactionData = transactionData)
                Spacer(modifier = Modifier.height(IBSpacing.spacingS))
            }

            item {
                TaxPaymentSection(items = transactionData.taxItems ?: emptyList())
                Spacer(modifier = Modifier.height(IBSpacing.spacingM))
            }

            item {
                GeneralInfoSection(transactionData = transactionData)
                Spacer(modifier = Modifier.height(80.dp))
            }
        }

        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .background(LocalColorScheme.current.bgMainPrimary)
                .padding(horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingL),
            shadowElevation = spacingXs
        ) {
            Button(
                onClick = { onInvokeAction.invoke(actionType) },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(IBSpacing.spacing5xl)
                    .border(
                        width = IBBorderDivider.borderDividerS,
                        color = LocalColorScheme.current.borderSolidPrimary,
                        shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
                    ),
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL),
                colors = ButtonDefaults.buttonColors(
                    containerColor = LocalColorScheme.current.bgBrand_01Primary
                )
            ) {
                Text(
                    text = stringResource(actionType.titleRes),
                    style = curLocalTypography.labelLabel_xl,
                    color = curLocalColorScheme.contentOn_specialPrimary
                )
            }
        }
    }
}

@Composable
private fun GeneralInfoSection(transactionData: TransactionDetailDMO) {
    InfoCard(title = stringResource(R.string.thong_tin_chung)) {
        DetailItem(
            label = stringResource(R.string.ma_giao_dich), 
            value = transactionData.transactionId
        )
        
        val status = transactionData.status?.let { TransactionStatusBase.fromString(it) }

        if (status != null) {
            DetailItemWithBadge(
                label = stringResource(R.string.trang_thai),
                badgeTitle = transactionData.statusName ?: "",
                badgeColor = status.color
            )
        }
        
        val listLabelResAndValue = listOf(
            R.string.so_tham_chieu_lo to transactionData.batchNumber,
            R.string.ghi_chu_den_nguoi_duyet to transactionData.noteToAuthorizer,
            R.string.ly_do_tu_choi to transactionData.denialReasonNote
        )
        
        listLabelResAndValue.forEach { (labelRes, value) ->
            if (!(labelRes in listOf(R.string.so_tham_chieu_lo, R.string.ghi_chu_den_nguoi_duyet, R.string.ly_do_tu_choi) && value == null)) {
                DetailItem(label = stringResource(labelRes), value = "$value")
            }
        }
      }
}

@Composable
private fun DetailItemWithBadge(label: String, badgeTitle: String, badgeColor: LabelColor) {
    val curLocalTypography = LocalTypography.current
    val curLocalColorScheme = LocalColorScheme.current

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = spacingXs),
        verticalArrangement = Arrangement.spacedBy(spacing2xs, Alignment.Top),
        horizontalAlignment = Alignment.Start,
    ) {
        Text(
            text = label,
            style = curLocalTypography.bodyBody_m,
            color = curLocalColorScheme.contentMainTertiary
        )

        IBankBadgeLabel(
            title = badgeTitle,
            badgeSize = LabelSize.M,
            badgeColor = badgeColor
        )
    }
}

@Composable
private fun BankCardSection(
    transactionData: TransactionDetailDMO
) {
    var firstCardHeight by remember { mutableIntStateOf(0) }
    val spacing = with(LocalDensity.current) { (spacing4xl).roundToPx() }
    val bankBIDVCardInfo = BankCardInfoDMO(
        title = "BIDV",
        iconPainterResId = DesignSystemR.mipmap.bidv,
        iconContentDescription = "BIDV",
        listDescription = listOf(transactionData.debitAccName ?: ""),
        subtitle = transactionData.debitAccNumber ?: "",
    )
    val beneficiaryBankCardInfo = BankCardInfoDMO(
        title = transactionData.treasuryName ?: "",
        iconContentDescription = transactionData.treasuryName ?: "",
        iconPainterResId = DesignSystemR.drawable.dich_vu_cong,
        listDescription = listOf(transactionData.authorityName ?: ""),
        subtitle = transactionData.authorityCode ?: "",
    )
    Box {
        Column {
            Box {
                IBankCardTransfer(
                    modifier = Modifier
                        .wrapContentHeight()
                        .onGloballyPositioned { coordinates ->
                            firstCardHeight = coordinates.size.height
                        },
                    title = bankBIDVCardInfo.title,
                    icon = {
                        Image(
                            painter = painterResource(id = bankBIDVCardInfo.iconPainterResId),
                            contentDescription = bankBIDVCardInfo.iconContentDescription
                        )
                    },
                    subTitle = bankBIDVCardInfo.subtitle,
                    listDescription = bankBIDVCardInfo.listDescription,
                    isShowDropDownIcon = false
                )
            }
            Spacer(modifier = Modifier.height(spacingXs))

            IBankCardTransfer(
                title = beneficiaryBankCardInfo.title,
                icon = {
                    Box(
                        modifier = Modifier
                            .size(IBSpacing.spacing3xl)
                            .clip(CircleShape)
                            .background(LocalColorScheme.current.bgMainPrimary),
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            painter = painterResource(id = beneficiaryBankCardInfo.iconPainterResId),
                            contentDescription = beneficiaryBankCardInfo.iconContentDescription
                        )
                    }
                },
                subTitle = beneficiaryBankCardInfo.subtitle,
                listDescription = beneficiaryBankCardInfo.listDescription,
                bgBrush = IBGradient.color_grd_card_nh,
                isShowLogo = false,
                isShowDropDownIcon = false
            )
        }

        Box(
            modifier = Modifier
                .wrapContentSize()
                .align(Alignment.TopCenter)
                .offset {
                    IntOffset(
                        x = 0,
                        y = (firstCardHeight / 2) + spacing
                    )
                }
                .size(IBSpacing.spacing3xl)
                .clip(CircleShape)
                .background(LocalColorScheme.current.bgMainTertiary),
            contentAlignment = Alignment.Center
        ) {
            Box(
                modifier = Modifier
                    .size(IBSpacing.spacing2xl)
                    .clip(CircleShape)
                    .background(LocalColorScheme.current.bgMainPrimary),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    modifier = Modifier.padding(spacing2xs),
                    painter = painterResource(DesignSystemR.drawable.arrow2_bottom),
                    contentDescription = "",
                    tint = LocalColorScheme.current.contentMainPrimary,
                )
            }
        }
    }
}

@Composable
private fun AmountSection(amount: String, amountText: String) {
    val curLocalTypography = LocalTypography.current
    val curLocalColorScheme = LocalColorScheme.current

    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL))
            .wrapContentHeight()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL),
                    color = LocalColorScheme.current.bgMainTertiary
                )
                .padding(
                    horizontal = IBSpacing.spacingM,
                    vertical = IBSpacing.spacingM
                )
                .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL)),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = stringResource(R.string.so_tien_nop_nsnn),
                style = curLocalTypography.bodyBody_l,
                color = curLocalColorScheme.contentMainTertiary
            )

            Text(
                text = amount,
                style = curLocalTypography.headlineHeadline_s,
            )

            Text(
                text = amountText,
                style = curLocalTypography.bodyBody_l
            )
        }
    }
}

@Composable
private fun TaxPaymentSection(items: List<TaxItemDMO>) {
    InfoCard(title = stringResource(R.string.thong_tin_khoan_nop)) {
        items.forEach { item ->
            TaxPaymentItemCard(item = item)
            Spacer(modifier = Modifier.height(IBSpacing.spacingS))
        }
    }
}

@Composable
private fun TaxPaymentItemCard(item: TaxItemDMO) {
    val curLocalTypography = LocalTypography.current
    val curLocalColorScheme = LocalColorScheme.current

    Column(
        modifier = Modifier
            .border(
                width = IBBorderDivider.borderDividerS,
                color = curLocalColorScheme.borderMainSecondary,
                shape = RoundedCornerShape(size = spacingXs)
            )
            .fillMaxWidth()
            .background(
                color = curLocalColorScheme.bgMainTertiary,
                shape = RoundedCornerShape(size = spacingXs)
            )
            .padding(spacingNone)
            .wrapContentHeight(align = Alignment.CenterVertically),
        horizontalAlignment = Alignment.Start,
    ) {
        Text(
            text = item.declarationNumber ?: "",
            color = curLocalColorScheme.contentMainPrimary,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = IBSpacing.spacingM,
                    top = IBSpacing.spacingS,
                    end = spacingXs,
                    bottom = IBSpacing.spacingS
                ),
            style = curLocalTypography.titleTitle_m,
            textAlign = TextAlign.Start,
        )

        HorizontalDivider(
            thickness = IBBorderDivider.borderDividerS,
            color = LocalColorScheme.current.borderMainPrimary
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingS)
        ) {
            Text(
                text = "${item.eiTypeCode} - ${item.eiTypeName ?: ""}",
                style = curLocalTypography.bodyBody_m,
            )
            Spacer(modifier = Modifier.height(spacing2xs))
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = item.ecName ?: "",
                    style = curLocalTypography.bodyBody_s,
                    color = curLocalColorScheme.contentMainTertiary,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .padding(spacingNone, spacingXs, spacingNone, spacingNone)
                    .fillMaxWidth()
            ) {
                Text(
                    text = item.amount.formatMoney(item.ccy, isShowCurrCode = true),
                    style = curLocalTypography.titleTitle_m
                )

                Spacer(modifier = Modifier.weight(1f))
                Icon(
                    painter = painterResource(id = DesignSystemR.drawable.calendar_outline),
                    contentDescription = null,
                    tint = LocalColorScheme.current.contentMainTertiary,
                    modifier = Modifier.size(IBSpacing.spacingM)
                )
                Spacer(modifier = Modifier.width(spacing2xs))
                Text(
                    text = item.declarationDate?.toFormattedDate() ?: "",
                    style = curLocalTypography.bodyBody_s,
                    color = curLocalColorScheme.contentMainTertiary
                )
            }
        }
    }
}

@Composable
private fun InfoCard(
    title: String,
    body: @Composable () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = LocalColorScheme.current.bgMainTertiary,
                shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
            )
    ) {
        IBankSectionHeader(
            shLeadingType = LeadingType.Dash(),
            shSectionTitle = title,
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = IBSpacing.spacingM)

        ) {
            body()
        }
    }
}

@Composable
private fun TransactionDetailCard(transactionData: TransactionDetailDMO) {
    InfoCard(
        title = stringResource(R.string.chi_tiet_giao_dich)
    ) {
        val listItemDetailFieldLabelValue = listOf(
            R.string.ma_so_thue_nguoi_nop_thue to transactionData.payerTaxCode,
            R.string.ho_ten_nguoi_nop_thue to transactionData.payerName,
            R.string.dia_chi_nguoi_nop_thue to transactionData.payerAddress,
            R.string.ma_so_nguoi_nop_thay to transactionData.altTaxCode,
            R.string.ho_ten_nguoi_nop_thay to transactionData.altPayerName,
            R.string.dia_chi_nguoi_nop_thay to transactionData.altPayerAddress,
            R.string.dia_ban_hanh_chinh to transactionData.admAreaName,
            R.string.co_quan_thu to transactionData.authorityName,
            R.string.loai_hinh_nnt to transactionData.payerTypeName,
            R.string.phi_dich_vu_bao_gom_vat to when (transactionData.feeMethod) {
                FeeMethod.CONT_FEE.serializedName -> stringResource(R.string.khach_hang_dang_ky_phi_khoan)
                else -> transactionData.feeTotal.formatMoney(transactionData.ccy, isShowCurrCode = true)
            }
        )
        listItemDetailFieldLabelValue.forEach { (labelRes, dataValue) ->
            when {
                labelRes in listOf(
                    R.string.ma_so_nguoi_nop_thay,
                    R.string.ho_ten_nguoi_nop_thay,
                    R.string.dia_chi_nguoi_nop_thay
                ) && dataValue == null -> return@forEach

                else -> DetailItem(
                    label = stringResource(labelRes),
                    value = dataValue ?: ""
                )
            }
        }
    }
}

@Composable
private fun DetailItem(label: String, value: String) {
    val curLocalTypography = LocalTypography.current
    val curLocalColorScheme = LocalColorScheme.current

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = spacingXs),
        verticalArrangement = Arrangement.spacedBy(spacing2xs, Alignment.Top),
        horizontalAlignment = Alignment.Start,
    ) {
        Text(
            text = label,
            style = curLocalTypography.bodyBody_m,
            color = curLocalColorScheme.contentMainTertiary
        )

        Text(
            text = value,
            style = curLocalTypography.titleTitle_m,
            color = curLocalColorScheme.contentMainPrimary,
            fontWeight = FontWeight.Medium
        )
    }
}

@Preview(showBackground = true, name = "Transaction Detail Card Preview")
@Composable
fun PreviewTransactionDetailCard() {
    val mockData = TransactionDetailDMO(
        transactionId = "TX123456789",
        payerName = "Công ty đầu tư và phát triển AA",
        payerAddress = "Bạch Mai, Hai Bà Trưng, Hà Nội",
        admAreaName = "Địa bàn Hai Bà Trưng",
        paymentAmount = "1550000",
        paymentAmountText = "Một tỷ năm trăm năm mươi triệu đồng",
        createdDate = "08/05/2025 10:30:45",
        status = "REJECTED",
        statusName = "Từ chối duyệt",
        authorityName = "Cơ quan hành chính Hà Nội",
        payerType = "Doanh nghiệp",
        noteToAuthorizer = "Nộp thuế doanh nghiệp hàng tháng",
        tccRefNo = "TX-2025-TX123456789",
        payerTaxCode = "*************",
        feeTotal = "15000000",
        taxItems = emptyList(),
        denialReasonNote = "",
        ccy = "VND"
    )
    TransactionDetailCard(transactionData = mockData)
}

@Preview(showBackground = true, name = "Tax Payment Section Preview")
@Composable
fun PreviewTaxPaymentSection() {
    val items = listOf(
        TaxItemDMO(
            admAreaCode = "HN10000",
            admAreaName = "Hà Nội",
            amount = "************",
            ccy = "VND",
            eiTypeCode = "A41",
            eiTypeName = "Nhập kinh doanh của doanh nghiệp đầu tư",
            declarationDate = "21/12/2023",
            declarationNumber = "66668888",
            ecName = "Nộp thuế thu nhập doanh nghiệp",
            ccCode = "VND"
        ),
        TaxItemDMO(
            admAreaCode = "HN10000",
            admAreaName = "Hà Nội",
            amount = "200000000000",
            ccy = "VND",
            eiTypeCode = "A42",
            eiTypeName = "TEST NAME BN",
            chapterCode = "ABCD",
            chapterName = "Nhập kinh doanh của doanh nghiệp đầu tư - 2",
            declarationDate = "12/12/2024",
            declarationNumber = "********",
            ecCode = "123",
            ecName = "Nộp thuế thu nhập doanh nghiệp",
            ccCode = "VND"
        )
    )
    TaxPaymentSection(items = items)
}

@Preview(showBackground = true, name = "Bank Card Section Preview")
@Composable
fun PreviewBankCardSection() {
    val mockData = TransactionDetailDMO(
        transactionId = "TXN123",
        authorityCode = "7111",
        authorityName = "CHI CUC HAI QUAN HAI PHONG",
        debitAccName = "CONG TY CO PHAN DAU TU QUANG CAO",
        debitAccNumber = "***************",
        treasuryName = "KHO BAC NHA NUOC HAI PHONG"
    )
    BankCardSection(mockData)
}

@Preview(showBackground = true, name = "Amount Section Preview")
@Composable
fun PreviewAmountSection() {
    AmountSection(amount = "1,600,000,000 VND", amountText = "Một tỷ sáu trăm triệu đồng")
}

@Preview(showBackground = true, name = "Tax Payment Item Card Preview")
@Composable
fun PreviewTaxPaymentItemCard() {
    val item = TaxItemDMO(
        declarationNumber = "************",
        declarationDate = "30/10/2024",
        eiTypeName = "Nhập kinh doanh của doanh nghiệp đầu tư",
        eiTypeCode = "A41",
        revAuthName = "Chi cục Bắc Hà Nội",
        amount = "************",
        ccCode = "VND"
    )
    TaxPaymentItemCard(item = item)
}

@Preview(showBackground = true, name = "General Info Section Preview")
@Composable
fun PreviewGeneralInfoSection() {
    val mockData = TransactionDetailDMO(
        transactionId = "243643",
        paymentAmount = "1500000",
        paymentAmountText = "Một tỷ năm trăm triệu đồng",
        createdDate = "08/05/2025 10:30:45",
        status = "REJECTED",
        statusName = "Từ chối duyệt",
        payerName = "Nguyen Van A",
        approvedBy = "Tax Department",
        noteToAuthorizer = "Khoản nộp thuế gấp",
        payerType = "Tax Payment",
        batchNumber = "*********",
        denialReasonNote = "Chưa cung cấp đầy đủ hồ sơ theo yêu cầu",
        payerTaxCode = "*************",
        payerAddress = "Bạch Mai, Hai Bà Trưng, Hà Nội",
        admAreaName = "Địa bàn Hai Bà Trưng",
        feeMethod = "Phí khoản",
        feeTotal = "12,000,000 VND",
        taxItems = emptyList()
    )
    GeneralInfoSection(transactionData = mockData)
}