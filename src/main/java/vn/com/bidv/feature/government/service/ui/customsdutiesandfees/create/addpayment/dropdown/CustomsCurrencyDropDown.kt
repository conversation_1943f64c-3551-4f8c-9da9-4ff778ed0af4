package vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import vn.com.bidv.designsystem.component.IBankContextMenu
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownBaseV2
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownTypeData
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankSearchDialog
import vn.com.bidv.designsystem.component.feedback.bottomsheet.SearchDialogState
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.feature.government.service.domain.model.ChapterDMO
import vn.com.bidv.feature.government.service.domain.model.CustomsCurrencyDMO
import vn.com.bidv.feature.government.service.model.FieldStatus
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentItem
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.AddPaymentReducer
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
fun CustomsCurrencyDropDown(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
) {
    val messageError: String

    val fieldStatus =
        uiState.fieldError[AddPaymentItem.DropDownItem.CustomsCurrency]
            ?: FieldStatus.VALID
    val state =
        if (fieldStatus == FieldStatus.INVALID) {
            messageError =
                stringResource(
                    R.string.truong_s_bat_buoc_nhap,
                    stringResource(R.string.loai_tien_hq)
                )
            IBFrameState.ERROR(LocalColorScheme.current)
        } else {
            messageError = ""
            IBFrameState.DEFAULT(LocalColorScheme.current)
        }

    val code = uiState.customsCurrencySelected?.ccCode ?: ""
    val name = uiState.customsCurrencySelected?.ccName ?: ""
    var displayName = ""

    if (code.isNotEmpty() && name.isNotEmpty()) {
        displayName = "$code - $name"
    }

    IBankInputDropdownBaseV2(
        labelText = stringResource(R.string.loai_tien_hq),
        required = true,
        typeData = IBankInputDropdownTypeData.Select(text = displayName),
        iconEnd = vn.com.bidv.designsystem.R.drawable.arrow_bottom_outline,
        hintTextStart = messageError,
        state = state,
        onClickEnd = {
            onEvent(
                AddPaymentReducer.AddPaymentEvent.CustomsCurrencyEvent.ShowCustomsCurrencyBottomSheet(
                    true
                )
            )
        },
        onClickClear = { onEvent(AddPaymentReducer.AddPaymentEvent.CustomsCurrencyEvent.ClearCustomsCurrency) }
    )
}

@Composable
fun ShowCustomsCurrencyBottomSheet(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
    onValueChange: (field: AddPaymentItem.DropDownItem.CustomsCurrency, value: CustomsCurrencyDMO?) -> Unit
) {
    if (uiState.showCustomsCurrencyBottomSheet) {
        IBankSearchDialog(
            title = stringResource(R.string.loai_tien_hq),
            itemSelected = uiState.customsCurrencySelected,
            compareKey = { it },
            showSearchBox = uiState.listCustomsCurrency != null && uiState.listCustomsCurrency.size > 10,
            listData = uiState.listCustomsCurrency,
            searchFilter = { item, query ->
                val search = "${item.ccCode}${item.ccName}"
                VNCharacterUtil.removeAccent(search)
                    .contains(VNCharacterUtil.removeAccent(query), ignoreCase = true)
            },
            state = SearchDialogState.CONTENT,
            onRequestDismiss = {
                onEvent(
                    AddPaymentReducer.AddPaymentEvent.CustomsCurrencyEvent.ShowCustomsCurrencyBottomSheet(
                        false
                    )
                )
                onValueChange(AddPaymentItem.DropDownItem.CustomsCurrency, it)
            },
            listSearchFilterText = listOf(),
            errorView = {
                IBankEmptyState(
                    modifier = Modifier.fillMaxSize(),
                    supportingText = stringResource(R.string.khong_tai_duoc_du_lieu),
                    textButton = stringResource(R.string.thu_lai)
                )
            }
        ) { _, searchItem ->
            val isSelected = searchItem.data == uiState.customsCurrencySelected
            IBankContextMenu(
                isSelected = isSelected,
                title = searchItem.data.ccName.orEmpty()
            )
        }
    }
}
