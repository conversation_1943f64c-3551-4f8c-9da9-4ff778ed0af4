package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.dropdown

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import vn.com.bidv.designsystem.component.IBankContextMenu
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownBaseV2
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownTypeData
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankSearchDialog
import vn.com.bidv.designsystem.component.feedback.bottomsheet.SearchDialogState
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.feature.government.service.domain.model.CustomsCurrencyDMO
import vn.com.bidv.feature.government.service.domain.model.EconomicContentDMO
import vn.com.bidv.feature.government.service.domain.model.TaxTypeDMO
import vn.com.bidv.feature.government.service.model.FieldStatus
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentItem
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.AddPaymentReducer
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VNCharacterUtil

@Composable
fun EconomicCodeDropDown(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
) {
    val messageError: String

    val fieldStatus =
        uiState.fieldError[AddPaymentItem.DropDownItem.EconomicCode]
            ?: FieldStatus.VALID
    val state =
        if (fieldStatus == FieldStatus.INVALID) {
            messageError = stringResource(
                R.string.truong_s_bat_buoc_nhap,
                stringResource(R.string.ma_noi_dung_kinh_ke)
            )
            IBFrameState.ERROR(LocalColorScheme.current)
        } else {
            messageError = ""
            IBFrameState.DEFAULT(LocalColorScheme.current)
        }

    val code = uiState.economicCodeSelected?.ecCode ?: ""
    val name = uiState.economicCodeSelected?.ecName ?: ""
    var displayName = ""

    if (code.isNotEmpty() && name.isNotEmpty()) {
        displayName = "$code - $name"
    }

    IBankInputDropdownBaseV2(
        labelText = stringResource(R.string.ma_noi_dung_kinh_ke),
        required = true,
        typeData = IBankInputDropdownTypeData.Select(text = displayName),
        iconEnd = vn.com.bidv.designsystem.R.drawable.arrow_bottom_outline,
        hintTextStart = messageError,
        state = state,
        onClickEnd = {
            onEvent(
                AddPaymentReducer.AddPaymentEvent.EconomicCodeEvent.ShowEconomicCodeBottomSheet(
                    true
                )
            )
        },
        onClickClear = { onEvent(AddPaymentReducer.AddPaymentEvent.EconomicCodeEvent.ClearEconomicCode) }
    )
}

@Composable
fun ShowEconomicCodeBottomSheet(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
    onValueChange: (field: AddPaymentItem.DropDownItem.EconomicCode, value: EconomicContentDMO?) -> Unit

) {
    if (uiState.showEconomicCodeBottomSheet) {
        IBankSearchDialog(
            title = stringResource(R.string.ma_noi_dung_kinh_ke),
            itemSelected = uiState.economicCodeSelected,
            compareKey = { it },
            showSearchBox = false,
            listData = uiState.listEconomicCode,
            searchFilter = { item, query ->
                val search = "${item.ecCode}${item.ecName}"
                VNCharacterUtil.removeAccent(search)
                    .contains(VNCharacterUtil.removeAccent(query), ignoreCase = true)
            },
            state = SearchDialogState.CONTENT,
            onRequestDismiss = {
                onEvent(
                    AddPaymentReducer.AddPaymentEvent.EconomicCodeEvent.ShowEconomicCodeBottomSheet(
                        false
                    )
                )
                onValueChange(AddPaymentItem.DropDownItem.EconomicCode, it)
            },
            listSearchFilterText = listOf(),
            errorView = {
                IBankEmptyState(
                    modifier = Modifier.fillMaxSize(),
                    supportingText = stringResource(R.string.khong_tai_duoc_du_lieu),
                    textButton = stringResource(R.string.thu_lai)
                )
            }
        ) { _, searchItem ->
            val isSelected = searchItem.data == uiState.economicCodeSelected
            IBankContextMenu(
                isSelected = isSelected,
                title = searchItem.data.ecName.orEmpty()
            )
        }
    }
}
