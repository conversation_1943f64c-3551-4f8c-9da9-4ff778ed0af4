package vn.com.bidv.feature.government.service.model

import vn.com.bidv.feature.government.service.ui.common.TaxPaymentEntryCard

/**
 * Thể hiện thông tin sẽ show lên U<PERSON>, <PERSON><PERSON> dụ [TaxPaymentEntryCard]
 */
interface IViewTransaction {
    fun getHeaderString(): String
    fun getValueTitle(): String
    fun getValueTaxDescription(): String
    fun getValueAmount(): String
    fun getValueCcy(): String

    /**
     * Pair<date pattern, date>
     */
    fun getValueDate(): Pair<String, String>
}