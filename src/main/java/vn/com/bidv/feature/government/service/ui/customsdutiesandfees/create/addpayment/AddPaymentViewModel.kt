package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.feature.common.utils.ModalConfirmConfig
import vn.com.bidv.feature.government.service.domain.GovernmentServiceUseCase
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.AddPaymentReducer
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class AddPaymentViewModel @Inject constructor(
    private val governmentServiceUseCase: GovernmentServiceUseCase
) : ViewModelIBankBase<AddPaymentReducer.AddPaymentState, AddPaymentReducer.AddPaymentEvent, AddPaymentReducer.AddPaymentEffect>(
    initialState = AddPaymentReducer.AddPaymentState(),
    reducer = AddPaymentReducer()
) {
    override fun handleEffect(
        sideEffect: AddPaymentReducer.AddPaymentEffect,
        onResult: (AddPaymentReducer.AddPaymentEvent) -> Unit
    ) {
        when (sideEffect) {
            is AddPaymentReducer.AddPaymentEffect.GetListChapterCode -> {
                callDomain(
                    showLoadingIndicator = false,
                    isListenAllError = true,
                    onSuccess = { result ->
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.ChapterCodeEvent.GetListChapterCodeSuccess(
                                result.data?.items
                            )
                        )
                    },
                    onFail = {
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.ShowError(
                                modalConfirm = ModalConfirmConfig(
                                    modalConfirmType = ModalConfirmType.Error,
                                    title = resourceProvider.getString(R.string.loi),
                                    supportingText = it?.errorMessage ?: ""
                                )
                            )
                        )
                    }
                ) {
                    governmentServiceUseCase.getListChapterCode()
                }
            }

            is AddPaymentReducer.AddPaymentEffect.GetListEconomicCode -> {
                callDomain(
                    showLoadingIndicator = false,
                    isListenAllError = true,
                    onSuccess = { result ->
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.EconomicCodeEvent.GetListEconomicCodeSuccess(
                                result.data?.items
                            )
                        )
                    },
                    onFail = {
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.ShowError(
                                modalConfirm = ModalConfirmConfig(
                                    modalConfirmType = ModalConfirmType.Error,
                                    title = resourceProvider.getString(R.string.loi),
                                    supportingText = it?.errorMessage ?: ""
                                )
                            )
                        )
                    }
                ) {
                    governmentServiceUseCase.getListEconomicCode()
                }
            }

            is AddPaymentReducer.AddPaymentEffect.GetListCurrencyType -> {
                onResult(
                    AddPaymentReducer.AddPaymentEvent.CurrencyTypeEvent.GetListCurrencyTypeSuccess(
                        governmentServiceUseCase.getListCurrencyType()
                    )
                )
            }

            is AddPaymentReducer.AddPaymentEffect.GetListTaxType -> {
                callDomain(
                    showLoadingIndicator = false,
                    isListenAllError = true,
                    onSuccess = { result ->
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.TaxTypeEvent.GetListTaxTypeSuccess(
                                result.data?.items
                            )
                        )
                    },
                    onFail = {
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.ShowError(
                                modalConfirm = ModalConfirmConfig(
                                    modalConfirmType = ModalConfirmType.Error,
                                    title = resourceProvider.getString(R.string.loi),
                                    supportingText = it?.errorMessage ?: ""
                                )
                            )
                        )
                    }
                ) {
                    governmentServiceUseCase.getListTaxType()
                }
            }

            is AddPaymentReducer.AddPaymentEffect.GetListCustomsCurrency -> {
                callDomain(
                    showLoadingIndicator = false,
                    isListenAllError = true,
                    onSuccess = { result ->
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.CustomsCurrencyEvent.GetListCustomsCurrencySuccess(
                                result.data?.items
                            )
                        )
                    },
                    onFail = {
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.ShowError(
                                modalConfirm = ModalConfirmConfig(
                                    modalConfirmType = ModalConfirmType.Error,
                                    title = resourceProvider.getString(R.string.loi),
                                    supportingText = it?.errorMessage ?: ""
                                )
                            )
                        )
                    }
                ) {
                    governmentServiceUseCase.getListCustomsCurrency()
                }
            }

            is AddPaymentReducer.AddPaymentEffect.GetListTradeType -> {
                callDomain(
                    showLoadingIndicator = false,
                    isListenAllError = true,
                    onSuccess = { result ->
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.TradeTypeEvent.GetListTradeTypeSuccess(
                                result.data?.items
                            )
                        )
                    },
                    onFail = {
                        onResult(
                            AddPaymentReducer.AddPaymentEvent.ShowError(
                                modalConfirm = ModalConfirmConfig(
                                    modalConfirmType = ModalConfirmType.Error,
                                    title = resourceProvider.getString(R.string.loi),
                                    supportingText = it?.errorMessage ?: ""
                                )
                            )
                        )
                    }
                ) {
                    governmentServiceUseCase.getListTradeType()
                }
            }

            is AddPaymentReducer.AddPaymentEffect.SendDataToMain -> {
                viewModelScope.launch {
                    governmentServiceUseCase.sendDataToMain(sideEffect.addPaymentDTO)
                }
                onResult(AddPaymentReducer.AddPaymentEvent.AddPaymentSuccess)
            }

            is AddPaymentReducer.AddPaymentEffect.NavigateBack -> {
                /*nothing*/
            }
        }
    }
}