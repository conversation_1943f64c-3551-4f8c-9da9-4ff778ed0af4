package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step1

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.government.service.domain.GovernmentServiceUseCase
import vn.com.bidv.feature.government.service.data.MasterDataUseCase
import vn.com.bidv.feature.government.service.domain.model.TaxPayerInfoDMO
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class TaxPayerInfoViewModel @Inject constructor(
    private val governmentServiceUseCase: GovernmentServiceUseCase,
    private val masterDataUseCase: MasterDataUseCase,
    private val userInfoUseCase: UserInfoUseCase,
) :
    ViewModelIBankBase<
            TaxPayerInfoReducer.TaxPaymentViewState,
            TaxPayerInfoReducer.TaxPaymentViewEvent,
            TaxPayerInfoReducer.TaxPaymentSideEffect
            >(
        initialState = TaxPayerInfoReducer.TaxPaymentViewState(),
        reducer = TaxPayerInfoReducer()
    ) {
    override fun handleEffect(
        sideEffect: TaxPayerInfoReducer.TaxPaymentSideEffect,
        onResult: (TaxPayerInfoReducer.TaxPaymentViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is TaxPayerInfoReducer.TaxPaymentSideEffect.InitScreen -> {
                initializeScreenData(onResult)
            }
            is TaxPayerInfoReducer.TaxPaymentSideEffect.ToastAddInquiryItemsSuccessfully -> {
                snackBar(resourceProvider.getString(R.string.luu_thanh_cong))
            }
            is TaxPayerInfoReducer.TaxPaymentSideEffect.ToastAddInquiryItemsDuplicated -> {
                snackBar(resourceProvider.getString(R.string.khoan_nop_da_ton_tai))
            }
            is TaxPayerInfoReducer.TaxPaymentSideEffect.InitScreenFailed -> {
                /*noop*/
            }
        }
    }

    private fun snackBar(text: String) {
        showSnackBar(
            IBankSnackBarInfo(
                message = text,
                primaryButtonText = resourceProvider.getString(R.string.dong)
            )
        )
    }

    private fun initializeScreenData(onResult: (TaxPayerInfoReducer.TaxPaymentViewEvent) -> Unit) {
        val cifNo = userInfoUseCase.getUserInfoFromStorage().getSafeData()?.user?.cifNo ?: ""

        callDomain(
            callBlock =  {
                masterDataUseCase.getCustomerInfo(cifNo)
            },
            onSuccess = { customerInfoResult ->
                val taxPayerInfo = customerInfoResult.data ?: run {
                    onResult(
                        TaxPayerInfoReducer.TaxPaymentViewEvent.InitScreenFailed(
                            null,
                            "Lỗi không xác định"
                        )
                    )
                    return@callDomain
                }

                callDomain(
                    callBlock = {
                        governmentServiceUseCase.inquiryTransaction(
                            taxId = taxPayerInfo.taxCode,
                            declarationNo = null,
                            year = null,
                        )
                    },
                    onSuccess = { inquiryResult ->
                        val transactions = inquiryResult.data?.items ?: emptyList()
                        onResult(
                            TaxPayerInfoReducer.TaxPaymentViewEvent.InitScreenSuccess(
                                taxPayerInfo = taxPayerInfo,
                                transactions = transactions
                            )
                        )
                    },
                    onFail = { inquiryError ->
                        if (inquiryError != null) {
                            onResult(
                                TaxPayerInfoReducer.TaxPaymentViewEvent.InitScreenFailed(
                                    inquiryError.errorCode,
                                    inquiryError.errorMessage
                                )
                            )
                        }
                    }
                )
            },
            onFail = { customerInfoError ->
                if (customerInfoError != null) {
                    onResult(
                        TaxPayerInfoReducer.TaxPaymentViewEvent.InitScreenFailed(
                            customerInfoError.errorCode,
                            customerInfoError.errorMessage
                        )
                    )
                }
            }
        )
    }

    private fun createEmptyTaxPayerInfo(): TaxPayerInfoDMO {
        return TaxPayerInfoDMO(
            taxCode = "",
            payerName = "",
            payerAddress = ""
        )
    }
}
