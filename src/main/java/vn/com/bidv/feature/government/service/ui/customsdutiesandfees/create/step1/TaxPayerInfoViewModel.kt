package vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.step1

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.government.service.domain.GovernmentServiceUseCase
import vn.com.bidv.feature.government.service.data.MasterDataUseCase
import vn.com.bidv.feature.government.service.domain.model.InquiryCustomsDutyDMO
import vn.com.bidv.feature.government.service.domain.model.TaxPayerInfoDMO
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import vn.com.bidv.sdkbase.utils.ResourceProvider
import javax.inject.Inject

@HiltViewModel
class TaxPayerInfoViewModel @Inject constructor(
    private val governmentServiceUseCase: GovernmentServiceUseCase,
    private val masterDataUseCase: MasterDataUseCase,
    private val userInfoUseCase: UserInfoUseCase,
) :
    ViewModelIBankBase<
            TaxPayerInfoReducer.TaxPaymentViewState,
            TaxPayerInfoReducer.TaxPaymentViewEvent,
            TaxPayerInfoReducer.TaxPaymentSideEffect
            >(
        initialState = TaxPayerInfoReducer.TaxPaymentViewState(),
        reducer = TaxPayerInfoReducer()
    ) {
    override fun handleEffect(
        sideEffect: TaxPayerInfoReducer.TaxPaymentSideEffect,
        onResult: (TaxPayerInfoReducer.TaxPaymentViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is TaxPayerInfoReducer.TaxPaymentSideEffect.InitScreen -> {
                initializeScreenData(onResult)
            }
            is TaxPayerInfoReducer.TaxPaymentSideEffect.ToastAddInquiryItemsSuccessfully -> {
                snackBar(resourceProvider.getString(R.string.luu_thanh_cong))
            }
            is TaxPayerInfoReducer.TaxPaymentSideEffect.ToastAddInquiryItemsDuplicated -> {
                snackBar(resourceProvider.getString(R.string.khoan_nop_da_ton_tai))
            }
        }
    }

    private fun snackBar(text: String) {
        showSnackBar(
            IBankSnackBarInfo(
                message = text,
                primaryButtonText = resourceProvider.getString(R.string.dong)
            )
        )
    }

    private fun initializeScreenData(onResult: (TaxPayerInfoReducer.TaxPaymentViewEvent) -> Unit) {
        val cifNo = try {
            userInfoUseCase.getUserInfoFromStorage().getSafeData()?.user?.cifNo ?: ""
        } catch (e: Exception) {
            onResult(
                TaxPayerInfoReducer.TaxPaymentViewEvent.InitScreenSuccess(
                    taxPayerInfo = createEmptyTaxPayerInfo(),
                    transactions = emptyList()
                )
            )
            return
        }

        // Load tax payer info and transactions independently
        var taxPayerInfo: TaxPayerInfoDMO? = null
        var transactions: List<InquiryCustomsDutyDMO> = emptyList()
        var completedOperations = 0
        val totalOperations = 2

        fun checkCompletion() {
            completedOperations++
            if (completedOperations >= totalOperations) {
                onResult(
                    TaxPayerInfoReducer.TaxPaymentViewEvent.InitScreenSuccess(
                        taxPayerInfo = taxPayerInfo ?: createEmptyTaxPayerInfo(),
                        transactions = transactions
                    )
                )
            }
        }

        callDomain(
            onSuccess = {
                taxPayerInfo = it.data
                checkCompletion()
            },
            onFail = {
                taxPayerInfo = null
                checkCompletion()
            }
        ) {
            masterDataUseCase.getCustomerInfo(cifNo)
        }

        callDomain(
            onSuccess = {
                transactions = it.data ?: emptyList()
                checkCompletion()
            },
            onFail = {
                transactions = emptyList()
                checkCompletion()
            }
        ) {
            // Try to get tax code from previously loaded taxPayerInfo, or load it separately
            val taxCode = taxPayerInfo?.taxCode ?: run {
                try {
                    masterDataUseCase.getCustomerInfo(cifNo).getSafeData()?.taxCode ?: ""
                } catch (e: Exception) {
                    ""
                }
            }

            if (taxCode.isNotEmpty()) {
                governmentServiceUseCase.inquiryTransaction(
                    taxId = taxCode,
                    declarationNo = null,
                    year = null,
                ).let { result ->
                    when (result) {
                        is DomainResult.Success -> DomainResult.Success(result.data?.items ?: emptyList())
                        is DomainResult.Error -> result
                    }
                }
            } else {
                DomainResult.Success(emptyList())
            }
        }
    }

    private fun createEmptyTaxPayerInfo(): TaxPayerInfoDMO {
        return TaxPayerInfoDMO(
            taxCode = "",
            payerName = "",
            payerAddress = ""
        )
    }
}
