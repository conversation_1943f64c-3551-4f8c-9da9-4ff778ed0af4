package vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.dropdown

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import vn.com.bidv.designsystem.component.IBankContextMenu
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownBaseV2
import vn.com.bidv.designsystem.component.dataentry.IBankInputDropdownTypeData
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankSearchDialog
import vn.com.bidv.designsystem.component.feedback.bottomsheet.SearchDialogState
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.feature.government.service.domain.model.ChapterDMO
import vn.com.bidv.feature.government.service.model.FieldStatus
import vn.com.bidv.feature.government.service.ui.customsdutiesandfees.create.addpayment.AddPaymentItem
import vn.com.bidv.feature.government.service.ui.list.customsfeeinfo.addpayment.AddPaymentReducer
import vn.com.bidv.localization.R

@Composable
fun CurrencyTypeDropDown(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
) {
    val messageError: String

    val fieldStatus = uiState.fieldError[AddPaymentItem.DropDownItem.CurrencyType] ?: ""
    val state = if (fieldStatus == FieldStatus.INVALID) {
        messageError =
            stringResource(R.string.truong_s_bat_buoc_nhap, stringResource(R.string.loai_tien))
        IBFrameState.ERROR(LocalColorScheme.current)
    } else {
        messageError = ""
        IBFrameState.DEFAULT(LocalColorScheme.current)
    }



    IBankInputDropdownBaseV2(
        labelText = stringResource(R.string.loai_tien),
        required = true,
        typeData = IBankInputDropdownTypeData.Select(text = uiState.currencyTypeSelected ?: ""),
        iconEnd = vn.com.bidv.designsystem.R.drawable.arrow_bottom_outline,
        hintTextStart = messageError,
        state = state,
        onClickEnd = {
            onEvent(
                AddPaymentReducer.AddPaymentEvent.CurrencyTypeEvent.ShowCurrencyTypeBottomSheet(
                    true
                )
            )
        },
        onClickClear = { onEvent(AddPaymentReducer.AddPaymentEvent.CurrencyTypeEvent.ClearCurrencyType) })
}

@Composable
fun ShowCurrencyTypeBottomSheet(
    uiState: AddPaymentReducer.AddPaymentState,
    onEvent: (AddPaymentReducer.AddPaymentEvent) -> Unit,
    onValueChange: (field: AddPaymentItem.DropDownItem.CurrencyType, value: String?) -> Unit
) {
    if (uiState.showCurrencyTypeBottomSheet) {
        IBankSearchDialog(
            title = stringResource(R.string.loai_tien),
            itemSelected = uiState.currencyTypeSelected,
            compareKey = {},
            showSearchBox = false,
            listData = uiState.listCurrencyType,
            searchFilter = { _, _ -> true },
            state = SearchDialogState.CONTENT,
            onRequestDismiss = {
                onEvent(
                    AddPaymentReducer.AddPaymentEvent.CurrencyTypeEvent.ShowCurrencyTypeBottomSheet(
                        false
                    )
                )
                onValueChange(AddPaymentItem.DropDownItem.CurrencyType, it)
            },
            listSearchFilterText = listOf(),
            errorView = {
                IBankEmptyState(
                    modifier = Modifier.fillMaxSize(),
                    supportingText = stringResource(R.string.khong_tai_duoc_du_lieu),
                    textButton = stringResource(R.string.thu_lai)
                )
            }) { _, searchItem ->
            val isSelected = searchItem.data == uiState.currencyTypeSelected
            IBankContextMenu(
                isSelected = isSelected,
                title = searchItem.data
            )
        }
    }
}
