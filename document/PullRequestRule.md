# PullRequest Rules

## [Background]

Provide a brief overview of the feature or change request being addressed in this PR. Summarize the purpose and any relevant context.

## [Requirement]

Feature/Change Request: Describe in detail what this feature or change request needs to accomplish.

## [Root cause]

For Bug Fixes: Explain why this bug occurred, outlining any underlying issues or causes identified during debugging.

## [Solution]

Describe the logic and approach taken to implement the solution. Include any major functions, classes, or modules modified or created.
List step-by-step changes or notable parts of the code.

## [Side Affect]

Describe any potential side effects or areas of the code that could be affected by this change. Mention any additional tests that might be necessary due to this.

## [Evidence]

Attach screenshots or videos that show the feature working as intended or the bug being resolved. Provide any relevant logs, metrics, or performance checks if applicable.


# Sample

![PullRequestRule.webp](/picture/PullRequestRule.webp)
