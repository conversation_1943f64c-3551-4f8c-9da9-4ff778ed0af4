# Git Flow with Submodules for iBank 2

This document outlines the process for using Git Flow in the iBank 2 project that includes submodules.

## Initial Setup

**Clone the main repository:**

    git clone <url git ibank-2-workspace>
    cd ibank-2-workspace
    .\run_first_init.bat


## Working with Git Flow
**Submodule**: 
Move to directory of submodule and create new branch feature base on branch develop.

    cd path/to/submodule 
    git checkout develop
    git checkout -b feature/<featurename>

Do some change on submodule

    cd path/to/submodule
    # Do some change on submodule
    git add 
    git commit -m "Commit message content"
    git push origin feature/<featurename>

When need publish change from submodule to other user, 
create merge request from feature branch to develop branch.

    cd path/to/submodule
    git fetch origin develop
    git rebase origin/develop feature/<featurename>
    git push origin feature/<featurename>

**Get new commit**: Step to get new commit from other user.
    
    cd path/to/mainproject
    git fetch origin develop
    git rebase origin/develop develop (git rebase will trickger run file update_init_submodule.bat)
    If not see update_init_submodule.bat auto run, run it manual.

**For contractor**: For the contractor, the process is similar; 
however, there is an additional step of pulling the code from the contractor's repository 
to the local machine, and then pushing it to BIDV's repository. 



    