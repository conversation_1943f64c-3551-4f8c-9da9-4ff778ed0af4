# Use this prompt template to generate UI Component from specific design (figma - for example) by AI (ChatGPT - for example)

output jetpack compose reusable code for component ui [TYPE] with following attributes as below :

[LIST OF ATTRIBUTES]

[THEME] (color, style, space ...) define by below class :
[CODE IN PROJECT]

usage of [THEME]:
[CODE IN PROJECT]

component [TYPE] has common attributes specific as below :
[COMMON ATTRIBUTES SPECIFIC]

component [TYPE] has many state, each state with attributes modifier as below :
- [NAME] state : [LIST OF ATTRIBUTES WITH SPECIFIC STATE]
  ...