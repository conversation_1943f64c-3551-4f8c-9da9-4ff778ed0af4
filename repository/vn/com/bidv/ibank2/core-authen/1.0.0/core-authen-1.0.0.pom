<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>vn.com.bidv.ibank2</groupId>
  <artifactId>core-authen</artifactId>
  <version>1.0.0</version>
  <packaging>aar</packaging>
  <dependencies>
    <dependency>
      <groupId>com.google.dagger</groupId>
      <artifactId>hilt-android</artifactId>
      <version>2.48</version>
    </dependency>
    <dependency>
      <groupId>vn.com.bidv.ibank2</groupId>
      <artifactId>core-log</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>vn.com.bidv.ibank2</groupId>
      <artifactId>core-common</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>vn.com.bidv.ibank2</groupId>
      <artifactId>core-network</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>4.11.0</version>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.10</version>
    </dependency>
    <dependency>
      <groupId>androidx.security</groupId>
      <artifactId>security-crypto-ktx</artifactId>
      <version>1.1.0-alpha06</version>
    </dependency>
  </dependencies>
</project>
