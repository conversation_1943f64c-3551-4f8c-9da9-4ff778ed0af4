#!/bin/bash
# Script to copy modules from source project to target project
# Checks out develop branch for each submodule before copying

# Print colored status messages
print_status() {
  local color="\033[0;36m"  # Cyan color
  local reset="\033[0m"     # Reset color
  echo -e "${color}==>${reset} $1"
}

# Error handling function
handle_error() {
  local error_msg="$1"
  echo -e "\033[0;31mERROR:\033[0m $error_msg"
  exit 1
}

# Warning function
print_warning() {
  local warning_msg="$1"
  echo -e "\033[0;33mWARNING:\033[0m $warning_msg"
}

# Function to checkout develop branch in a git repository
checkout_develop() {
  local repo_path="$1"
  local module_name="$2"
  
  # Check if it's a git repository
  if [ ! -e "$repo_path/.git" ]; then
    print_warning "$module_name is not a git repository, skipping git checkout"
    return 0
  fi
  
  print_status "Checking out develop branch for $module_name..."
  
  # Change to the repository directory
  cd "$repo_path" || handle_error "Failed to change directory to $repo_path"
  
  # Fetch latest changes
  print_status "Fetching latest changes for $module_name..."
  git fetch origin || print_warning "Failed to fetch from origin for $module_name"
  
  # Check if develop branch exists
  if git show-ref --verify --quiet refs/heads/develop; then
    # Local develop branch exists
    git checkout develop || handle_error "Failed to checkout develop branch for $module_name"
    print_status "Checked out local develop branch for $module_name"
    
    # Pull latest changes
    git pull origin develop || print_warning "Failed to pull latest changes for $module_name"
  elif git show-ref --verify --quiet refs/remotes/origin/develop; then
    # Remote develop branch exists but no local branch
    git checkout -b develop origin/develop || handle_error "Failed to checkout remote develop branch for $module_name"
    print_status "Checked out and created local develop branch from origin for $module_name"
  else
    print_warning "No develop branch found for $module_name, staying on current branch"
  fi
  
  # Show current branch and latest commit
  current_branch=$(git branch --show-current)
  latest_commit=$(git log --oneline -1)
  print_status "$module_name is now on branch: $current_branch"
  print_status "Latest commit: $latest_commit"
}

# Define source project path (to be provided as argument or set manually)
if [ -z "$1" ]; then
  # No argument provided, ask user for source path
  read -p "Enter the source project path: " SOURCE_REPO
else
  SOURCE_REPO="$1"
fi

# Verify source path exists
if [ ! -d "$SOURCE_REPO" ]; then
  handle_error "Source repository directory does not exist: $SOURCE_REPO"
fi

# Current directory is the target
TARGET_REPO=$(pwd)

print_status "Starting copy from source project to target project..."
print_status "Source: $SOURCE_REPO"
print_status "Target: $TARGET_REPO"

# Define the specific modules to copy
MODULES=(
  "core/public"
  "repository"
  "feature/login"
  "feature/common"
  "feature/homepage"
)

# Store original directory to return to
ORIGINAL_DIR=$(pwd)

# Process each defined module
for module_path in "${MODULES[@]}"; do
  source_module_path="$SOURCE_REPO/$module_path"
  target_module_path="$TARGET_REPO/$module_path"
  
  # Check if source directory exists
  if [ ! -d "$source_module_path" ]; then
    print_status "WARNING: Source directory doesn't exist: $source_module_path"
    print_status "Skipping module: $module_path"
    continue
  fi
  
  # Checkout develop branch for the submodule
  checkout_develop "$source_module_path" "$module_path"
  
  # Return to original directory
  cd "$ORIGINAL_DIR" || handle_error "Failed to return to original directory"
  
  # Check if target directory exists, create if not
  if [ ! -d "$target_module_path" ]; then
    print_status "Creating target directory: $target_module_path"
    mkdir -p "$target_module_path" || handle_error "Failed to create directory: $target_module_path"
  fi
  
  print_status "Copying $module_path from source to target..."
  
  # Copy recursively, preserving attributes
  # Using rsync if available for more efficient copying
  if command -v rsync &> /dev/null; then
    # Exclude .git directory to avoid copying git history
    rsync -av --delete --exclude='.git' "$source_module_path/" "$target_module_path/" || handle_error "Failed to rsync $module_path"
  else
    # Fallback to cp if rsync is not available
    rm -rf "$target_module_path"/* || handle_error "Failed to clean target directory: $target_module_path"
    # Copy everything except .git directory
    find "$source_module_path" -mindepth 1 -maxdepth 1 ! -name '.git' -exec cp -R {} "$target_module_path"/ \; || handle_error "Failed to copy $module_path"
  fi
  
  print_status "Successfully copied $module_path from source to target"
done

print_status "All modules have been successfully copied from source to target repository!"
print_status "Source: $SOURCE_REPO"
print_status "Target: $TARGET_REPO"