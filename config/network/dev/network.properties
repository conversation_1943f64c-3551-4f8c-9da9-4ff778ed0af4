## This file must *NOT* be checked into Version Control Systems,
# as it contains information specific to your local configuration.
#
# Location of the SDK. This is only used by Gradle.
# For customization when using a Version Control System, please read the
# header note.
#Thu Jul 04 09:12:57 ICT 2024
DEBUG_MODE=true
TIME_OUT_IN_MINI_SECOND=30000
MAX_REQUEST_PER_HOST=5
BASE_URL=https://bidv.net:9303/bidvorg/service/open-banking/ibank2-dev/
RETRY_ON_CONNECT_FAIL=false
NET_WORK_CODE_SUCCESS=0,00
USE_MOCK_DATA=false
REFRESH_TOKEN_END_POINT=auth/mobile/refresh-token
LOGIN_END_POINT=auth/mobile/login,auth/login/biometric/challenge/1.0
NON_AUTH_END_POINT=auth/mobile/non-auth,auth/pw/forgot/create/1.0,auth/pw/forgot/security-questions/1.0,utilities/smartotp/user/delete/1.0,utilities/auth/qrcode/parse/1.0,version/1.0,utilities/smartotp/user/sync-time/1.0,utilities/smartotp/user/lock/1.0,utilities/auth/trans/push-otp/1.0
FLOW_AUTHENTICATION_END_POINT=auth/mobile/login,auth/otp/verify,auth/login/biometric/verify/1.0
APIS_REQUIRING_DEVICE_ID_END_POINT=auth/otp/verify,utilities/smartotp/user/lock/1.0,utilities/auth/trans/push-otp/1.0,utilities/smartotp/user/request-active-retry/1.0,utilities/smartotp/user/approve-pending/1.0,utilities/admin/approval-request/action/1.0,auth/login/biometric/challenge/1.0,auth/login/biometric/verify/1.0,utilities/smartotp/user/delete/1.0,utilities/smartotp/user/sync-time/1.0
TURN_ON_BIOMETRIC_END_POINT=auth/biometric/on/1.0
VERIFY_OTP_BIOMETRIC_END_POINT=auth/otp/secured/verify/1.0
APP_TIMEOUT_MINI_SECOND=300000

APIS_USE_DEFAULT_KEY_ENDPOINT=auth/mobile/login,auth/login/biometric/challenge/1.0,auth/pw/forgot/create/1.0,utilities/auth/trans/push-otp/1.0,utilities/auth/qrcode/parse/1.0,utilities/smartotp/user/lock/1.0,utilities/smartotp/user/delete/1.0,utilities/smartotp/user/sync-time/1.0,version/1.0
APIS_USE_SID_KEY_END_POINT=auth/pw/change/create,auth/otp/resend,auth/pw/forgot/security-questions/1.0,auth/otp/verify,auth/login/biometric/verify/1.0
APIS_GEN_KEY=auth/mobile/login,auth/login/biometric/challenge/1.0,auth/pw/forgot/create/1.0

HEADER_X-Client-ID=635bdf50465dce92380c15b9c19d8c51
HEADER_Channel=IBANK2
HEADER_User-Agent=Android
HEADER_X-Forwarded-For=10.21.101.126
HEADER_I-Mobile=true
HEADER_I-Client-ID=APP
HEADER_I-Encrypted=true
HEADER_I-Os=ANDROID
HEADER_Content-Type=application/json

SUPPORT_URLS=terms=http://10.202.100.73/support/terms,faq=http://10.202.100.73/support/fre-asked-questions,guideline=https://www.bidv.net/ibankmedia/avatar/VXWbwNgPR1mVey1wQw2vBQ/HDSDIBANK20_UTILITIES_MobileApp.pdf
APIS_NOT_ENCRYPT_END_POINT=utilities/userinfo/avatar/upload/1.0
URL_TERM_DEPOSIT=https://www.bidv.net/bidvdirectsit/support/terms