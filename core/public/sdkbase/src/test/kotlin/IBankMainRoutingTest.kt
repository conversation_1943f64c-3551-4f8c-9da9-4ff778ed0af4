import org.junit.Test
import vn.com.bidv.sdkbase.navigation.listPublicRoutes
import kotlin.test.assertEquals

class IBankMainRoutingTest {

    @Test
    fun testListPublicRoutes() {
        val expectedRoutes = listOf(
            "auth_route",
            "home_route",
            "notify_route",
            "cnr_route",
            "inquiry_route",
            "deposit_route",
            "transfer_route"
        )
        val actualRoutes = listPublicRoutes()
        println(actualRoutes)
        assertEquals(expectedRoutes, actualRoutes)
    }
}