package vn.com.bidv.sdkbase.ui.component.snackbar

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInWindow
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import vn.com.bidv.designsystem.theme.IBSpacing

@Composable
fun Modifier.pinSnackBarPosition(
    snackBarPosition: SnackBarPosition,  // position of snackbar relative to view
    margin: Dp = 0.dp,                   // margin between snackbar and view
    viewModel: SnackBarPositionViewModel =  hiltViewModel(),
): Modifier {
    val density = LocalDensity.current
    val snackBarHeight = 48.dp
    return this.onGloballyPositioned { coordinates ->
        val position = coordinates.positionInWindow()
        val size = coordinates.size
        val yPos = position.y.toInt()

        val adjustedPosition: IntOffset = with(density) {
            when (snackBarPosition) {
                SnackBarPosition.TopOut -> IntOffset(
                    IBSpacing.spacingM.toPx().toInt(),
                    yPos - margin.toPx().toInt() - snackBarHeight.toPx().toInt()
                )

                SnackBarPosition.TopIn -> IntOffset(
                    IBSpacing.spacingM.toPx().toInt(),
                    yPos + margin.toPx().toInt()
                )
                SnackBarPosition.BottomOut -> IntOffset(
                    IBSpacing.spacingM.toPx().toInt(),
                    yPos + size.height + margin.toPx().toInt()
                )

                SnackBarPosition.BottomIn -> IntOffset(
                    IBSpacing.spacingM.toPx().toInt(),
                    yPos + size.height - margin.toPx().toInt() - snackBarHeight.toPx().toInt()
                )
            }
        }

        viewModel.setSnackBarOffset(adjustedPosition)
    }
}



