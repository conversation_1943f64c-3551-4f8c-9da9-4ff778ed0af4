package vn.com.bidv.sdkbase.data

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable
import vn.com.bidv.sdkbase.navigation.notifyroute.NotiParam
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants.NotificationConstants.REDIRECT_ID

@Serializable
data class NotificationModel(
    val isLogin: <PERSON>olean,
    val notificationData: NotificationData? = null
) : java.io.Serializable

@Serializable
data class NotificationData(
    @SerializedName(REDIRECT_ID)
    val navigateId: String,
    val notifyId: Long? = null,
    val notiType: String? = null,
    val displayTab: String? = null,
    val listParam: List<NotiParam>? = null,
) : java.io.Serializable