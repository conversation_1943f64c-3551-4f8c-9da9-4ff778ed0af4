package vn.com.bidv.sdkbase.utils.exts

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri

object ContextExt {
    private const val IBANK_PACKAGE_NAME = "vn.com.bidv.ibank"
    fun Context.openAppInGooglePlay() {
        // tạm thời hard code marketUrl, link app trên google play
        val marketUrl = ""
        try {
            if (marketUrl.isNotBlank()) {
                startActivity(Intent(Intent.ACTION_VIEW).apply { data = Uri.parse(marketUrl) })
            } else {
                startActivity(
                    Intent(
                        Intent.ACTION_VIEW
                    ).apply {
                        data = Uri.parse("market://details?id=$IBANK_PACKAGE_NAME")
                        setPackage("com.android.vending")
                    }
                )
            }
        } catch (anfe: ActivityNotFoundException) {
            if (marketUrl.isBlank()) {
                startActivity(
                    Intent(
                        Intent.ACTION_VIEW
                    ).apply {
                        data =
                            Uri.parse("https://play.google.com/store/apps/details?id=$IBANK_PACKAGE_NAME")
                    }
                )
            }
        }
    }
}