package vn.com.bidv.sdkbase.navigation.notifyroute

import androidx.navigation.NavHostController
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.navigation.IBankMainRouting.NotifyRoute
import vn.com.bidv.sdkbase.navigation.IBankMainRouting.NotifyRoute.Companion.DISPLAY_TAB
import vn.com.bidv.sdkbase.navigation.IBankMainRouting.NotifyRoute.Companion.REDIRECT_ID
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants.NotificationConstants
import vn.com.bidv.sdkbase.utils.exts.navigateWithArgument

class NotificationBuilderUtils private constructor(
    private val notificationRouteBuilders: Set<NotificationRouteBuilder>
) {
    companion object {
        private var instance: NotificationBuilderUtils? = null

        private val defaultParamKeys = setOf(
            NotificationConstants.TAB_1,
            NotificationConstants.TAB_2,
            NotificationConstants.FUNCTION_CODE
        )

        fun initialize(notificationRouteBuilders: Set<NotificationRouteBuilder>) {
            try {
                instance = NotificationBuilderUtils(notificationRouteBuilders)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        fun routeToDestinationByNotyInfo(
            navController: NavHostController,
            navigateId: String? = null,
            displayTab: String? = null,
            listParam: List<NotiParam>? = null,
            isRedirectDefault: Boolean = false
        ) {
            if (navigateId.isNullOrEmpty()) return
            instance?.notificationRouteBuilders?.firstNotNullOfOrNull { builder ->
                val urlStr = buildString {
                    append(navigateId)

                    listParam?.runCatching {
                        val params = listParam.associate { it.key to it.value }

                        val (defaultKeys, remainKeys) = params.keys.partition { it in defaultParamKeys }

                        defaultKeys.sorted()
                            .forEach { key -> params[key]?.let { append("$key$it") } }
                        remainKeys.sorted().forEach { append(it) }
                    }
                }

                builder.buildRoute(urlStr, defaultParamKeys, listParam)
            }?.let { des ->
                try {
                    navController.navigateWithArgument(
                        route = des.route,
                        listData = des.listParam
                    ) {
                        launchSingleTop = true
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            } ?: run {
                if (!isRedirectDefault) return@run
                try {
                    navController.navigateWithArgument(
                        route = NotifyRoute.NotifyMainRoute.route,
                        listData = listOf(
                            REDIRECT_ID to navigateId,
                            DISPLAY_TAB to displayTab
                        )
                    ) {
                        launchSingleTop = true
                    }
                } catch (e: Exception) {
                    BLogUtil.e(e.message.orEmpty())
                }
            }
        }
    }
}