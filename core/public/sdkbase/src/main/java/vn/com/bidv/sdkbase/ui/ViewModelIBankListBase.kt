package vn.com.bidv.sdkbase.ui

import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreReducer
import vn.com.bidv.designsystem.ui.listwithloadmorev2.ListAutoLoadMoreViewModel
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.RuleFilters
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ReloadDataDTO
import vn.com.bidv.sdkbase.utils.ResourceProvider
import javax.inject.Inject

abstract class ViewModelIBankListBase<T, Rule : RuleFilters>(
    reducer: ListAutoLoadMoreReducer<T, Rule>,
    itemPerPage: Int = 20
) : ListAutoLoadMoreViewModel<T, Rule>(
    reducer,
    itemPerPage
), ViewModelIBankBaseInterface {
    @Inject
    protected lateinit var localRepositoryImp: LocalRepository

    @Inject
    @IoDispatcher
    protected lateinit var iOdispatcherImp: CoroutineDispatcher

    @Inject
    protected lateinit var resourceProviderImp: ResourceProvider

    override val listSubscribedKey = mutableListOf<String>()
    override val reloadDataCache = mutableMapOf<String, ReloadDataDTO>()
    override val localRepository
        get() = localRepositoryImp
    override val ioDispatcher
        get() = iOdispatcherImp
    override val resourceProvider
        get() = resourceProviderImp
    override val viewModelScopes
        get() = viewModelScope

    override fun onCleared() {
        onViewModelCleared()
        super.onCleared()
    }
}