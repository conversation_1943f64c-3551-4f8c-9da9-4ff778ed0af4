package vn.com.bidv.sdkbase.ui.component.snackbar

import androidx.compose.ui.unit.IntOffset
import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.sdkbase.data.LocalRepository
import javax.inject.Inject

@HiltViewModel
class SnackBarPositionViewModel @Inject constructor(
    private val localRepository: LocalRepository
) : ViewModel() {
    fun setSnackBarOffset(offset: IntOffset) {
        localRepository.setSnackBarOffset(offset)
    }
}