package vn.com.bidv.sdkbase.ui

import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.common.patterns.mvi.BaseMviViewModel
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.data.ReloadDataDTO
import vn.com.bidv.sdkbase.utils.ResourceProvider
import javax.inject.Inject

abstract class ViewModelIBankBase<UiState : ViewState, UiEvent : ViewEvent, IBankSideEffect : SideEffect>(
    initialState: UiState,
    reducer: Reducer<UiState, UiEvent, IBankSideEffect>,
) : BaseMviViewModel<UiState, UiEvent, IBankSideEffect>(
    initialState = initialState,
    reducer = reducer
), ViewModelIBankBaseInterface {
    @Inject
    protected lateinit var localRepositoryImp: LocalRepository

    @Inject
    @IoDispatcher
    protected lateinit var iOdispatcherImp: CoroutineDispatcher

    @Inject
    protected lateinit var resourceProviderImp: ResourceProvider

    override val listSubscribedKey = mutableListOf<String>()
    override val reloadDataCache = mutableMapOf<String, ReloadDataDTO>()
    override val localRepository
        get() = localRepositoryImp
    override val ioDispatcher
        get() = iOdispatcherImp
    override val resourceProvider
        get() = resourceProviderImp
    override val viewModelScopes
        get() = viewModelScope

    override fun onCleared() {
        onViewModelCleared()
        super.onCleared()
    }
}
