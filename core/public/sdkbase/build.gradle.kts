plugins {
    `maven-publish`
    alias(libs.plugins.local.android.library)
    alias(libs.plugins.local.android.compose)
    alias(libs.plugins.local.publish.local.repo)
    alias(libs.plugins.kotlin.serialization)
}

android {
    namespace = "vn.com.bidv.ibank"
}

dependencies {
    implementation(libs.core.database)
    implementation(project(":core:public:designsystem"))
    implementation(project(":core:public:localization"))
    implementation(libs.core.common)
    implementation(libs.core.log)
    implementation(libs.core.network)
    implementation(libs.zxing.core)
    implementation(libs.gson)
    implementation(libs.androidx.navigation.compose)
    implementation(libs.androidx.core.splashscreen)
    implementation(libs.androidx.compose.material3)
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.androidx.hilt.navigation.compose)
    implementation(libs.accompanist.permissions)
}

publishLocal {
    version = "1.0.0"
}

