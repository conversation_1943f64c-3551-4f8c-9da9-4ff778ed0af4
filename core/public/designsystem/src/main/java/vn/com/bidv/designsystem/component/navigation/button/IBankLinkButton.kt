package vn.com.bidv.designsystem.component.navigation.button

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBColorScheme
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBTypography
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.utils.debounceClick

/**
 * Draft version of Link Buttons with all size.
 * @param onClick called when this button is clicked.
 * @param modifier the custom [Modifier] to be applied to this button.
 * @param isEnable controls the enabled state of this button. When `false`, this component will not
 *   respond to user input, and it will appear visually disabled and disabled to accessibility
 *   services.
 * @param text defines text content, when text is empty, only use one icon between leading or trailing icon
 * to display a icon button.
 */

@Composable
fun IBankLinkButton(
    modifier: Modifier,
    size: LinkButtonSize = LinkButtonSize.L(LocalTypography.current, LocalColorScheme.current),
    isEnable: Boolean = true,
    text: String,
    leadingIcon: ImageVector? = null,
    trailingIcon: ImageVector? = null,
    onClick: () -> Unit,
) {

    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    var textColor by remember { mutableStateOf(Color.Transparent) }
    var linkColor by remember { mutableStateOf(Color.Transparent) }

    val onlyImage = text.isEmpty()

    if (isEnable) {
        textColor = size.textColorEnable
        linkColor = if (isPressed) {
            size.linkColorPress
        } else {
            size.linkColorUnPress
        }
    } else {
        textColor = size.textColorDisable
        linkColor = size.linkColorDisable
    }

    val typographyStyle = size.typography
    val iconSize = size.iconSize
    val paddingText = size.paddingText

    Button(
        modifier = modifier
            .wrapContentWidth().testTagIBank("IBankLinkButton_$text"),
        colors = ButtonDefaults.buttonColors(Color.Transparent),
        onClick = debounceClick(onClick = { if (isEnable) onClick() }),
        interactionSource = interactionSource,
        contentPadding = PaddingValues(0.dp)
    ) {
        Column(
            modifier = Modifier
                .width(IntrinsicSize.Max)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(paddingText),
            ) {
                if (leadingIcon != null) {
                    Icon(
                        imageVector = leadingIcon,
                        contentDescription = null,
                        tint =
                        textColor,
                        modifier = Modifier
                            .size(iconSize)
                    )
                }

                Text(
                    text = text,
                    color = textColor,
                    style = typographyStyle,
                    textAlign = TextAlign.Center,
                    modifier = if (!onlyImage) {
                        Modifier
                            .padding(
                                start = IBSpacing.spacing2xs,
                                end = IBSpacing.spacing2xs
                            )
                            .wrapContentWidth()
                    } else {
                        Modifier
                    }
                )

                if (trailingIcon != null) {
                    Icon(
                        imageVector = trailingIcon,
                        contentDescription = null,
                        tint =
                        textColor,
                        modifier = Modifier
                            .size(iconSize)
                    )
                }
            }

            HorizontalDivider(
                color = linkColor,
                thickness = 1.dp,
                modifier = Modifier.fillMaxWidth()
            )

        }

    }

}

sealed class LinkButtonSize(
    val colorScheme: IBColorScheme,
    val textColorEnable: Color = colorScheme.contentBrand_01Primary,
    val textColorDisable: Color = colorScheme.contentDisablePrimary,
    val linkColorPress: Color = colorScheme.borderBrandTertiary_press,
    val linkColorUnPress: Color = Color.Transparent,
    val linkColorDisable: Color = Color.Transparent,
    val iconSize: Dp,
    val paddingText: Dp,
    val typography: TextStyle,
) {

    class SM(typo: IBTypography, colorScheme: IBColorScheme) : LinkButtonSize(
        iconSize = 20.dp,
        paddingText = IBSpacing.spacing2xs,
        typography = typo.labelLabel_l,
        colorScheme = colorScheme
    )

    class M(typo: IBTypography, colorScheme: IBColorScheme) : LinkButtonSize(
        iconSize = 20.dp,
        paddingText = IBSpacing.spacingXs,
        typography = typo.labelLabel_xl,
        colorScheme = colorScheme
    )

    class L(typo: IBTypography, colorScheme: IBColorScheme) : LinkButtonSize(
        iconSize = 24.dp,
        paddingText = IBSpacing.spacingS,
        typography = typo.labelLabel_xl,
        colorScheme = colorScheme
    )

}

@Composable
@Preview
fun LinkButtonExample() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(20.dp),
        verticalArrangement = Arrangement.spacedBy(32.dp)

    ) {

        IBankLinkButton(
            Modifier,
            LinkButtonSize.L(LocalTypography.current, LocalColorScheme.current),
            true,
            "Button CTA Button",
            ImageVector.vectorResource(id = R.drawable.information_circle),
            ImageVector.vectorResource(id = R.drawable.information_circle),
        ) { }
        IBankLinkButton(
            modifier = Modifier.fillMaxWidth(),
            LinkButtonSize.L(LocalTypography.current, LocalColorScheme.current),
            isEnable = true,
            text = "Button CTA Button",
            leadingIcon = ImageVector.vectorResource(id = R.drawable.information_circle),
            trailingIcon = ImageVector.vectorResource(id = R.drawable.information_circle)
        ) { }
    }
}

