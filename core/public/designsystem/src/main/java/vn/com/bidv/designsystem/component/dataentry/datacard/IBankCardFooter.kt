package vn.com.bidv.designsystem.component.dataentry.datacard

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.localization.R as RLocal

/**
 * A composable function that represents the footer of a card, which can display different types of content
 * based on the provided `footerType`. It supports either a "CollapseExpand" type for toggling or an "ActionButton"
 * type with two action buttons.
 *
 * @param modifier Modifier applied to the `IBankCardFooter` for custom styling.
 * @param footerType Defines the type of the footer to be displayed:
 *      - [FooterType.CollapseExpand]: Displays a toggle button for collapsing or expanding.
 *      - [FooterType.ActionButton]: Displays two action buttons (e.g., Cancel and Confirm).
 * @param buttonLeft Optional ButtonAction for the left action button (required if `footerType` is ActionButton).
 * @param buttonRight Optional ButtonAction for the right action button (required if `footerType` is ActionButton).
 **/
@Composable
fun IBankCardFooter(
    modifier: Modifier = Modifier,
    footerType: FooterType,
) {
    var isExpanded by remember { mutableStateOf(false) }
    val text =
        stringResource(id = if (isExpanded) RLocal.string.thu_gon else RLocal.string.xem_them)
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    when (footerType) {
        is FooterType.CollapseExpand -> {
            Row(
                modifier = modifier
                    .fillMaxWidth()
                    .padding(
                        top = IBSpacing.spacing2xs,
                        bottom = IBSpacing.spacingM,
                        start = IBSpacing.spacingXs,
                        end = IBSpacing.spacingXs
                    )
                    .wrapContentHeight()
                    .clickable {
                        footerType.onExpandToggle()
                        isExpanded = !isExpanded
                    },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Text(
                    text = text,
                    style = LocalTypography.current.captionCaption_m,
                    color = colorScheme.contentMainSecondary
                )
                Spacer(modifier = Modifier.width(IBSpacing.spacingXs))
                IconButton(onClick = {
                    footerType.onExpandToggle()
                    isExpanded = !isExpanded
                }, modifier = Modifier.size(16.dp)) {
                    Icon(
                        imageVector = ImageVector.vectorResource(id = if (isExpanded) R.drawable.arrow_top else R.drawable.arrow_bottom),
                        contentDescription = text,
                        tint = colorScheme.contentMainSecondary
                    )
                }
            }
        }

        is FooterType.ActionButton -> {
            Column {
                HorizontalDivider(
                    color = colorScheme.borderMainSecondary,
                    thickness = IBBorderDivider.borderDividerS
                )
                Row(
                    modifier = modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IBankNormalButton(
                        text = footerType.buttonLeft.text,
                        modifier = Modifier,
                        size = NormalButtonSize.L(typography),
                        type = NormalButtonType.TERTIARY(colorScheme),
                        leadingIcon = footerType.buttonLeft.icon,
                        onClick = footerType.buttonLeft.onClick
                    )
                    VerticalDivider(
                        modifier = Modifier
                            .height(24.dp)
                            .padding(horizontal = IBSpacing.spacingXs),
                        thickness = IBBorderDivider.borderDividerS,
                        color = colorScheme.borderMainPrimary
                    )
                    IBankNormalButton(
                        text = footerType.buttonRight.text,
                        modifier = Modifier,
                        size = NormalButtonSize.L(typography),
                        type = NormalButtonType.TERTIARY(colorScheme),
                        leadingIcon = footerType.buttonRight.icon,
                        onClick = footerType.buttonRight.onClick
                    )
                }
            }
        }
    }
}

sealed class FooterType {
    class CollapseExpand(
        val onExpandToggle: () -> Unit
    ) : FooterType()

    class ActionButton(
        val buttonLeft: ButtonAction, val buttonRight: ButtonAction
    ) : FooterType()

}

data class ButtonAction(
    val text: String,
    val icon: ImageVector? = null,
    val onClick: () -> Unit = {}
)

@Composable
@Preview(showBackground = true)
fun FooterPreview() {
    Column {
        IBankCardFooter(
            footerType = FooterType.ActionButton(
                buttonLeft = ButtonAction("Button Left",
                    icon = ImageVector.vectorResource(id = R.drawable.send),
                    onClick = {
                        BLogUtil.d("onButtonLeftClick")
                    }),
                buttonRight = ButtonAction("Button Right",
                    icon = ImageVector.vectorResource(id = R.drawable.transfer),
                    onClick = {
                        BLogUtil.d("onButtonLeftClick")
                    }),
            ),
        )
        Spacer(modifier = Modifier.height(IBSpacing.spacingS))
        IBankCardFooter(footerType = FooterType.CollapseExpand(onExpandToggle = {}))
    }

}
