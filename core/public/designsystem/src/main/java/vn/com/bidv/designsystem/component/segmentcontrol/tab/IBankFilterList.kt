package vn.com.bidv.designsystem.component.segmentcontrol.tab

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.log.BLogUtil

/**
 * A composable function that displays a horizontal, scrollable list of filter chips.
 * Each filter chip represents a tab item that can be selected, with customizable background and spacing options.
 *
 * @param modifier Modifier applied to the `IBankFilterList` for custom styling.
 * @param listTab List of `TabItem` objects, each representing a filter chip in the list.
 * @param showBackground If `true`, displays a background behind the list of filter chips.
 * @param indent If `true`, adds indentation between chips, starting from the second chip onward.
 * @param onFilterSelected Callback function invoked when a filter chip is selected, passing the selected `TabItem`.
 */
@Composable
fun IBankFilterList(
    modifier: Modifier = Modifier,
    listTab: List<TabItem>,
    showBackground: Boolean = false,
    indent: Boolean = false,
    onFilterSelected: (TabItem) -> Unit
) {
    val (selectedIndex, setSelectedIndex) = remember { mutableStateOf(-1) }
    val horizontalSPacing = if (indent) IBSpacing.spacingM else 0.dp
    val verticalSpacing = if (indent) IBSpacing.spacingXs else 0.dp

    LazyRow(
        modifier = modifier
            .wrapContentHeight()
            .padding(
                horizontal = horizontalSPacing,
                vertical = verticalSpacing
            ),
        horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)
    ) {
        itemsIndexed(listTab) { index, tabItem ->
            IBankFilter(text = tabItem.title,
                badgeNumber = tabItem.badgeText,
                isSelected = selectedIndex == index,
                onSelectedChange = {
                    setSelectedIndex(index)
                    onFilterSelected(tabItem)
                })
        }
    }
}

@Composable
@Preview(showBackground = true, backgroundColor = (0xFFFFFF))
fun FilterListPreview() {
    val tabs = listOf(
        TabItem(title = "Text"),
        TabItem(title = "VND", badgeText = "5"),
        TabItem(title = "Tab 3"),
        TabItem(title = "Tab 4", badgeText = "6XX"),
        TabItem(title = "Tab 5"),
        TabItem(title = "Tab 6"),
        TabItem(title = "Tab 7", badgeText = "99")
    )

    Column {
        Text("Ident = false")
        IBankFilterList(listTab = tabs, showBackground = false, indent = false) { item ->
            BLogUtil.d("Label seleted : $item")
        }
        Spacer(modifier = Modifier.height(IBSpacing.spacingS))
        Text("Ident = true")
        IBankFilterList(listTab = tabs, showBackground = true, indent = true) { item ->
            BLogUtil.d("Label seleted 2 : $item")
        }
    }
}
