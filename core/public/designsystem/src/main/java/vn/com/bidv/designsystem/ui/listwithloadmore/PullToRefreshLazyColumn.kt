package vn.com.bidv.designsystem.ui.listwithloadmore

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import vn.com.bidv.common.utils.unpack
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.IBankLoaderIndicators
import vn.com.bidv.localization.R

/**
 * A composable that displays a list of items with pull-to-refresh and load-more functionality.
 *
 * @param viewModel The ViewModel that provides the data and handles events related to loading more content.
 * @param loadingView Composable to show a loading indicator when the initial content is being loaded.
 * @param onRetry Callback to be executed when a retry action is triggered on error.
 * @param errorView Composable to show an error message when there is a failure loading the data.
 * @param emptyView Composable to show when there are no items to display.
 * @param onRefresh Callback to be executed when a pull-to-refresh gesture is detected.
 * @param onLoadMoreData Callback to be executed when the end of the list is reached, triggering loading more data.
 * @param contentPadding Padding values to be applied to the content of the LazyColumn.
 * @param verticalArrangement Vertical arrangement of the items within the LazyColumn.
 * @param itemView Composable to display each individual item in the list.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T> PullToRefreshLazyColumn(
    viewModel: ListContentLoaderViewModel<T>,
    loadingView: @Composable () -> Unit = { IBankLoaderIndicators() },
    onRetry: () -> Unit = { viewModel.sendEvent(ListContentLoaderReducer.ListContentLoaderViewEvent.LoadMoreData()) },
    errorView: @Composable (errorMessage: String?) -> Unit = { errorMessage ->
        IBankEmptyState(
            modifier = Modifier.fillMaxSize(),
            supportingText = errorMessage,
            textButton = stringResource(id = R.string.retry),
            onClickButton = onRetry
        )
    },
    emptyView: @Composable () -> Unit = { IBankEmptyState() },
    onRefresh: () -> Unit = { viewModel.sendEvent(ListContentLoaderReducer.ListContentLoaderViewEvent.RefreshData()) },
    onLoadMoreData: () -> Unit = { viewModel.sendEvent(ListContentLoaderReducer.ListContentLoaderViewEvent.LoadMoreData()) },
    contentPadding: PaddingValues = PaddingValues(0.dp),
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    itemView: @Composable (T) -> Unit,
) {
    val (uiState, _, _) = viewModel.unpack()
    val lazyListState = rememberLazyListState()
    val isRefreshing = remember { mutableStateOf(false) }

    // Update isRefreshing state
    LaunchedEffect(uiState) {
        isRefreshing.value =
            uiState is ListContentLoaderReducer.ListContentLoaderViewState.Loading && uiState.content.items.isEmpty()
    }

    val items = when (uiState) {
        is ListContentLoaderReducer.ListContentLoaderViewState.Loading -> uiState.content.items
        is ListContentLoaderReducer.ListContentLoaderViewState.ShowContent -> uiState.content.items
    }

    if (items.isEmpty() && uiState is ListContentLoaderReducer.ListContentLoaderViewState.Loading) {
        if (uiState.content.isLastPage) {
            emptyView()
        } else {
            loadingView()
        }
    } else {
        PullToRefreshBox(
            modifier = Modifier.fillMaxSize(),
            state = rememberPullToRefreshState(),
            isRefreshing = isRefreshing.value,
            onRefresh = {
                items.clear()
                onRefresh()
            }
        ) {
            LazyColumn(
                state = lazyListState,
                modifier = Modifier.fillMaxSize(),
                contentPadding = contentPadding,
                verticalArrangement = verticalArrangement
            ) {
                when (uiState) {
                    is ListContentLoaderReducer.ListContentLoaderViewState.Loading -> {
                        items(items.size) { index ->
                            itemView(items[index])
                        }
                        item {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(16.dp),
                                contentAlignment = Alignment.BottomCenter
                            ) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(32.dp),
                                    strokeWidth = 2.dp
                                )
                            }
                        }
                    }

                    is ListContentLoaderReducer.ListContentLoaderViewState.ShowContent -> {
                        items(items.size) { index ->
                            itemView(items[index])
                        }
                        if (uiState.content.errorMessage != null) {
                            item { errorView(uiState.content.errorMessage) }
                        }
                    }
                }
            }
        }
        HandleLoadMore(lazyListState, items, onLoadMoreData)
    }
}

@Composable
private fun <T> HandleLoadMore(
    lazyListState: LazyListState,
    items: List<T>,
    onLoadMoreData: () -> Unit
) {
    LaunchedEffect(lazyListState) {
        snapshotFlow { lazyListState.layoutInfo }
            .map { layoutInfo ->
                layoutInfo.visibleItemsInfo.lastOrNull()?.index == items.size - 1
            }
            .distinctUntilChanged()
            .collectLatest { isAtLastIndex ->
                if (isAtLastIndex) {
                    onLoadMoreData()
                }
            }
    }
}
