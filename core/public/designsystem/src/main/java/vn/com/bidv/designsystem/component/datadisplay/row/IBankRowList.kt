package vn.com.bidv.designsystem.component.datadisplay.row

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

/**
 * Sealed class to represent different types of leading elements in IBankRowList
 */
sealed class IBankRowListLeadingElement {
    data object None : IBankRowListLeadingElement()
    data class Icon(val imageVector: ImageVector) :
        IBankRowListLeadingElement()

    data class Image(val painter: Painter) :
        IBankRowListLeadingElement()
}

/**
 * Sealed class to represent different types of trailing elements in IBankRowList
 */
sealed class IBankRowListTrailingElement {
    data object None : IBankRowListTrailingElement()
    data class Icon(val imageVector: ImageVector) :
        IBankRowListTrailingElement()

    data class Content(val trailingContent: @Composable (() -> Unit?)? = null) :
        IBankRowListTrailingElement()
}

/**
 * A composable that displays an input design with customizable elements.
 *
 * @param labelText The main text label (required)
 * @param labelTextStyle The style main text label
 * @param descriptionText Optional description text below the label
 * @param descriptionTextStyle Optional style description text below the label
 * @param showTopDivider Whether to show the top divider
 * @param showBottomDivider Whether to show the bottom divider
 * @param showLeftControl Whether to show the menu/toggle icon on the left
 * @param leadingElement The type of leading element to display (None, Icon, or Image)
 * @param trailingElement Optional trailing icon to display instead of the default arrow
 * @param onClick Callback when the component is clicked
 * @param modifier Modifier for the component
 */
@Composable
fun IBankRowList(
    modifier: Modifier = Modifier,
    showTopDivider: Boolean = false,
    showBottomDivider: Boolean = false,
    showLeftControl: Boolean = false,
    leadingElement: IBankRowListLeadingElement = IBankRowListLeadingElement.None,
    labelText: String,
    labelTextStyle: TextStyle = LocalTypography.current.bodyBody_l.copy(
        color = LocalColorScheme.current.contentMainPrimary
    ),
    descriptionText: String? = null,
    descriptionTextStyle: TextStyle = LocalTypography.current.bodyBody_m.copy(
        color = LocalColorScheme.current.contentMainTertiary
    ),
    trailingElement: IBankRowListTrailingElement = IBankRowListTrailingElement.Icon(
        ImageVector.vectorResource(id = R.drawable.arrow_right_outline)
    ),
    onClick: (() -> Unit)? = null,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = ripple(
                    bounded = true,
                    color = LocalColorScheme.current.bgSolidPrimary_press,
                ),
                onClick = {
                    onClick?.invoke()
                }
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Left Control (Menu icon)
        LeftControl(leadingElement, showLeftControl)

        // Leading element (Icon, Image, or None)
        LeadingElement(leadingElement, showLeftControl)

        // Content section with dividers and text
        Column(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight(),
            verticalArrangement = Arrangement.Center
        ) {
            // Top divider
            if (showTopDivider) {
                IBankHorizontalDivider()
            }

            // Label, description, and value
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = IBSpacing.spacingXs),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Label and description
                Column(
                    modifier = Modifier
                        .weight(1f)
                ) {
                    Text(
                        text = labelText,
                        style = labelTextStyle
                    )
                    if (descriptionText != null) {
                        Text(
                            text = descriptionText,
                            style = descriptionTextStyle
                        )
                    }
                }

                // Trailing element (Icon, Image, or None)
                TrailingElement(trailingElement)
            }

            // Bottom divider
            if (showBottomDivider) {
                IBankHorizontalDivider()
            }
        }
    }
}

@Composable
private fun LeftControl(leadingElement: IBankRowListLeadingElement, showLeftControl: Boolean) {
    if (showLeftControl) {
        Box(
            modifier = Modifier
                .padding(
                    start = IBSpacing.spacingM,
                    end = if (leadingElement is IBankRowListLeadingElement.None) IBSpacing.spacingM else IBSpacing.spacingNone
                )
                .size(20.dp),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = ImageVector.vectorResource(id = R.drawable.menu_outline),
                contentDescription = "Menu",
                tint = LocalColorScheme.current.contentMainPrimary,
            )
        }
    }

}

@Composable
private fun LeadingElement(leadingElement: IBankRowListLeadingElement, showLeftControl: Boolean) {
    when (leadingElement) {
        is IBankRowListLeadingElement.Icon -> {
            Box(
                modifier = Modifier
                    .padding(
                        start = IBSpacing.spacingM,
                        end = IBSpacing.spacingL
                    )
                    .size(24.dp),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = leadingElement.imageVector,
                    contentDescription = "leading icon",
                    tint = LocalColorScheme.current.contentMainPrimary,
                )
            }
        }

        is IBankRowListLeadingElement.Image -> {
            Box(
                modifier = Modifier
                    .padding(
                        start = IBSpacing.spacingM,
                        end = IBSpacing.spacingL
                    )
                    .size(24.dp),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = leadingElement.painter,
                    contentDescription = "leading image"
                )
            }
        }

        IBankRowListLeadingElement.None -> {
            // No leading element, add some spacing if left control is not shown
            if (!showLeftControl) {
                Spacer(modifier = Modifier.width(IBSpacing.spacingM))
            }
        }
    }
}

@Composable
private fun TrailingElement(trailingElement: IBankRowListTrailingElement) {
    when (trailingElement) {
        is IBankRowListTrailingElement.Icon -> {
            Box(
                modifier = Modifier
                    .padding(start = IBSpacing.spacingXs, end = IBSpacing.spacingM)
                    .size(20.dp),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = trailingElement.imageVector,
                    contentDescription = "Trailing Icon",
                    tint = LocalColorScheme.current.contentMainPrimary,
                )
            }
        }

        is IBankRowListTrailingElement.Content -> {
            Row(
                modifier = Modifier
                    .padding(start = IBSpacing.spacingXs, end = IBSpacing.spacingM),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacing2xs)
            ) {
                trailingElement.trailingContent?.invoke()
            }
        }

        IBankRowListTrailingElement.None -> {
            // No trailing element
            Spacer(modifier = Modifier.width(IBSpacing.spacingM))
        }
    }
}

@Composable
private fun IBankHorizontalDivider() {
    HorizontalDivider(
        thickness = IBBorderDivider.borderDividerXs,
        color = LocalColorScheme.current.borderSolidPrimary.copy(alpha = 0.2f)
    )
}

@Preview(showBackground = true)
@Composable
fun IBankRowListDefaultPreview() {
    IBankRowList(
        labelText = "Account Settings",
        descriptionText = "Manage your profile and preferences",
    )
}

@Preview(showBackground = true)
@Composable
fun IBankRowListNoDescriptionPreview() {
    IBankRowList(
        labelText = "Account Settings",
        leadingElement = IBankRowListLeadingElement.Icon(
            ImageVector.vectorResource(id = R.drawable.home_outline)
        ),
    )
}

@Preview(showBackground = true)
@Composable
fun IBankRowListDividersPreview() {
    IBankRowList(
        labelText = "Account Settings",
        descriptionText = "Manage your profile and preferences",
        showTopDivider = true,
        showBottomDivider = true
    )
}

@Preview(showBackground = true)
@Composable
fun IBankRowListLeftControlPreview() {
    IBankRowList(
        labelText = "Account Settings",
        descriptionText = "Manage your profile and preferences",
        showLeftControl = true
    )
}

@Preview(showBackground = true)
@Composable
fun IBankRowListLeadingElementPreview() {
    IBankRowList(
        labelText = "Account Settings",
        descriptionText = "Manage your profile and preferences",
        leadingElement = IBankRowListLeadingElement.Icon(
            ImageVector.vectorResource(id = R.drawable.home_outline),
        )
    )
}

@Preview(showBackground = true)
@Composable
fun IBankRowListTrailingNonePreview() {
    IBankRowList(
        labelText = "Account Settings",
        descriptionText = "Manage your profile and preferences",
        trailingElement = IBankRowListTrailingElement.None
    )
}

@Preview(showBackground = true)
@Composable
fun IBankRowListTrailingElementPreview() {
    IBankRowList(
        labelText = "Account Settings",
        descriptionText = "Manage your profile and preferences",
        trailingElement = IBankRowListTrailingElement.Content {
            Row(
                modifier = Modifier
            ) {
                Text(
                    text = "Value",
                    style = LocalTypography.current.bodyBody_l.copy(
                        color = LocalColorScheme.current.contentMainPrimary
                    ),
                )
                Icon(
                    imageVector = ImageVector.vectorResource(id = R.drawable.arrow_right_outline),
                    contentDescription = "Trailing Icon",
                    tint = LocalColorScheme.current.contentMainPrimary,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    )
}