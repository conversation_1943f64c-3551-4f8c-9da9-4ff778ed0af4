package vn.com.bidv.designsystem.ui.listwithloadmorev2

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.RuleFilters
import javax.inject.Inject

class ListAutoLoadMoreReducer<T, Rule : RuleFilters?> @Inject constructor() :
    Reducer<ListAutoLoadMoreReducer.ListAutoLoadMoreViewState<T, Rule>,
            ListAutoLoadMoreReducer.ListAutoLoadMoreViewEvent<T, Rule>,
            ListAutoLoadMoreReducer.ListAutoLoadMoreViewEffect<T, Rule>> {

    @Immutable
    data class ListAutoLoadMoreViewState<T, Rule>(
        val listItems: List<ModelCheckAble<T>> = listOf(),
        val total: Int? = null,
        val isLastPage: Boolean = false,
        val isLoading: Boolean = false,
        val isError: Boolean = false,
        val isRefreshing: Boolean = false,
        val pageSize: Int = 20,
        val errorMessage: String? = null,
        val ruleFilters: Rule? = null,
        val defaultRuleFilters: Rule? = null,
        val searchKeyLocal: String? = null,
        val listAllItems: List<ModelCheckAble<T>> = listOf(),
    ) : ViewState {

        internal fun loadSuccess(
            listItem: List<T>,
            listItemFiltered: List<ModelCheckAble<T>>,
            isLastPage: Boolean,
            total: Int?,
        ) = this.copy(
            listAllItems = listAllItems + listItem.map { ModelCheckAble(it) },
            listItems = listItems + listItemFiltered,
            isLastPage = isLastPage,
            total = total,
            isLoading = false,
            isRefreshing = false,
            isError = false,
        )

        internal fun filterLocalSuccess(listItemFiltered: List<ModelCheckAble<T>>) = this.copy(
            listItems = listItemFiltered,
        )

        internal fun loadDataFail(errorMessage: String?) = this.copy(
            isLoading = false,
            isRefreshing = false,
            isError = true,
            errorMessage = errorMessage
        )

        internal fun startLoadMore() = this.copy(
            isLoading = true,
            isRefreshing = false,
            isError = false,
        )

        internal fun startRefreshing() = this.copy(
            listItems = listOf(),
            listAllItems = listOf(),
            isLastPage = false,
            isLoading = true,
            isRefreshing = true,
            errorMessage = null,
            isError = false
        )

        internal fun retry() = this.copy(
            isLoading = true,
            isRefreshing = false,
            isError = false,
        )

        internal fun updateRuleFilters(ruleFilters: Rule?) = this.copy(
            ruleFilters = ruleFilters,
            defaultRuleFilters = ruleFilters,
            listItems = listOf(),
            listAllItems = listOf(),
            isLastPage = false,
            isLoading = true,
            isRefreshing = false,
            isError = false
        )

        internal fun updateSearchKeyLocal(key: String?) = this.copy(
            searchKeyLocal = key,
            listItems = listOf()
        )

        internal fun updateItem(oldValue: T, newValue: T) = this.copy(
            listItems = listItems.map {
                if (it.data == oldValue) {
                    it.copy(data = newValue)
                } else {
                    it
                }
            },
            listAllItems = listAllItems.map {
                if (it.data == oldValue) {
                    ModelCheckAble(newValue, it.isChecked)
                } else {
                    it
                }
            }
        )

        internal fun getRuleFilter(): Rule? {
            return ruleFilters ?: defaultRuleFilters
        }
    }

    @Immutable
    sealed class ListAutoLoadMoreViewEvent<out T, out Rule> : ViewEvent {
        data class GetDataSuccess<T>(
            val items: List<T>,
            val listItemFiltered: List<ModelCheckAble<T>>,
            val total: Int?,
            val isLastPage: Boolean = false,
        ) : ListAutoLoadMoreViewEvent<T, Nothing>()

        data class FilterDataLocalSuccess<T>(
            val listItemFiltered: List<ModelCheckAble<T>>,
        ) : ListAutoLoadMoreViewEvent<T, Nothing>()

        data class GetDataFail(val errorMessage: String?) :
            ListAutoLoadMoreViewEvent<Nothing, Nothing>()

        data object LoadMoreData : ListAutoLoadMoreViewEvent<Nothing, Nothing>()
        data object RefreshData : ListAutoLoadMoreViewEvent<Nothing, Nothing>()
        data object Retry : ListAutoLoadMoreViewEvent<Nothing, Nothing>()
        data class SelectAll(val isSelectAll: Boolean) :
            ListAutoLoadMoreViewEvent<Nothing, Nothing>()

        data class SelectItem<T>(val item: ModelCheckAble<T>) :
            ListAutoLoadMoreViewEvent<T, Nothing>()

        data class SelectListItem<T>(val isSelect: Boolean, val items: List<ModelCheckAble<T>>) :
            ListAutoLoadMoreViewEvent<T, Nothing>()

        data class UpdateRuleFilters<Rule>(val ruleFilters: Rule?) :
            ListAutoLoadMoreViewEvent<Nothing, Rule>()

        data class UpdateSearchKeyLocal<T>(val key: String?) :
            ListAutoLoadMoreViewEvent<T, Nothing>()

        data class UpdateItem<T>(val oldValue: T, val newValue: T) :
            ListAutoLoadMoreViewEvent<T, Nothing>()

        data class UpdateDefaultRuleFilters<Rule>(val defaultRuleFilters: Rule?) :
            ListAutoLoadMoreViewEvent<Nothing, Rule>()
    }

    @Immutable
    sealed class ListAutoLoadMoreViewEffect<out T, out Rule> : SideEffect {
        data class GetData<Rule>(
            val pageIndex: Int,
            val pageSize: Int,
            val rule: Rule?,
            val searchKey: String?,
        ) :
            ListAutoLoadMoreViewEffect<Nothing, Rule>()

        data class FilterDataLocal<T>(
            val listItem: List<ModelCheckAble<T>>,
            val searchKey: String?,
        ) :
            ListAutoLoadMoreViewEffect<T, Nothing>()
    }

    override fun reduce(
        previousState: ListAutoLoadMoreViewState<T, Rule>,
        event: ListAutoLoadMoreViewEvent<T, Rule>,
    ): Pair<ListAutoLoadMoreViewState<T, Rule>, ListAutoLoadMoreViewEffect<T, Rule>?> {
        return when (event) {
            is ListAutoLoadMoreViewEvent.GetDataSuccess -> {
                previousState.loadSuccess(
                    listItem = event.items,
                    listItemFiltered = event.listItemFiltered,
                    isLastPage = event.isLastPage,
                    total = event.total
                ) to null
            }

            is ListAutoLoadMoreViewEvent.FilterDataLocalSuccess -> {
                previousState.filterLocalSuccess(
                    listItemFiltered = event.listItemFiltered,
                ) to null
            }

            is ListAutoLoadMoreViewEvent.GetDataFail -> {
                previousState.loadDataFail(event.errorMessage) to null
            }

            is ListAutoLoadMoreViewEvent.LoadMoreData -> {
                if (previousState.isLastPage || previousState.isLoading || previousState.isError) {
                    previousState to null
                } else {
                    previousState.startLoadMore() to ListAutoLoadMoreViewEffect.GetData(
                        pageIndex = previousState.listAllItems.size / previousState.pageSize + 1,
                        pageSize = previousState.pageSize,
                        rule = previousState.getRuleFilter(),
                        searchKey = previousState.searchKeyLocal
                    )
                }
            }

            is ListAutoLoadMoreViewEvent.RefreshData -> {
                if (previousState.isRefreshing) {
                    previousState to null
                } else {
                    previousState.startRefreshing() to ListAutoLoadMoreViewEffect.GetData(
                        pageIndex = 1,
                        pageSize = previousState.pageSize,
                        rule = previousState.getRuleFilter(),
                        searchKey = previousState.searchKeyLocal
                    )
                }
            }

            is ListAutoLoadMoreViewEvent.Retry -> {
                if (previousState.isLoading) {
                    previousState to null
                } else {
                    previousState.retry() to ListAutoLoadMoreViewEffect.GetData(
                        pageIndex = previousState.listAllItems.size / previousState.pageSize + 1,
                        pageSize = previousState.pageSize,
                        rule = previousState.getRuleFilter(),
                        searchKey = previousState.searchKeyLocal
                    )
                }
            }

            is ListAutoLoadMoreViewEvent.SelectAll -> {
                val newsListAllItems = previousState.listAllItems.map {
                    it.copy(isChecked = event.isSelectAll)
                }
                val newListItems = previousState.listItems.map {
                    it.copy(isChecked = event.isSelectAll)
                }
                previousState.copy(
                    listAllItems = newsListAllItems,
                    listItems = newListItems
                ) to null
            }

            is ListAutoLoadMoreViewEvent.SelectItem -> {
                val newsListAllItems = previousState.listAllItems.map {
                    if (it == event.item) {
                        it.copy(isChecked = !it.isChecked)
                    } else {
                        it
                    }
                }
                val newListItems = previousState.listItems.map {
                    if (it == event.item) {
                        it.copy(isChecked = !it.isChecked)
                    } else {
                        it
                    }
                }
                previousState.copy(
                    listItems = newListItems,
                    listAllItems = newsListAllItems
                ) to null
            }

            is ListAutoLoadMoreViewEvent.SelectListItem -> {
                val newListItems = previousState.listItems.map {
                    if (event.items.contains(it)) {
                        it.copy(isChecked = event.isSelect)
                    } else {
                        it
                    }
                }
                val newsListAllItems = previousState.listAllItems.map {
                    if (event.items.contains(it)) {
                        it.copy(isChecked = event.isSelect)
                    } else {
                        it
                    }
                }
                previousState.copy(
                    listAllItems = newsListAllItems,
                    listItems = newListItems
                ) to null
            }

            is ListAutoLoadMoreViewEvent.UpdateRuleFilters -> {
                if (previousState.ruleFilters == event.ruleFilters) {
                    previousState to null
                } else {
                    previousState.updateRuleFilters(event.ruleFilters) to ListAutoLoadMoreViewEffect.GetData(
                        pageIndex = 1,
                        pageSize = previousState.pageSize,
                        rule = event.ruleFilters,
                        searchKey = previousState.searchKeyLocal
                    )
                }
            }

            is ListAutoLoadMoreViewEvent.UpdateSearchKeyLocal -> {
                previousState.updateSearchKeyLocal(event.key) to ListAutoLoadMoreViewEffect.FilterDataLocal(
                    previousState.listAllItems,
                    event.key
                )
            }

            is ListAutoLoadMoreViewEvent.UpdateItem -> {
                previousState.updateItem(event.oldValue, event.newValue) to null
            }

            is ListAutoLoadMoreViewEvent.UpdateDefaultRuleFilters -> {
                if (previousState.defaultRuleFilters == event.defaultRuleFilters) {
                    previousState to null
                } else {
                    previousState.copy(defaultRuleFilters = event.defaultRuleFilters) to null
                }
            }
        }
    }
}

