package vn.com.bidv.designsystem.component.datepicker.utils

import vn.com.bidv.designsystem.component.datepicker.model.BorderType
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

fun getMonthName(month: Int): String {
    return Calendar.getInstance().apply { set(Calendar.MONTH, month) }
        .getDisplayName(Calendar.MONTH, Calendar.LONG, Locale.getDefault())
        ?.replaceFirstChar { it.uppercase() }
        ?: ""
}

fun getDaysInMonth(month: Int, year: Int): Int {
    val calendar = Calendar.getInstance()
    calendar.set(year, month, 1)
    return calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
}

fun getBorderType(day: Date, startDate: Date?, endDate: Date?, firstDayOfWeek: Int): BorderType {
    val dayCalendar = Calendar.getInstance().apply {
        this.firstDayOfWeek = firstDayOfWeek
        this.time = day
        set(Calendar.HOUR_OF_DAY, 0)
        set(Calendar.MINUTE, 0)
        set(Calendar.SECOND, 0)
        set(Calendar.MILLISECOND, 0)
    }
    val dayOfWeek = dayCalendar.get(Calendar.DAY_OF_WEEK)
    if (dayOfWeek == firstDayOfWeek) {
        return BorderType.START
    }
    if (dayOfWeek == (firstDayOfWeek + 6) % 7) {
        return BorderType.END
    }
    if (startDate != null && endDate != null && startDate.equalDay(endDate)) {
        return BorderType.FULL
    }
    if (startDate != null && day.equalDay(startDate)) {
        return BorderType.START
    }
    if (endDate != null && day.equalDay(endDate)) {
        return BorderType.END
    }
    return BorderType.NONE
}

fun Date.equalDay(other: Date?): Boolean {
    if (other == null) {
        return false
    }
    val from = Calendar.getInstance(Locale.getDefault())
    from.time = this
    val to = Calendar.getInstance(Locale.getDefault())
    to.time = other
    val equalYear = from.get(Calendar.YEAR) == to.get(Calendar.YEAR)
    val equalDayOfYear = from.get(Calendar.DAY_OF_YEAR) == to.get(Calendar.DAY_OF_YEAR)
    return equalYear && equalDayOfYear
}

// Helper functions for date calculations
fun getFirstDayOfMonth(month: Int, year: Int, firstDayOfWeek: Int): Int {
    val calendar = Calendar.getInstance()
    calendar.set(year, month, 1)
    val firstDay = calendar.get(Calendar.DAY_OF_WEEK)
    return if (firstDayOfWeek == Calendar.MONDAY) {
        (firstDay - Calendar.MONDAY + 7) % 7
    } else {
        (firstDay - Calendar.SUNDAY + 7) % 7
    }
}

fun getDate(year: Int, month: Int, day: Int): Date {
    return Calendar.getInstance().apply {
        set(Calendar.YEAR, year)
        set(Calendar.MONTH, month)
        set(Calendar.DAY_OF_MONTH, day)
        set(Calendar.HOUR_OF_DAY, 0)
        set(Calendar.MINUTE, 0)
        set(Calendar.SECOND, 0)
        set(Calendar.MILLISECOND, 0)
    }.time
}

fun getDayNames(daysOfWeek: List<String>, firstDayOfWeek: Int): List<String> {
    return if (firstDayOfWeek == Calendar.MONDAY) {
        daysOfWeek.drop(1) + daysOfWeek.take(1) // Shift for Monday start
    } else {
        daysOfWeek.toList()
    }
}

fun getStartDate(date: Date): Date {
    val calendar = Calendar.getInstance().apply {
        time = date
        set(Calendar.HOUR_OF_DAY, 0)
        set(Calendar.MINUTE, 0)
        set(Calendar.SECOND, 0)
        set(Calendar.MILLISECOND, 0)
    }
    return calendar.time;
}

fun getEndDate(date: Date): Date {
    val calendar = Calendar.getInstance().apply {
        time = date
        set(Calendar.HOUR_OF_DAY, 23)
        set(Calendar.MINUTE, 59)
        set(Calendar.SECOND, 59)
        set(Calendar.MILLISECOND, 999)
    }
    return calendar.time;
}

object DateTimeFormats {
    const val DATE_FORMAT = "dd/MM/yyyy"
}

internal fun Date.toFormattedString(format: String): String {
    return SimpleDateFormat(format, Locale.getDefault()).format(this)
}

fun getMidNightCalendar(): Calendar {
    return Calendar.getInstance().apply {
        set(Calendar.HOUR_OF_DAY, 0)
        set(Calendar.MINUTE, 0)
        set(Calendar.SECOND, 0)
        set(Calendar.MILLISECOND, 0)
    }
}