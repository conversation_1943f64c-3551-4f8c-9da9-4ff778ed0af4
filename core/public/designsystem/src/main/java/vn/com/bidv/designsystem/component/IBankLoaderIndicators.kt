package vn.com.bidv.designsystem.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieClipSpec
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import vn.com.bidv.designsystem.R

/**
 * A composable function that displays a loading indicator.
 *
 * @param modifier Modifier for adjusting the layout or other visual properties of the loading indicator.
 * @param loadingSize The size of the loading indicator. Default is `LoadingSize.MEDIUM`.
 * @param isParentFullSize A boolean flag indicating whether the loading indicator should fill its parent's size. Default is true.
 * @param backgroundColor The background color of the loading indicator. Default is semi-transparent black.
 *
 * This function uses the Lottie library to display an animated loading indicator.
 * The animation is loaded from a raw resource file and is looped indefinitely.
 * The size of the loading indicator can be adjusted using the `loadingSize` parameter.
 * The loading indicator can be made to fill its parent's size by setting `isParentFullSize` to true.
 * The background color of the loading indicator can be customized using the `backgroundColor` parameter.
 */
@Composable
fun IBankLoaderIndicators(
    modifier: Modifier = Modifier,
    loadingSize: LoadingSize = LoadingSize.MEDIUM,
    isParentFullSize: Boolean = true,
    backgroundColor: Color = Color.Black.copy(alpha = 0.3f)
) {
    val composition by rememberLottieComposition(LottieCompositionSpec.RawRes(R.raw.bidv_loading))
    val progress by animateLottieCompositionAsState(
        composition = composition,
        reverseOnRepeat = true,
        iterations = LottieConstants.IterateForever,
        clipSpec = LottieClipSpec.Progress(0f, 0.5f)
    )
    var finalModifier = modifier.background(backgroundColor)
    if (isParentFullSize) {
        finalModifier = finalModifier.fillMaxSize()
    }

    Box(
        modifier = finalModifier,
        contentAlignment = Alignment.Center
    ) {
        LottieAnimation(
            composition = composition,
            progress = { progress },
            modifier = Modifier.width(loadingSize.size.dp).height(loadingSize.size.dp)
        )
    }
}

enum class LoadingSize(val size: Int) {
    TINY(20),
    SMALL(40),
    MEDIUM(60),
    LARGE(80)
}

object IBankLoaderIndicatorsImpl {

    @Composable
    fun Default() {
        IBankLoaderIndicators(
            modifier = Modifier.fillMaxWidth(),
            loadingSize = LoadingSize.SMALL,
            isParentFullSize = false,
            backgroundColor = Color.Transparent,
        )
    }

}
