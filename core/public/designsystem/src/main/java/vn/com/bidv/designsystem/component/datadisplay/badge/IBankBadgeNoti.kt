package vn.com.bidv.designsystem.component.datadisplay.badge

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

enum class NotiSize {
    DOT, SMALL, MEDIUM
}

enum class NotiColor {
    RED, GREY, GREEN
}

@Composable
fun IBankNotiBadge(
    modifier: Modifier,
    title: String,
    badgeSize: NotiSize,
    badgeColor: NotiColor
) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current

    val titleStyle = when (badgeSize) {
        NotiSize.DOT -> typography.captionCaption_s
        NotiSize.SMALL -> typography.captionCaption_s
        NotiSize.MEDIUM -> typography.captionCaption_m
    }
    val (minWidth, minHeight) = when (badgeSize) {
        NotiSize.DOT -> 6.dp to 6.dp
        NotiSize.SMALL -> 16.dp to 16.dp
        NotiSize.MEDIUM -> 20.dp to 20.dp
    }
    val (bgColor, titleColor) = when (badgeColor) {
        NotiColor.RED -> colorScheme.bgNegativePrimary to colorScheme.contentOn_specialPrimary
        NotiColor.GREY -> colorScheme.bgMainPrimary to colorScheme.contentMainPrimary
        NotiColor.GREEN -> colorScheme.bgBrand_01Primary to colorScheme.contentOn_specialPrimary
    }

    Box(
        modifier = modifier
            .wrapContentSize()
            .widthIn(minWidth)
            .heightIn(minHeight)
            .background(bgColor, shape = RoundedCornerShape(IBCornerRadius.cornerRadiusRound)),
        contentAlignment = Alignment.Center
    ) {
        if (badgeSize != NotiSize.DOT) {
            Text(
                text = title,
                color = titleColor,
                style = titleStyle,
                modifier = Modifier.padding(
                    top = IBSpacing.spacing3xs,
                    bottom = IBSpacing.spacing3xs,
                    start = IBSpacing.spacing2xs,
                    end = IBSpacing.spacing2xs
                )
            )
        }
    }
}

@Composable
@Preview
fun BadgePreview() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly,
    ) {
        val badgeColors = listOf(NotiColor.RED, NotiColor.GREY, NotiColor.GREEN)
        badgeColors.forEach { color ->
            Column(
                modifier = Modifier.wrapContentWidth(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                NotiSize.entries.forEach { size ->
                    Spacer(modifier = Modifier.height(IBSpacing.spacingS))
                    IBankNotiBadge(
                        modifier = Modifier,
                        title = if (color == NotiColor.RED) "200" else "2",
                        badgeSize = size,
                        badgeColor = color
                    )
                    Spacer(modifier = Modifier.height(IBSpacing.spacingS))
                }
            }
        }
    }
}
