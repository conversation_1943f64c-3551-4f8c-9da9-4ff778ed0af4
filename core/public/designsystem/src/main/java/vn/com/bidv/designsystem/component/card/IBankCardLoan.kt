package vn.com.bidv.designsystem.component.card

import IBGradient
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBSpacing.spacingM
import vn.com.bidv.designsystem.theme.IBSpacing.spacingS
import vn.com.bidv.designsystem.theme.IBSpacing.spacingXs
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun IBankCardLoan(
    modifier: Modifier = Modifier,
    icon: (@Composable () -> Unit)? = null,
    title: TextField? = null,
    subTitle: TextField? = null,
    bgBrush: Brush = IBGradient.cardSecondary,
    invisibleFlower: Boolean = false,
    onClick: () -> Unit = {}
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current


    Box(modifier = modifier.testTagIBank("IBankCardLoan_$title")
        .clip(RoundedCornerShape(IBCornerRadius.cornerRadiusL))
        .wrapContentHeight()
        .background(brush = bgBrush)
        .clickable { onClick() }) {
        Icon(
            imageVector = ImageVector.vectorResource(id = R.drawable.bidv_flower_background_inquiry_payment),
            modifier = Modifier
                .wrapContentHeight()
                .align(Alignment.CenterEnd)
                .alpha(if (invisibleFlower) 0f else 1f),
            contentDescription = "Account Icon",
            tint = Color.Unspecified
        )

        Column(
            modifier = Modifier
                .align(Alignment.CenterStart)
                .fillMaxWidth()
                .padding(vertical = spacingS, horizontal = spacingM),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically
            ) {
                if (icon != null) {
                    Box(modifier = Modifier.size(32.dp)) {
                        icon()
                    }
                    Spacer(modifier = Modifier.width(spacingXs))
                }
                Column(verticalArrangement = Arrangement.spacedBy(IBSpacing.spacing3xs)) {
                    Text(
                        color = title?.color ?: colorSchema.contentOn_specialSecondary,
                        text = title?.value ?: "",
                        style = typography.bodyBody_m
                    )

                    Text(
                        color = subTitle?.color ?: colorSchema.contentOn_specialPrimary,
                        text = subTitle?.value ?: "",
                        style = typography.titleTitle_m
                    )

                }
            }
        }
    }
}

@Composable
fun AccountIcon(iconId: Int, bgColor: Color = LocalColorScheme.current.bgMainTertiary) {
    val colorScheme = LocalColorScheme.current
    Box {
        Surface(
            color = bgColor,
            modifier = Modifier
                .size(32.dp)
                .align(Alignment.Center),
            shape = CircleShape,
            border = BorderStroke(width = 1.dp, color = colorScheme.borderMainPrimary)
        ) {
            Image(
                modifier = Modifier.padding(6.dp),
                painter = painterResource(iconId),
                contentDescription = "",
            )
        }
    }
}

@Preview(showBackground = false)
@Composable
fun ShowPreviewIBankCardLoan() {
    val colorScheme = LocalColorScheme.current
    Column(
        modifier = Modifier.fillMaxWidth(), verticalArrangement = Arrangement.spacedBy(spacingM)
    ) {
        IBankCardLoan(
            title = TextField(value = "Chọn tài khoản chuyển tiền"),
            subTitle = TextField(value = "***********"),
            icon = {
                AccountIcon(iconId = R.drawable.tra_no)
            },
        ) {

        }

        val brushWhite = Brush.linearGradient(
            colors = listOf(colorScheme.bgMainTertiary, colorScheme.bgMainTertiary),
            start = Offset(0f, Float.POSITIVE_INFINITY),
            end = Offset(0f, Float.POSITIVE_INFINITY)
        )

        IBankCardLoan(
            title = TextField(
                value = "Chọn tài khoản chuyển tiền", color = colorScheme.contentMainTertiary
            ),
            subTitle = TextField(
                value = "***********", color = colorScheme.contentMainPrimary
            ),
            bgBrush = brushWhite,
            invisibleFlower = true,
            icon = {
                AccountIcon(iconId = R.drawable.tra_no, bgColor = colorScheme.bgMainPrimary)
            },
        )
    }
}