package vn.com.bidv.designsystem.component.dataentry.datacard

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

/**
 * A composable function for displaying a card header with a title, optional subtitle, and icon.
 * Includes an optional divider for separation.
 *
 * @param modifier Modifier applied to the card header.
 * @param title The main title text.
 * @param subtitle Optional subtitle text.
 * @param showDivider Whether to show a divider below the header. Defaults to `true`.
 * @param icon Optional icon displayed beside the title.
 */
@Composable
fun IBankCardHeader(
    modifier: Modifier = Modifier,
    title: String,
    subtitle: String? = null,
    showDivider: Boolean = true,
    icon: ImageVector? = null,
    onClick: () -> Unit = {}
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    Column(verticalArrangement = Arrangement.Center) {
        Row(
            modifier = modifier
                .fillMaxWidth()
                .padding(
                    top = IBSpacing.spacingS,
                    end = IBSpacing.spacingXs,
                    bottom = IBSpacing.spacingS,
                    start = IBSpacing.spacingM
                ), verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(IBSpacing.spacing2xs)
            ) {
                Text(
                    text = title,
                    style = typography.titleTitle_m,
                    color = colorScheme.contentMainPrimary
                )
                subtitle?.let {
                    Text(
                        text = it,
                        style = typography.captionCaption_m,
                        color = colorScheme.contentMainSecondary
                    )
                }
            }
            if (icon != null) {
                IBankNormalButton(
                    text = "",
                    modifier = Modifier,
                    size = NormalButtonSize.SM(typography),
                    type = NormalButtonType.TERTIARY(colorScheme),
                    trailingIcon = icon
                ) {
                    onClick.invoke()
                }
            }
        }
        if (showDivider) {
            HorizontalDivider(
                color = colorScheme.borderMainSecondary, thickness = IBBorderDivider.borderDividerS
            )
        }
    }
}

@Composable
@Preview(showBackground = true)
fun CardHeaderPreview() {
    IBankCardHeader(
        title = "Title",
        subtitle = "Supporting text",
        showDivider = true,
        icon = ImageVector.vectorResource(id = R.drawable.information_circle),
    )
}
