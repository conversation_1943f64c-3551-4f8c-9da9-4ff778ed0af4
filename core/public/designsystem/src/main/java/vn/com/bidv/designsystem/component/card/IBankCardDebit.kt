package vn.com.bidv.designsystem.component.card

import IBGradient
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CornerSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography

@Composable
fun IBankCardDebit(
    title: String,
    amount: String,
    subTitle: String
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    var iconHeight by remember { mutableStateOf(0) }
    Box(
        modifier = Modifier
            .wrapContentHeight()
            .fillMaxWidth()
            .clip(
                shape = RoundedCornerShape(
                    topStart = CornerSize(IBCornerRadius.cornerRadiusL),
                    topEnd = CornerSize(IBCornerRadius.cornerRadiusL),
                    bottomStart = CornerSize(0),
                    bottomEnd = CornerSize(0),
                )
            )
            .background(
                brush = IBGradient.cardPrimary,
            )
    ) {
        Icon(
            imageVector = ImageVector.vectorResource(id = R.drawable.bidv_flower_background_card_deposit),
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .onGloballyPositioned { coordinates ->
                    iconHeight = coordinates.size.height
                }
                .alpha(0.3f),
            contentDescription = "Account Icon",
            tint = Color.Unspecified
        )

        Column(
            modifier = Modifier
                .height(with(LocalDensity.current) { iconHeight.toDp() })
                .padding(IBSpacing.spacingM),
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)
        ) {
            Text(
                modifier = Modifier.testTagIBank("IBankCardDebit_$title"),
                text = title,
                color = colorScheme.contentOn_specialSecondary,
                style = typography.bodyBody_m,
            )
            Text(
                text = amount,
                color = colorScheme.contentOn_specialPrimary,
                style = typography.titleTitle_l
            )
            Text(
                text = subTitle,
                color = colorScheme.contentOn_specialSecondary,
                style = typography.bodyBody_m,
            )
        }
    }
}

@Composable
fun IBankCardInterest(
    startIcon: Int = R.drawable.lai_suat_outline,
    title: String,
    subTitle: String,
    onClick: () -> Unit = {}
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    Box(
        modifier = Modifier
            .wrapContentHeight()
            .fillMaxWidth()
            .clip(
                shape = RoundedCornerShape(
                    topStart = CornerSize(0),
                    topEnd = CornerSize(0),
                    bottomStart = CornerSize(IBCornerRadius.cornerRadiusL),
                    bottomEnd = CornerSize(IBCornerRadius.cornerRadiusL),
                )
            )
            .background(color = colorScheme.bgMainTertiary)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = IBSpacing.spacingS, horizontal = IBSpacing.spacingM),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                modifier = Modifier
                    .weight(1f),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    modifier = Modifier.size(24.dp),
                    painter = painterResource(id = startIcon),
                    contentDescription = null
                )
                Spacer(Modifier.width(IBSpacing.spacing2xs))
                Text(
                    text = title,
                    color = colorScheme.contentMainTertiary,
                    style = typography.bodyBody_m,
                )
            }
            Row(
                modifier = Modifier
                    .testTagIBank("IBankCardInterest_icon_rateDetail")
                    .weight(1f)
                    .clickable { onClick.invoke() },
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = subTitle,
                    color = colorScheme.contentMainPrimary,
                    style = typography.titleTitle_m,
                )
                Spacer(Modifier.width(IBSpacing.spacing2xs))
                Image(
                    painter = painterResource(id = R.drawable.arrow_right_outline),
                    contentDescription = null
                )
            }
        }
    }
}

@Composable
@Preview(showBackground = false)
fun IBankCardDebitPreview() {
    Column(modifier = Modifier.wrapContentSize()) {
        IBankCardDebit(
            title = "Tổng số tiền đề nghị giải ngân",
            amount = "1,500,810,000 VND",
            subTitle = "Một tỷ năm trăm triệu tám trăm mười nghìn đồng"
        )
        IBankCardInterest(title = "Lãi suất cho vay", subTitle = "7.5%/năm")
    }
}