package vn.com.bidv.designsystem.component

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
fun MainMenuItem(label: String, onSelect: () -> Unit) {
	Button(
		onClick = onSelect,
		modifier = Modifier
			.height(48.dp)
			.fillMaxWidth()
	) {
		Text(label)
	}
}