package vn.com.bidv.designsystem.ui.listwithloadmore

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.IBankLoaderIndicators
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.localization.R

@Composable
fun <T> ListContentLoaderScreen(
    navController: NavHostController,
    viewModel: ListContentLoaderViewModel<T>,
    topAppBarType: TopAppBarType = TopAppBarType.Title,
    topAppBarConfig: TopAppBarConfig = TopAppBarConfig(),
    loadingView: @Composable () -> Unit = { IBankLoaderIndicators() },
    onRetry: () -> Unit = { viewModel.sendEvent(ListContentLoaderReducer.ListContentLoaderViewEvent.LoadMoreData()) },
    errorView: @Composable (errorMessage: String?) -> Unit  = { errorMessage ->
        IBankEmptyState(
            modifier = Modifier.fillMaxSize(),
            supportingText = errorMessage,
            textButton = stringResource(id = R.string.retry),
            onClickButton = onRetry
        )
    },
    emptyView: @Composable () -> Unit = { IBankEmptyState() },
    itemView: @Composable (T) -> Unit,
) {
    BaseScreen(
        navController = navController,
        viewModel = viewModel,
        topAppBarType = topAppBarType,
        topAppBarConfig = topAppBarConfig,
        renderContent = { _, onEvent ->
            PullToRefreshLazyColumn(
                viewModel = viewModel,
                loadingView = loadingView,
                onRetry = onRetry,
                errorView = errorView,
                emptyView = emptyView,
                itemView = itemView,
                onRefresh = {
                    onEvent(ListContentLoaderReducer.ListContentLoaderViewEvent.RefreshData())
                },
                onLoadMoreData = {
                    onEvent(ListContentLoaderReducer.ListContentLoaderViewEvent.LoadMoreData())
                }
            )
        }
    )
}
