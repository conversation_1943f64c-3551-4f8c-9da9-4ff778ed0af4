package vn.com.bidv.designsystem.component

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.window.DialogWindowProvider

@Composable
fun IBankLoaderIndicatorsDialog(
    modifier: Modifier = Modifier,
    loadingSize: LoadingSize = LoadingSize.MEDIUM,
    backgroundColor: Color = Color.Black.copy(alpha = 0.0f),
    onDismissRequest: () -> Unit
) {
    val dialogProperties = DialogProperties(
        dismissOnBackPress = false,
        dismissOnClickOutside = false,
        usePlatformDefaultWidth = false
    )

    Dialog(onDismissRequest = onDismissRequest, properties = dialogProperties) {
        // Make the dialog window background transparent
        val dialogWindowProvider = LocalView.current.parent as DialogWindowProvider
        dialogWindowProvider.window.setDimAmount(0f)
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            IBankLoaderIndicators(
                loadingSize = loadingSize,
                isParentFullSize = true,
                backgroundColor = backgroundColor
            )
        }
    }


}