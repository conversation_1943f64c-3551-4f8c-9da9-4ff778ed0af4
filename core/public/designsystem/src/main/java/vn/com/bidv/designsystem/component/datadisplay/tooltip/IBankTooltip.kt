package vn.com.bidv.designsystem.component.datadisplay.tooltip

import android.view.View
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.layout.LayoutCoordinates
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntRect
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.center
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupPositionProvider
import androidx.compose.ui.window.PopupProperties
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.extension.noRippleClickable

/**
 * Draft: Displays a tooltip popup with specified content, optional sub-content, and configuration options.
 *
 * @param modifier Modifier to be applied to the icon or view triggering the tooltip.
 * @param content The main text content displayed in the tooltip.
 * @param isSupportingText Flag indicating if `subContent` should be styled as supporting text.
 * @param subContent Additional text content, displayed below the main content if provided.
 * @param showCloseButton Determines whether a close button is shown in the tooltip.
 * @param showIconView Composable function to display the icon or view that triggers the tooltip popup when clicked.
 */
@Composable
fun IBankTooltip(
    modifier: Modifier = Modifier,
    content: String? = null,
    isSupportingText: Boolean = false,
    subContent: @Composable () -> Unit = {},
    showCloseButton: Boolean = false,
    tooltipPosition: TooltipPosition = TooltipPosition.AUTO,
    showIconView: @Composable (modifier: Modifier) -> Unit
) {
    var isShowTooltip by remember { mutableStateOf(false) }
    var showPosition by remember { mutableStateOf(Placement()) }
    val view = LocalView.current.rootView

    if (isShowTooltip) {
        TooltipPopup(
            onDismissRequest = { isShowTooltip = isShowTooltip.not() },
            placement = showPosition,
            content = content,
            isSupportingText = isSupportingText,
            showCloseButton = showCloseButton,
            subContent = subContent,
        )
    }
    val iconModifier = modifier
        .noRippleClickable { isShowTooltip = isShowTooltip.not() }
        .onGloballyPositioned { coordinates ->
            showPosition = calculateShowPosition(view, coordinates, tooltipPosition)
        }
    showIconView(iconModifier)
}

@Composable
fun TooltipPopup(
    placement: Placement,
    arrowHeight: Dp = 7.dp,
    onDismissRequest: (() -> Unit)? = null,
    content: String? = null,
    isSupportingText: Boolean,
    showCloseButton: Boolean,
    subContent: @Composable () -> Unit = {},
) {
    val colorScheme = LocalColorScheme.current
    var alignment = Alignment.TopCenter
    var offset = placement.offset
    val horizontalPadding = IBSpacing.spacingS

    val horizontalPaddingInPx = with(LocalDensity.current) {
        horizontalPadding.toPx()
    }

    var arrowPositionX by remember { mutableFloatStateOf(placement.centerPositionX) }

    when (placement.alignment) {
        TooltipAlignment.TOP_CENTER -> {
            alignment = Alignment.TopCenter
            offset = offset.copy(
                y = placement.offset.y
            )
        }

        TooltipAlignment.BOTTOM_CENTER -> {
            alignment = Alignment.BottomCenter
            offset = offset.copy(
                y = placement.offset.y
            )
        }
    }

    val popupPositionProvider = remember(alignment, offset) {
        TooltipAlignmentOffsetPositionProvider(
            alignment = alignment,
            offset = offset,
            horizontalPaddingInPx = horizontalPaddingInPx,
            centerPositionX = placement.centerPositionX,
        ) { position ->
            arrowPositionX = position
        }
    }

    Popup(
        popupPositionProvider = popupPositionProvider,
        onDismissRequest = onDismissRequest,
        properties = PopupProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = !showCloseButton
        ),
    ) {
        BubbleLayout(
            modifier = Modifier
                .padding(horizontal = horizontalPadding, vertical = arrowHeight)
                .background(
                    color = colorScheme.bgSolidPrimary,
                    shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL),
                ),
            alignment = placement.alignment,
            arrowHeight = arrowHeight,
            arrowPositionX = arrowPositionX,
            onClose = { onDismissRequest?.invoke() },
            content = content,
            showCloseButton = showCloseButton,
            isSupportingText = isSupportingText,
            subContent = subContent,
        )
    }
}

internal class TooltipAlignmentOffsetPositionProvider(
    val alignment: Alignment,
    val offset: IntOffset,
    val centerPositionX: Float,
    val horizontalPaddingInPx: Float,
    private val onArrowPositionX: (Float) -> Unit,
) : PopupPositionProvider {

    override fun calculatePosition(
        anchorBounds: IntRect,
        windowSize: IntSize,
        layoutDirection: LayoutDirection,
        popupContentSize: IntSize
    ): IntOffset {
        var popupPosition = IntOffset(0, 0)

        val parentAlignmentPoint = alignment.align(
            IntSize.Zero, IntSize(anchorBounds.width, anchorBounds.height), layoutDirection
        )

        val relativePopupPos = alignment.align(
            IntSize.Zero, IntSize(popupContentSize.width, popupContentSize.height), layoutDirection
        )

        popupPosition += IntOffset(anchorBounds.left, anchorBounds.top)
        popupPosition += parentAlignmentPoint
        popupPosition -= IntOffset(relativePopupPos.x, relativePopupPos.y)

        val resolvedOffset = IntOffset(
            offset.x * (if (layoutDirection == LayoutDirection.Ltr) 1 else -1), offset.y
        )

        popupPosition += resolvedOffset

        val leftSpace = centerPositionX - horizontalPaddingInPx
        val rightSpace = windowSize.width - centerPositionX - horizontalPaddingInPx

        val tooltipWidth = popupContentSize.width
        val halfPopupContentSize = popupContentSize.center.x

        val fullPadding = horizontalPaddingInPx * 2

        val maxTooltipSize = windowSize.width - fullPadding

        val isCentralPositionTooltip =
            halfPopupContentSize <= leftSpace && halfPopupContentSize <= rightSpace

        when {
            isCentralPositionTooltip -> {
                popupPosition =
                    IntOffset(centerPositionX.toInt() - halfPopupContentSize, popupPosition.y)
                val arrowPosition = halfPopupContentSize.toFloat() - horizontalPaddingInPx
                onArrowPositionX.invoke(arrowPosition)
            }

            tooltipWidth >= maxTooltipSize -> {
                popupPosition =
                    IntOffset(windowSize.center.x - halfPopupContentSize, popupPosition.y)
                val arrowPosition = centerPositionX - popupPosition.x - horizontalPaddingInPx
                onArrowPositionX.invoke(arrowPosition)
            }

            halfPopupContentSize > rightSpace -> {
                popupPosition = IntOffset(centerPositionX.toInt(), popupPosition.y)
                val arrowPosition =
                    halfPopupContentSize + (halfPopupContentSize - rightSpace) - fullPadding

                onArrowPositionX.invoke(arrowPosition)
            }

            halfPopupContentSize > leftSpace -> {
                popupPosition = IntOffset(0, popupPosition.y)
                val arrowPosition = centerPositionX - horizontalPaddingInPx
                onArrowPositionX.invoke(arrowPosition)
            }

            else -> {
                val position = centerPositionX
                onArrowPositionX.invoke(position)
            }
        }

        return popupPosition
    }
}

@Composable
fun BubbleLayout(
    modifier: Modifier = Modifier,
    alignment: TooltipAlignment = TooltipAlignment.TOP_CENTER,
    arrowHeight: Dp,
    arrowPositionX: Float,
    content: String? = null,
    isSupportingText: Boolean,
    showCloseButton: Boolean,
    subContent: @Composable () -> Unit = {},
    onClose: (() -> Unit)?
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    val arrowHeightPx = with(LocalDensity.current) {
        arrowHeight.toPx()
    }

    Box(modifier = modifier.drawBehind {
        if (arrowPositionX <= 0f) return@drawBehind

        val isTopCenter = alignment == TooltipAlignment.TOP_CENTER

        val path = Path()

        if (isTopCenter) {
            val position = Offset(arrowPositionX, 0f)
            path.apply {
                moveTo(x = position.x, y = position.y)
                lineTo(x = position.x - arrowHeightPx, y = position.y)
                lineTo(x = position.x, y = position.y - arrowHeightPx)
                lineTo(x = position.x + arrowHeightPx, y = position.y)
                lineTo(x = position.x, y = position.y)
            }
        } else {
            val arrowY = drawContext.size.height
            val position = Offset(arrowPositionX, arrowY)
            path.apply {
                moveTo(x = position.x, y = position.y)
                lineTo(x = position.x + arrowHeightPx, y = position.y)
                lineTo(x = position.x, y = position.y + arrowHeightPx)
                lineTo(x = position.x - arrowHeightPx, y = position.y)
                lineTo(x = position.x, y = position.y)
            }
        }
        drawPath(
            path = path,
            color = colorScheme.bgSolidPrimary,
        )
        path.close()
    }) {
        Column(
            modifier = Modifier
                .wrapContentWidth()
                .padding(
                    top = if (isSupportingText) IBSpacing.spacingS else IBSpacing.spacingXs,
                    bottom = if (isSupportingText) IBSpacing.spacingS else IBSpacing.spacingXs,
                    start = IBSpacing.spacingS,
                    end = if (isSupportingText) IBSpacing.spacingS else IBSpacing.spacingXs
                ),
        ) {
            Row(
//                verticalAlignment = Alignment.CenterVertically
            ) {
                content?.let {
                    Text(
                        modifier = Modifier.weight(1f),
                        text = it,
                        style = typography.bodyBody_m,
                        color = colorScheme.contentOn_specialPrimary,
                    )
                }
                if (showCloseButton) {
                    Spacer(modifier = if (content.isNotNullOrEmpty()) Modifier else Modifier.weight(1f))
                    Icon(painter = painterResource(id = R.drawable.close),
                        tint = colorScheme.contentOn_specialPrimary,
                        contentDescription = "Close",
                        modifier = Modifier
                            .size(20.dp)
                            .padding(IBSpacing.spacing3xs)
                            .align(Alignment.Top)
                            .clickable { onClose?.invoke() })
                }
            }
            if (isSupportingText) {
                Spacer(modifier = if (content != null && showCloseButton) Modifier.height(IBSpacing.spacingXs) else Modifier)
                subContent()
            }
        }
    }
}

data class Placement(
    val offset: IntOffset = IntOffset(0, 0),
    val alignment: TooltipAlignment = TooltipAlignment.TOP_CENTER,
    val centerPositionX: Float = 0f,
)

fun calculateShowPosition(
    view: View,
    coordinates: LayoutCoordinates?,
    tooltipPosition: TooltipPosition
): Placement {
    coordinates ?: return Placement()

    val visibleWindowBounds = android.graphics.Rect()
    view.getWindowVisibleDisplayFrame(visibleWindowBounds)
    val boundsInWindow = coordinates.boundsInWindow()
    val heightAbove = boundsInWindow.top - visibleWindowBounds.top
    val heightBelow = visibleWindowBounds.bottom - visibleWindowBounds.top - boundsInWindow.bottom
    val centerPositionX = boundsInWindow.right - (boundsInWindow.right - boundsInWindow.left) / 2
    val offsetX = centerPositionX - visibleWindowBounds.centerX()

    val placementAbove = Placement(
        offset = IntOffset(x = offsetX.toInt(), y = -coordinates.size.height),
        alignment = TooltipAlignment.BOTTOM_CENTER,
        centerPositionX = centerPositionX
    )

    val placementBelow = Placement(
        offset = IntOffset(x = offsetX.toInt(), y = coordinates.size.height),
        alignment = TooltipAlignment.TOP_CENTER,
        centerPositionX = centerPositionX
    )

    return when (tooltipPosition) {
        TooltipPosition.ABOVE -> placementAbove
        TooltipPosition.BELOW -> placementBelow
        TooltipPosition.AUTO -> if (heightAbove < heightBelow) placementBelow else placementAbove
    }
}

enum class TooltipAlignment {
    BOTTOM_CENTER, TOP_CENTER,
}

enum class TooltipPosition {
    ABOVE, BELOW, AUTO
}

@Composable
@Preview
fun Example() {
    Column(Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .padding(20.dp)
                .clip(RoundedCornerShape(10.dp))
                .background(Color.Gray),
        ) {
            PopupExampleLayout(
                name = "",
                content = "This is a tooltip at the START position",
                isSupportingText = false,
                tooltipPosition = TooltipPosition.ABOVE,
                showCloseButton = false,
            )
            PopupExampleLayout(
                name = "Bottom & sub content",
                content = "This is a tooltip at the CENTER position",
                isSupportingText = true,
                tooltipPosition = TooltipPosition.ABOVE,
                subContent = {
                    Column {
                        Text(text = "Tooltips are used to describe or identify an element. In most scenarios, tooltips help the user understand the meaning, function or alt-text of an element.")
                        Spacer(modifier = Modifier.height(10.dp))
                        Text(text = "Tooltips are used to describe or identify an element.")
                    }
                }
            )
        }
        Spacer(modifier = Modifier.weight(1f))
        Column(
            modifier = Modifier
                .padding(20.dp)
                .clip(RoundedCornerShape(10.dp))
                .background(Color.Gray),
        ) {
            PopupExampleLayout(
                name = "Default",
                content = "This is a tooltip",
                isSupportingText = false
            )
            PopupExampleLayout(
                showCloseButton = false,
                name = "Top & sub content .   .   .   .   .   .    .",
                content = "This is a tooltip at the END position",
                isSupportingText = true,
                subContent = {
                    Column {
                        Text(text = "Tooltips are used to describe or identify an element. In most scenarios, tooltips help the user understand the meaning, function or alt-text of an element.")
                    }
                }
            )
        }
    }
}

@Composable
fun PopupExampleLayout(
    name: String,
    content: String,
    isSupportingText: Boolean,
    showCloseButton: Boolean = true,
    subContent: @Composable () -> Unit = {},
    tooltipPosition: TooltipPosition = TooltipPosition.AUTO,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(10.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            modifier = Modifier.weight(weight = 1f, fill = false),
            text = name,
            style = LocalTypography.current.titleTitle_l,
            color = Color.White,
            overflow = TextOverflow.Ellipsis,
        )
        IBankTooltip(
            isSupportingText = isSupportingText,
            subContent = subContent,
            content = content,
            showCloseButton = showCloseButton,
            tooltipPosition = tooltipPosition,
            showIconView = { modifier ->
                Icon(
                    painter = painterResource(id = R.drawable.information_circle),
                    contentDescription = "Tooltip Icon",
                    tint = LocalColorScheme.current.contentOn_specialPrimary,
                    modifier = modifier
                )
            }
        )
    }
}

@Composable
@Preview
fun BubbleLayoutPreview() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(200.dp)
    ) {
        TooltipPopup(
            isSupportingText = false,
            content = "This is a tooltip",
            showCloseButton = false,
            placement = Placement(),
        )
    }
}