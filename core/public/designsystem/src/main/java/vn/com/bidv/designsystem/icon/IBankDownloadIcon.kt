package vn.com.bidv.designsystem.icon

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme

/**
 * A composable function that displays an icon for downloads.
 *
 * @param modifier The modifier to be applied to the icon.
 * @param iconUrl The URL of the icon image. If null or empty, the default image is used.
 * @param defaultImgResId The resource ID of the default image to display when `iconUrl` is not available.
 */
@Composable
fun IBankDownloadIcon(
    modifier: Modifier = Modifier,
    iconUrl: String? = "",
    defaultImgResId: Int = R.drawable.tien_dien,
) {
    val colorScheme = LocalColorScheme.current
    Surface(
        modifier = modifier,
        shape = CircleShape,
        border = BorderStroke(IBBorderDivider.borderDividerS, colorScheme.borderMainPrimary),
        color = colorScheme.bgMainTertiary
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current).data(iconUrl).crossfade(true).build(),
            contentDescription = null,
            placeholder = painterResource(id = defaultImgResId),
            error = painterResource(id = defaultImgResId),
            contentScale = ContentScale.Crop,
            modifier = Modifier.padding(IBSpacing.spacing2xs)
        )
    }
}


