<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="156dp"
    android:height="104dp"
    android:viewportWidth="156"
    android:viewportHeight="104">
  <path
      android:pathData="M205.57,18.04C203.06,13.77 199.5,10.52 195.85,7.25C184.13,-3.16 169.43,-10.54 154.42,-13.9C153.3,-14.14 152.58,-15.16 152.69,-16.26C154.28,-30.5 152.55,-45.06 147.34,-58.66C145.56,-57.86 143.31,-56.68 140.61,-54.97L140.41,-54.84C145.67,-40.41 146.76,-24.96 143.84,-10.29C143.59,-9 144.48,-7.76 145.81,-7.56C166.17,-4.7 185.46,5.65 198.94,21.43C187.22,31.36 173.27,38.74 158.32,42.38C157.26,42.63 156.53,43.59 156.59,44.69C157.51,62.28 153.41,79.96 145.03,95.46C196.04,113.49 234.55,62.23 205.6,18.07L205.57,18.04Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="178.19"
          android:startY="-57.97"
          android:endX="177.59"
          android:endY="96.54"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M145.52,-71C127.78,-63.37 112.16,-49.77 102.47,-33.53C101.89,-32.57 100.69,-32.15 99.66,-32.62C86.74,-38.43 72.04,-41.35 57.42,-40.72C57.59,-38.79 57.95,-36.26 58.65,-33.14V-32.98C74.21,-33.47 89.36,-29.73 102.61,-22.43C103.78,-21.8 105.26,-22.24 105.84,-23.45C114.75,-41.71 130.82,-56.6 150.03,-64.5L151.12,-61.89C156.55,-48.51 158.69,-34 157.72,-19.76C157.63,-18.69 158.33,-17.73 159.39,-17.42C173,-13.79 185.64,-7.24 196.56,1.74H196.59C199.34,4.08 202.04,6.5 204.58,9.12C208.56,4.19 211.54,-2.06 213.21,-7.79C228.8,-49.11 185.7,-88.01 145.52,-71.03V-71Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="68.42"
          android:startY="-70.36"
          android:endX="215.41"
          android:endY="-19.04"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M133.5,80.81L133.64,80.53C132.97,80.09 132.33,79.65 131.69,79.18C119.66,70.51 110.5,59.33 104.38,46.39C103.82,45.21 102.34,44.71 101.18,45.35C83.05,54.85 61.19,57.79 41,52.78C44.6,37.89 51.44,23.87 61.39,12.2C62.08,11.37 62.11,10.16 61.41,9.34C50.25,-4.32 43.06,-20.98 40.64,-38.54V-38.71C35.49,-37.38 30.14,-34.82 25.86,-31.99C-23.29,0 4.92,65.78 61.11,62.94C74.05,62.94 86.53,60.32 97.94,55.48C99,55.04 100.26,55.42 100.81,56.42C107.8,68.61 117.94,79.51 130.1,87.5C131.11,85.85 132.3,83.64 133.53,80.81H133.5Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="103.88"
          android:startY="104.52"
          android:endX="15.48"
          android:endY="-20.91"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M186.82,15.37L186.68,15.26C174.43,24.68 159.92,30.43 144.91,32.27C143.57,32.44 142.66,33.65 142.91,34.97C146.41,54.91 142.63,76.52 131.41,93.86L129.07,92.38C116.65,84.53 106.49,74.32 98.83,62.17C98.24,61.27 97.1,60.88 96.07,61.27C79.53,67.54 61.16,69.19 43.67,66.06C41.08,115.56 99.08,141.13 133.22,103.47C148.09,87.56 153.46,62.37 151.62,41.17C151.54,40.07 152.35,38.99 153.43,38.74C167.38,35.91 181.03,29.69 192.5,20.63C191.16,19.2 189.3,17.41 186.82,15.37Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="200.91"
          android:startY="50.58"
          android:endX="52.94"
          android:endY="98.19"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M134.15,-80.99C134.15,-80.99 134.15,-80.99 134.13,-81.02C99.46,-118.6 38.84,-88.95 45.25,-39.5C45.25,-39.5 45.25,-39.5 45.25,-39.47C47.56,-21.58 55.66,-3.96 67.55,9.34C68.3,10.2 68.27,11.46 67.55,12.29C58.03,22.67 50.65,35.61 46.78,49.57C48.67,50.01 51.18,50.45 54.3,50.75H54.57C58.89,36.02 67.19,22.89 78.3,12.65C79.27,11.74 79.27,10.22 78.33,9.31C77.05,8.08 75.79,6.81 74.6,5.46C62.01,-8.03 53.99,-26.42 52.54,-45.06C67.86,-46.33 83.73,-44.12 97.87,-38.43C98.9,-38.01 100.04,-38.37 100.66,-39.28C110.32,-53.95 124.07,-65.87 140.2,-73.61C138.5,-76.2 136.41,-78.7 134.18,-80.99H134.15Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="22.06"
          android:startY="30.63"
          android:endX="113.8"
          android:endY="-95.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
