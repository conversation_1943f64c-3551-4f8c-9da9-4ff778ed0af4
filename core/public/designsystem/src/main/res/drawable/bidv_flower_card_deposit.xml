<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="165dp"
    android:height="125dp"
    android:viewportWidth="165"
    android:viewportHeight="125">
  <path
      android:pathData="M208.57,19.04C206.06,14.77 202.5,11.52 198.85,8.25C187.13,-2.16 172.43,-9.54 157.42,-12.9C156.3,-13.14 155.58,-14.16 155.69,-15.26C157.28,-29.5 155.55,-44.06 150.34,-57.66C148.56,-56.86 146.31,-55.68 143.61,-53.97L143.41,-53.84C148.67,-39.41 149.76,-23.96 146.84,-9.29C146.59,-8 147.48,-6.76 148.81,-6.56C169.17,-3.7 188.46,6.65 201.94,22.43C190.22,32.37 176.27,39.74 161.32,43.38C160.26,43.63 159.53,44.59 159.59,45.69C160.51,63.28 156.41,80.96 148.03,96.46C199.04,114.49 237.55,63.23 208.6,19.07L208.57,19.04Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="181.19"
          android:startY="-56.97"
          android:endX="180.59"
          android:endY="97.54"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M148.52,-70C130.78,-62.37 115.16,-48.77 105.47,-32.53C104.89,-31.57 103.69,-31.15 102.66,-31.62C89.74,-37.43 75.04,-40.35 60.42,-39.72C60.59,-37.79 60.95,-35.26 61.65,-32.14V-31.98C77.21,-32.47 92.36,-28.73 105.61,-21.43C106.78,-20.8 108.26,-21.24 108.84,-22.45C117.75,-40.71 133.82,-55.6 153.03,-63.5L154.12,-60.89C159.55,-47.51 161.69,-33 160.72,-18.76C160.63,-17.69 161.33,-16.73 162.39,-16.42C176,-12.79 188.64,-6.24 199.56,2.74H199.59C202.34,5.08 205.04,7.5 207.58,10.12C211.56,5.19 214.54,-1.06 216.21,-6.79C231.8,-48.11 188.7,-87.01 148.52,-70.03V-70Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="71.42"
          android:startY="-69.36"
          android:endX="218.41"
          android:endY="-18.04"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M136.5,81.81L136.64,81.53C135.97,81.09 135.33,80.65 134.69,80.18C122.66,71.51 113.5,60.33 107.38,47.39C106.82,46.21 105.34,45.71 104.18,46.35C86.05,55.85 64.19,58.79 44,53.78C47.6,38.89 54.44,24.87 64.39,13.2C65.08,12.37 65.11,11.16 64.41,10.34C53.25,-3.32 46.06,-19.98 43.64,-37.54V-37.71C38.49,-36.38 33.14,-33.82 28.86,-30.99C-20.29,1 7.92,66.78 64.11,63.94C77.05,63.94 89.53,61.32 100.94,56.48C102,56.04 103.26,56.42 103.81,57.42C110.8,69.61 120.94,80.51 133.1,88.5C134.11,86.85 135.3,84.64 136.53,81.81H136.5Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="106.88"
          android:startY="105.52"
          android:endX="18.48"
          android:endY="-19.91"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M189.82,16.37L189.68,16.26C177.43,25.68 162.92,31.43 147.91,33.27C146.57,33.44 145.66,34.65 145.91,35.97C149.41,55.91 145.63,77.52 134.41,94.86L132.07,93.38C119.65,85.53 109.49,75.32 101.83,63.17C101.25,62.27 100.1,61.88 99.07,62.27C82.53,68.54 64.16,70.19 46.67,67.06C44.08,116.56 102.08,142.13 136.22,104.47C151.09,88.56 156.46,63.37 154.62,42.17C154.54,41.07 155.35,39.99 156.43,39.74C170.38,36.91 184.03,30.69 195.5,21.63C194.16,20.2 192.3,18.41 189.82,16.37Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="203.91"
          android:startY="51.58"
          android:endX="55.94"
          android:endY="99.19"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M137.15,-79.99C137.15,-79.99 137.15,-79.99 137.13,-80.02C102.46,-117.6 41.84,-87.95 48.25,-38.5C48.25,-38.5 48.25,-38.5 48.25,-38.47C50.56,-20.58 58.66,-2.96 70.55,10.34C71.3,11.2 71.27,12.46 70.55,13.29C61.03,23.67 53.65,36.61 49.78,50.57C51.67,51.01 54.18,51.45 57.3,51.75H57.57C61.89,37.02 70.19,23.89 81.3,13.65C82.27,12.74 82.27,11.22 81.33,10.31C80.04,9.08 78.79,7.81 77.59,6.46C65.01,-7.03 56.99,-25.42 55.54,-44.06C70.86,-45.33 86.73,-43.12 100.87,-37.43C101.9,-37.01 103.04,-37.37 103.66,-38.28C113.32,-52.95 127.07,-64.87 143.2,-72.61C141.5,-75.2 139.41,-77.7 137.18,-79.99H137.15Z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="25.06"
          android:startY="31.63"
          android:endX="116.8"
          android:endY="-94.5"
          android:type="linear">
        <item android:offset="0" android:color="#FF13AE8E"/>
        <item android:offset="0.24" android:color="#C613AE8E"/>
        <item android:offset="0.78" android:color="#3A13AE8E"/>
        <item android:offset="1" android:color="#0013AE8E"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
