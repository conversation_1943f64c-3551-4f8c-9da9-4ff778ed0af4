@Suppress("DSL_SCOPE_VIOLATION") 
plugins { 
    alias(libs.plugins.local.ibank.feature) 
    alias(libs.plugins.local.android.openapi.generate)
    alias(libs.plugins.kotlin.serialization)
} 
 
android { 
    namespace = "vn.com.bidv.feature.government.service"
} 
 
dependencies {
    implementation(libs.gson)
    implementation(libs.swagger.annotations)
    implementation(libs.bundles.retrofit)
    testImplementation(libs.junit4)
    testImplementation(libs.mockito.core)
    testImplementation(libs.mockito.kotlin)
    testImplementation(libs.kotlinx.coroutines.test)
    testImplementation(libs.mockk)
    implementation(libs.core.common)
    implementation(libs.core.network)
    implementation(libs.core.log)
    implementation(project(":core:public:designsystem"))
    implementation(project(":feature:common"))
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.androidx.compose.ui.tooling)
} 
