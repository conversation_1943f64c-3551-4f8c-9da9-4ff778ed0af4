#!/bin/sh

set -e
git config -f .gitmodules --get-regexp '^submodule\..*\.path$' |
    while read path_key path
    do
        name_key=$(echo $path_key | sed 's/\submodule\.\(.*\)\.path/\1/')
#        url_key=$(echo $path_key | sed 's/\.path/.url/')
#        branch_key=$(echo $path_key | sed 's/\.path/.branch/')
        name=$(git config -f .gitmodules --get "$name_key")
#        url=$(git config -f .gitmodules --get "$url_key")
#        branch=$(git config -f .gitmodules --get "$branch_key" || echo "master")
        echo "name: $name"
#        echo "url: $url"
#        echo "branch: $branch"
        git submodule update --init $name || continue
    done