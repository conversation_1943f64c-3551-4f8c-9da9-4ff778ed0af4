apply plugin: 'com.android.library'

android {
    namespace "com.yalantis.ucrop"
    compileSdk 34
    defaultConfig {
        minSdkVersion 21

        vectorDrawables.useSupportLibrary = true
    }

    buildFeatures {
        buildConfig true
    }

    buildTypes {
        debug {
            buildConfigField "String", "LIBRARY_PACKAGE_NAME", "\"${namespace}\""
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            buildConfigField "String", "LIBRARY_PACKAGE_NAME", "\"${namespace}\""
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    lintOptions {
        abortOnError false
    }

    resourcePrefix 'ucrop_'

}

dependencies {
    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.exifinterface)
    implementation(libs.constraintlayout)
    implementation "androidx.transition:transition:1.4.1"
    implementation(project(":core:public:designsystem"))
}
