<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    tools:showIn="@layout/ucrop_controls"
    tools:visibility="visible">

    <TextView
        android:id="@+id/text_view_rotate"
        style="@style/ucrop_TextViewWidgetText"
        tools:text="100°" />

    <com.yalantis.ucrop.view.widget.HorizontalProgressWheelView
        android:id="@+id/rotate_scroll_wheel"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/text_view_rotate"
        android:layout_toStartOf="@+id/wrapper_rotate_by_angle"
        android:layout_toLeftOf="@+id/wrapper_rotate_by_angle"
        android:layout_toEndOf="@+id/wrapper_reset_rotate"
        android:layout_toRightOf="@+id/wrapper_reset_rotate" />

    <FrameLayout
        android:id="@+id/wrapper_reset_rotate"
        style="@style/ucrop_WrapperRotateButton"
        android:layout_centerVertical="true"
        android:background="?attr/selectableItemBackground">

        <ImageView
            style="@style/ucrop_ImageViewWidgetIcon"
            app:srcCompat="@drawable/ucrop_ic_reset" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/wrapper_rotate_by_angle"
        style="@style/ucrop_WrapperRotateButton"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:background="?attr/selectableItemBackground">

        <ImageView
            style="@style/ucrop_ImageViewWidgetIcon"
            android:src="@drawable/ucrop_ic_angle"
            android:tint="@color/ucrop_color_widget_rotate_angle" />

    </FrameLayout>

</RelativeLayout>