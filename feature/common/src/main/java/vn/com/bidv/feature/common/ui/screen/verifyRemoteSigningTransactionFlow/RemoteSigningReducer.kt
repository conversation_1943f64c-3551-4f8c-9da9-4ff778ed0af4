package vn.com.bidv.feature.common.ui.screen.verifyRemoteSigningTransactionFlow

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.TransRetrieveAuthValueResDMO

class RemoteSigningReducer :
    Reducer<RemoteSigningReducer.RemoteSigningViewState, RemoteSigningReducer.RemoteSigningViewEvent, RemoteSigningReducer.RemoteSigningViewEffect> {

    @Immutable
    data class RemoteSigningViewState(
        val isInitRemoteSigning: Boolean = false,
    )
     : ViewState

    @Immutable
    sealed class RemoteSigningViewEvent : ViewEvent {
        data class DoInitRemoteSigning(val authId: String): RemoteSigningViewEvent()
        data class DoInitRemoteSigningSuccess(val data: TransRetrieveAuthValueResDMO?): RemoteSigningViewEvent()
    }

    @Immutable
    sealed class RemoteSigningViewEffect : SideEffect {
        data class DoInitRemoteSigningEffect(val authId: String): RemoteSigningViewEffect()
        data class DoInitRemoteSigningSuccessEffect(val data: TransRetrieveAuthValueResDMO?): RemoteSigningViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: RemoteSigningViewState,
        event: RemoteSigningViewEvent,
    ): Pair<RemoteSigningViewState, RemoteSigningViewEffect?> {
        return when (event) {
            is RemoteSigningViewEvent.DoInitRemoteSigning -> {
                previousState.copy(isInitRemoteSigning = true) to RemoteSigningViewEffect.DoInitRemoteSigningEffect(event.authId)
            }

            is RemoteSigningViewEvent.DoInitRemoteSigningSuccess -> {
                previousState to RemoteSigningViewEffect.DoInitRemoteSigningSuccessEffect(event.data)
            }
        }
    }
}
