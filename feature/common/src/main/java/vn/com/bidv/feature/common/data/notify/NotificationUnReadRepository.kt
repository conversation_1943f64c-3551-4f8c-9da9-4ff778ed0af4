package vn.com.bidv.feature.common.data.notify

import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import vn.com.bidv.feature.common.domain.notificationcommon.ModelNotifyStatementCountDMO
import javax.inject.Inject
import kotlin.math.max

@ActivityRetainedScoped
class NotificationUnReadRepository @Inject constructor() {
    private val _notyUnreadCountState = MutableStateFlow(ModelNotifyStatementCountDMO())
    val notyUnreadCountState: StateFlow<ModelNotifyStatementCountDMO> = _notyUnreadCountState.asStateFlow()


    private fun minusCount(initCount: Long?, count: Int): Long {
        return initCount?.let {
            max(it - count, 0)
        } ?: 0
    }

    fun updateNotyUnreadCountState(modelNotifyStatementCountDMO: ModelNotifyStatementCountDMO) {
        _notyUnreadCountState.value = modelNotifyStatementCountDMO
    }

    fun minusUnReadBal(count: Int){
        _notyUnreadCountState.value = _notyUnreadCountState.value.copy(
            totalNotifyUnreadBal = minusCount(_notyUnreadCountState.value.totalNotifyUnreadBal, count),
            totalNotifyUnread = minusCount(_notyUnreadCountState.value.totalNotifyUnread, count),
        )
    }

    fun minusUnReadTrans(count: Int){
        _notyUnreadCountState.value = _notyUnreadCountState.value.copy(
            totalNotifyUnreadTrans = minusCount(_notyUnreadCountState.value.totalNotifyUnreadTrans, count),
            totalNotifyUnread = minusCount(_notyUnreadCountState.value.totalNotifyUnread, count),
        )
    }

    fun minusUnReadPromo(count: Int){
        _notyUnreadCountState.value = _notyUnreadCountState.value.copy(
            totalNotifyUnreadPromo = minusCount(_notyUnreadCountState.value.totalNotifyUnreadPromo, count),
            totalNotifyUnread = minusCount(_notyUnreadCountState.value.totalNotifyUnread, count),
        )
    }

    fun minusUnReadSys(count: Int){
        _notyUnreadCountState.value = _notyUnreadCountState.value.copy(
            totalNotifyUnreadSys = minusCount(_notyUnreadCountState.value.totalNotifyUnreadSys, count),
            totalNotifyUnread = minusCount(_notyUnreadCountState.value.totalNotifyUnread, count),
        )
    }

    fun resetNotyUnreadCountState() {
        _notyUnreadCountState.value = ModelNotifyStatementCountDMO()
    }
}
