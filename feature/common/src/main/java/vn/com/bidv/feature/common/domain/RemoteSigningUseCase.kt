package vn.com.bidv.feature.common.domain

import vn.com.bidv.feature.common.data.UtilitiesRepository
import vn.com.bidv.feature.common.domain.data.TransRetrieveAuthValueResDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class RemoteSigningUseCase @Inject constructor(
    private val repo: UtilitiesRepository,
) {
    suspend fun retrieveAuthStatus(authId: String): DomainResult<TransRetrieveAuthValueResDMO> {
        val result = repo.retrieveAuthStatus(authId)
        return result.convert(TransRetrieveAuthValueResDMO::class.java)
    }
}