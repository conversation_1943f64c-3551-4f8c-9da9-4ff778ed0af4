package vn.com.bidv.feature.common.domain.verifyFlowUseCase

import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyCreateTransaction
import vn.com.bidv.sdkbase.domain.DomainResult

interface VerifyByTypeTransactionUseCase {
    suspend fun initTransaction(input: InputVerifyTransaction): DomainResult<InitVerifyTransactionResponse>

    suspend fun initCreateTransaction(input: InputVerifyCreateTransaction): DomainResult<InitVerifyTransactionResponse> = DomainResult.Error("Not Implemented")

    suspend fun verifyTransaction(initResponse: InitVerifyTransactionResponse, reqValue: String?): DomainResult<String>
}