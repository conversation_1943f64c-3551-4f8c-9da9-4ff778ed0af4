package vn.com.bidv.feature.common.domain.data

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.AuthTxnType
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.ModelVerifyTransactionUI
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.VerifyMethodType

@Serializable
data class TransAuthDMO(
    @SerializedName("authType") val authType: String? = null,
    @SerializedName("isSameDevice") val isSameDevice: String? = null,
    @SerializedName("transKey") val transKey: String? = null,
    @SerializedName(value = "qrCode", alternate = ["qrData"]) val qrCode: String? = null,
    @SerializedName("expTime") val expTime: String? = null,
    @SerializedName("userId") val userId: String? = null,
    @SerializedName("authId") val authId: String? = null,
    @SerializedName("additionalInfo") val additionalInfo: AdditionalInfoDMO? = null,
    @SerializedName("isGenOtp") val isGenOtp: Boolean = true
) {
    fun mapToModelVerifySmartOtpTransactionUI(): ModelVerifyTransactionUI {
        return ModelVerifyTransactionUI(
            authTxnType = if (isFinancialTransaction()) {
                AuthTxnType.FINANCIAL_TRANSACTION
            } else {
                AuthTxnType.NON_FINANCIAL_TRANSACTION
            },
            totalTxn = additionalInfo?.totalTrans?.toString(),
            totalAmount = additionalInfo?.trans?.firstOrNull()?.amount,
            totalAmountText = additionalInfo?.trans?.firstOrNull()?.amountText,
            currCode = additionalInfo?.trans?.firstOrNull()?.ccy,
            txnType = additionalInfo?.subProductName ?: additionalInfo?.productName,
            createdBy = additionalInfo?.createdBy,
            timeEffectTxn = System.currentTimeMillis() + (expTime?.toLongOrNull() ?: 120) * 1000,
            authId = additionalInfo?.authId ?: authId ?: "",
            verifyMethodType = when (isSameDevice) {
                "Y" -> VerifyMethodType.SAME_DEVICE
                "N" -> VerifyMethodType.OTHER_DEVICE
                else -> VerifyMethodType.SCAN_QR
            },
            qrCode = qrCode,
            userId = additionalInfo?.userId ?: userId ?: "",
            isGenOtp = isGenOtp
        )
    }

    private fun isFinancialTransaction(): Boolean {
        return additionalInfo?.trans?.any { it.amount != null } == true
    }
}

@Serializable
data class AdditionalInfoDMO(
    @SerializedName("type") val type: String? = null,
    @SerializedName("productCode") val productCode: String? = null,
    @SerializedName("subProductCode") val subProductCode: String? = null,
    @SerializedName("amount") val amount: String? = null,
    @SerializedName("authId") val authId: String? = null,
    @SerializedName("userId") val userId: String? = null,
    @SerializedName("totalTrans") val totalTrans: Int? = null,
    @SerializedName("ccy") val ccy: String? = null,
    @SerializedName("trans") val trans: List<TransactionDMO>? = null,
    @SerializedName("createdBy") val createdBy: String? = null,
    @SerializedName("qrType") val qrType: String? = null,
    @SerializedName("productName") val productName: String? = null,
    @SerializedName("subProductName") val subProductName: String? = null
)

@Serializable
data class TransactionDMO(
    @SerializedName("ccy") val ccy: String? = null,
    @SerializedName("amount") val amount: String? = null,
    @SerializedName("total") val total: String? = null,
    @SerializedName("amountText") val amountText: String? = null
)
