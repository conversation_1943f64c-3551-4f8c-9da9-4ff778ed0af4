package vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model

import vn.com.bidv.feature.common.constants.MenuCode
import vn.com.bidv.sdkbase.data.ReloadFunctionKey
import vn.com.bidv.sdkbase.data.ReloadKey
import vn.com.bidv.sdkbase.data.ReloadModuleKey

enum class VerifyTransactionTypeConstant(
    val code: String,
    val keySuccess: ReloadKey? = null,
    val keyFail: ReloadKey? = null
) {
    DepositSaveAndPush(
        MenuCode.M_RPT_SEND.code,
        ReloadKey(ReloadModuleKey.ACCOUNT_SERVICE, ReloadFunctionKey.LIST_PENDING),
        ReloadKey(ReloadModuleKey.ACCOUNT_SERVICE, ReloadFunctionKey.LIST_PENDING)
    ), // đẩy duyệt tiền gửi
    WithdrawalSaveAndPush(
        MenuCode.M_RPT_WDW.code,
        ReloadKey(ReloadModuleKey.ACCOUNT_SERVICE_WITHDRAWAL, ReloadFunctionKey.LIST_PENDING),
        ReloadK<PERSON>(ReloadModuleKey.ACCOUNT_SERVICE_WITHDRAWAL, ReloadFunctionKey.LIST_PENDING)
    ), // đẩy duyệt tiền rút
    DepositApproval(
        MenuCode.M_APPROVE_DPT_SEND.code,
        ReloadKey(ReloadModuleKey.ACCOUNT_SERVICE, ReloadFunctionKey.LIST_DEPOSIT_APPROVE),
        ReloadKey(ReloadModuleKey.ACCOUNT_SERVICE, ReloadFunctionKey.LIST_DEPOSIT_APPROVE)
    ), // duyệt tiền gửi
    WithdrawalApproval(
        MenuCode.M_APPROVE_DPT_WDW.code,
        ReloadKey(ReloadModuleKey.ACCOUNT_SERVICE_WITHDRAWAL, ReloadFunctionKey.LIST_DEPOSIT_APPROVE),
        ReloadKey(ReloadModuleKey.ACCOUNT_SERVICE_WITHDRAWAL, ReloadFunctionKey.LIST_DEPOSIT_APPROVE)
    ), // duyệt tiền rút
    DepositRecallInit(
        MenuCode.M_DEPOSIT_RQT.code,
        ReloadKey(ReloadModuleKey.ACCOUNT_SERVICE, ReloadFunctionKey.LIST_RECALL),
        ReloadKey(ReloadModuleKey.ACCOUNT_SERVICE, ReloadFunctionKey.LIST_RECALL)
    ), // thu hồi giao dịch tiền gửi tương lai
    DepositRecallApprove(
        MenuCode.M_APPROVE_DPT_RCL.code,
        ReloadKey(ReloadModuleKey.ACCOUNT_SERVICE, ReloadFunctionKey.LIST_RECALL),
        ReloadKey(ReloadModuleKey.ACCOUNT_SERVICE, ReloadFunctionKey.LIST_RECALL)
    ), // duyệt thu hồi giao dịch tiền gửi tương lai
    ManageApprovalRequests(MenuCode.M_MANAGE_PENDING.code),
    CNRApproval(MenuCode.M_APPROVE_PMT_BILL.code), // duyệt thanh toán hóa đơn
    CNRPush(MenuCode.M_RPT_BILL.code), // đẩy duyệt thanh toán hóa đơn
    LoanApproval(MenuCode.M_APPROVE_LOAN_REPMT.code), //duyệt trả nợ
    DisbursementApproval(MenuCode.M_APPROVE_LOAN_DBMT.code), //duyệt giải ngân
    PaymentApproval(
        MenuCode.M_APPROVE_PMT_DMC.code,
        ReloadKey(ReloadModuleKey.TRANSACTION_APPROVAL, ReloadFunctionKey.LIST_PENDING),
        ReloadKey(ReloadModuleKey.TRANSACTION_APPROVAL, ReloadFunctionKey.LIST_PENDING)
    ), // duyệt chuyen tien trong nuoc
    PaymentSubmit(
        MenuCode.M_PAYMENT_DMC.code,
        ReloadKey(ReloadModuleKey.PAYMENT, ReloadFunctionKey.LIST_PENDING),
        ReloadKey(ReloadModuleKey.PAYMENT, ReloadFunctionKey.LIST_PENDING)
    ), // day duyệt chuyen tien trong nuoc
    PaymentBulkApprove(
        MenuCode.M_APPROVE_PMT_BULK.code,
        ReloadKey(ReloadModuleKey.PAYMENT_BULK, ReloadFunctionKey.LIST_BULK),
        ReloadKey(ReloadModuleKey.PAYMENT_BULK, ReloadFunctionKey.LIST_BULK)
    ), // Duyet bang ke
    PaymentRecallInit(
        MenuCode.M_PAYMENT_MNE.code,
        ReloadKey(ReloadModuleKey.PAYMENT_RECALL, ReloadFunctionKey.LIST_RECALL),
        ReloadKey(ReloadModuleKey.PAYMENT_RECALL, ReloadFunctionKey.LIST_RECALL)
    ), // Thu hoi giao dich tuong lai
    PaymentRecallApprove(
        MenuCode.M_APPROVE_PMT_RCL.code,
        ReloadKey(ReloadModuleKey.PAYMENT_RECALL, ReloadFunctionKey.LIST_RECALL),
        ReloadKey(ReloadModuleKey.PAYMENT_RECALL, ReloadFunctionKey.LIST_RECALL)
    ), // Duyet giao dich tuong lai
    PaymentSalaryApprove(
        MenuCode.M_APPROVE_PMT_SLY.code,
        ReloadKey(ReloadModuleKey.PAYMENT_SALARY, ReloadFunctionKey.LIST_SALARY),
        ReloadKey(ReloadModuleKey.PAYMENT_SALARY, ReloadFunctionKey.LIST_SALARY)
    ), // Duyet giao dich tuong lai
    PaymentInquiryInit(
        MenuCode.M_RPT_CHECK.code,
        ReloadKey(ReloadModuleKey.PAYMENT_INQUIRY, ReloadFunctionKey.LIST_INQUIRY),
        ReloadKey(ReloadModuleKey.PAYMENT_INQUIRY, ReloadFunctionKey.LIST_INQUIRY)
    ), // Tao tra soat
    PaymentInquiryApprove(
        MenuCode.M_APPROVE_RQT_CHECK.code,
        ReloadKey(ReloadModuleKey.PAYMENT_INQUIRY, ReloadFunctionKey.LIST_INQUIRY),
        ReloadKey(ReloadModuleKey.PAYMENT_INQUIRY, ReloadFunctionKey.LIST_INQUIRY)
    ), // Duyet tra soat
    SCAN_QR(MenuCode.M_SCAN_QR()),
    CHANGE_PIN(MenuCode.M_CHANGE_PIN()),
    TurnOnBiometric(MenuCode.TURN_ON_BIOMETRIC.code, ReloadKey(ReloadModuleKey.LOGIN, ReloadFunctionKey.TURN_ON_BIOMETRIC), keyFail = ReloadKey(ReloadModuleKey.LOGIN, ReloadFunctionKey.TURN_ON_BIOMETRIC_FAIL)),
    UserInfo(MenuCode.USER_INFO.code),
    ManageQuestions(MenuCode.MANAGE_QUESTIONS.code, ReloadKey(ReloadModuleKey.LOGIN)),
    EditQuestions(MenuCode.EDIT_QUESTIONS.code, ReloadKey(ReloadModuleKey.LOGIN, ReloadFunctionKey.EDIT_QUESTIONS)),
    ChangePassword(MenuCode.CHANGE_PASSWORD.code, ReloadKey(ReloadModuleKey.LOGIN, ReloadFunctionKey.POSITIVE_CHANGE_PW)),
    ForeignExchangeSaveAndPush(
        MenuCode.M_FOREIGN_EXCHANGE_SEND.code,
        ReloadKey(ReloadModuleKey.FOREIGN_EXCHANGE, ReloadFunctionKey.LIST_PENDING),
    ), // đẩy duyệt giao dịch ngoại tệ
    ForeignExchangeApproval(
        MenuCode.M_APPROVE_FX_SUB.code,
        ReloadKey(ReloadModuleKey.FOREIGN_EXCHANGE, ReloadFunctionKey.LIST_PENDING),
    ), // duyệt giao dịch ngoại tệ
    DEFAULT(MenuCode.DEFAULT.code); // mac dinh

    companion object {

        fun getTypeConstantByCode(code: String?) : VerifyTransactionTypeConstant {
            return VerifyTransactionTypeConstant.entries.find { it.code == code } ?: DEFAULT
        }

        fun getKeySuccessByCode(code: String?): ReloadKey? {
            return VerifyTransactionTypeConstant.entries.find { it.code == code }?.keySuccess
        }

        fun getKeyFailByCode(code: String?): ReloadKey? {
            return VerifyTransactionTypeConstant.entries.find { it.code == code }?.keyFail
        }
    }
}