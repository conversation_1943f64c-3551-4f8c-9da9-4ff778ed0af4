package vn.com.bidv.feature.common.navigation

import vn.com.bidv.feature.common.domain.verifyFlowUseCase.ScanQrVerifyUseCase
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.scanqr.ScanQrVerifySuccessScreen
import javax.inject.Inject

class ScanQrVerifyFlowScreenBuilder @Inject constructor(
    useCase: ScanQrVerifyUseCase
): VerifyTransactionFlowScreenBuilder(
    useCase = useCase,
    type = VerifyTransactionTypeConstant.SCAN_QR,
    content = { navController, _, _ ->
        ScanQrVerifySuccessScreen(navController)
    },
    messageSnackBar = null,
    isShowResultPopup = true,
    isAllowTransResponse = true
)