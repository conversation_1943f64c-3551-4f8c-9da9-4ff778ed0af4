package vn.com.bidv.feature.common.ui.screen.supportopenbrowser

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class SupportOpenBrowserReducer :
    Reducer<SupportOpenBrowserReducer.SupportOpenBrowserViewState, SupportOpenBrowserReducer.SupportOpenBrowserViewEvent, SupportOpenBrowserReducer.SupportOpenBrowserViewEffect> {

    data class SupportOpenBrowserViewState(
        val isInitSuccess: Boolean = false,
        val url: String = ""
    ) : ViewState

    @Immutable
    sealed class SupportOpenBrowserViewEvent : ViewEvent {
        data class InitData(val urlType: String) : SupportOpenBrowserViewEvent()
        data class InitDataSuccess(
            val url: String
        ) : SupportOpenBrowserViewEvent()
        data object ShowToast : SupportOpenBrowserViewEvent()
    }

    @Immutable
    sealed class SupportOpenBrowserViewEffect : SideEffect {
        data class InitData(val urlType: String) : SupportOpenBrowserViewEffect()
        data object ShowToast : SupportOpenBrowserViewEffect()
    }

    override fun reduce(
        previousState: SupportOpenBrowserViewState,
        event: SupportOpenBrowserViewEvent,
    ): Pair<SupportOpenBrowserViewState, SupportOpenBrowserViewEffect?> {
        return when (event) {
            is SupportOpenBrowserViewEvent.InitData -> {
                previousState to SupportOpenBrowserViewEffect.InitData(
                    urlType = event.urlType
                )
            }

            is SupportOpenBrowserViewEvent.InitDataSuccess -> {
                previousState.copy(
                    isInitSuccess = true,
                    url = event.url
                ) to null
            }

            SupportOpenBrowserViewEvent.ShowToast -> {
                previousState to SupportOpenBrowserViewEffect.ShowToast
            }
        }
    }
}
