package vn.com.bidv.feature.common.ui.screen.commontransaction

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import vn.com.bidv.feature.common.domain.GetPendingApprovalCountUseCase
import vn.com.bidv.feature.common.domain.data.MenuDMO
import javax.inject.Inject

@HiltViewModel
class TransactionApprovalViewModel @Inject constructor(
    private val getMenuWithCountsUseCase: GetPendingApprovalCountUseCase,
    itemBuilders: Set<@JvmSuppressWildcards TransactionBaseBuilder>,
    reducer: TransactionBaseReducer,
) : TransactionBaseViewModel(
    reducer = reducer,
    itemBuilders = itemBuilders
) {
    override fun fetchData(
        onLoadSuccess: (data: List<MenuDMO>?) -> Unit,
        onLoadFail: (String?) -> Unit
    ): Job {
        return callDomain(
            showLoadingIndicator = true, // Loading online
            onSuccess = { onLoadSuccess(it.data) }
        ) {
            getMenuWithCountsUseCase.getMenuApprovalWithCount()
        }
    }
}