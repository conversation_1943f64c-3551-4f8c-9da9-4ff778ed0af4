package vn.com.bidv.feature.common.data.notify

import vn.com.bidv.feature.common.data.utilitiesnotify.apis.UtilitiesNotifyStatementsApi
import vn.com.bidv.feature.common.data.utilitiesnotify.model.ExtParamsRequest
import vn.com.bidv.feature.common.data.utilitiesnotify.model.NotifyStatementCountResponse
import vn.com.bidv.feature.common.data.utilitiesnotify.model.NotifyStatementMarkRequest
import vn.com.bidv.feature.common.data.utilitiesnotify.model.ResultString
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class NotifyCommonRepository @Inject constructor(
    private val notifyService: UtilitiesNotifyStatementsApi
): BaseRepository() {

    suspend fun countUnread(): NetworkResult<NotifyStatementCountResponse> = launch {
        notifyService.countNotifyUnread()
    }

    suspend fun markAsRead(
        type: String,
        notifyIdList: List<Long>,
        isMarkAll: Boolean,
        listTransId: List<Long>
    ): NetworkResult<ResultString> = launch {
        notifyService.markNotifyRead(
            NotifyStatementMarkRequest(
                type = type,
                notifyIdList = notifyIdList,
                isMarkAll = isMarkAll,
                extParams = ExtParamsRequest(
                    transNotifyIdList = listTransId
                ),
            )
        )
    }
}