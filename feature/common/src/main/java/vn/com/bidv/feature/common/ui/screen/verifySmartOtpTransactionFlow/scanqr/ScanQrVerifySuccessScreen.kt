package vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.scanqr

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.feature.common.navigation.NavigationHelper
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow.sharedVerifyTransactionViewModel
import vn.com.bidv.localization.R

@Composable
fun ScanQrVerifySuccessScreen(navController: NavHostController) {
    val viewModelFlow = navController.sharedVerifyTransactionViewModel()
    IBankModalConfirm(
        title = stringResource(R.string.thong_bao),
        supportingText = stringResource(R.string.vui_long_kiem_tra_ket_qua_giao_dich_tren_trinh_duyet_hoac_thiet_bi_thuc_hien_giao_dich),
        modalConfirmType = ModalConfirmType.Info,
        listDialogButtonInfo = listOf(
            DialogButtonInfo(
                label = stringResource(R.string.dong),
            )
        ),
        onDismissRequest = {
            NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
        }
    )
}
