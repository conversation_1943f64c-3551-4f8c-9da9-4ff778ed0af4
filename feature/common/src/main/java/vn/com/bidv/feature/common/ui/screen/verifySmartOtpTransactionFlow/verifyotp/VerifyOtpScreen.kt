package vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.verifyotp

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import kotlinx.coroutines.delay
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.utils.unpack
import vn.com.bidv.designsystem.component.dataentry.IBankInformation
import vn.com.bidv.designsystem.component.dataentry.IBankTextAndTextSupport
import vn.com.bidv.designsystem.component.dataentry.IBankTextAndTextSupportConfig
import vn.com.bidv.designsystem.component.dataentry.InformationType
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.feedback.otpview.IBankOtpItemInputList
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBTypography.NormalTypography.bodyBody_m
import vn.com.bidv.designsystem.theme.IBTypography.NormalTypography.captionCaption_m
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.constants.Constants
import vn.com.bidv.feature.common.navigation.NavigationHelper
import vn.com.bidv.feature.common.navigation.VerifyTransactionRoute
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow.VerifyByTypeTransactionFlowViewModel
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.viewModelFlow.sharedVerifyTransactionViewModel
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.AuthTxnType
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.ModelVerifyTransactionUI
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.VerifyMethodType
import vn.com.bidv.sdkbase.ui.HandleBackAction
import vn.com.bidv.sdkbase.utils.formatMoney
import vn.com.bidv.sdkbase.utils.toSafeInt
import vn.com.bidv.designsystem.R as RDesignsystem
import vn.com.bidv.localization.R as RLocalization

@Composable
fun VerifyOtpScreen(
    navController: NavHostController,
    modelVerifyOTPTransactionUI: ModelVerifyTransactionUI,
    secretKey: String,
    smToken: String,
) {
    val vm: VerifyOtpViewModel = hiltViewModel()
    val viewModelFlow = navController.sharedVerifyTransactionViewModel()

    val (_uiState, onEvent, _) = vm.unpack()

    HandleBackAction {
        NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
    }

    BaseScreen(
        navController = navController,
        viewModel = vm,
        backgroundColor = LocalColorScheme.current.bgMainTertiary,
        renderContent = { uiState, onEvent ->
            if (!uiState.isInitSuccess) {
                LaunchedEffect(true) {
                    onEvent(
                        VerifyOtpReducer.VerifyOtpViewEvent.OnInitTransaction(
                            modelVerifyOTPTransactionUI,
                            secretKey,
                            smToken
                        )
                    )
                }
            }
            VerifyOtpContent(uiState, onEvent, navController, viewModelFlow)
        },
        handleSideEffect = { verifyOtpViewEffect ->
            when (verifyOtpViewEffect) {
                is VerifyOtpReducer.VerifyOtpViewEffect.ShareOtpTransactionSuccess -> {
                    navController.navigate(VerifyTransactionRoute.VerifyTransactionScreenRoute(
                        data = verifyOtpViewEffect.otpVerify,
                    ))
                }

                else -> {
                    // nothing
                }
            }
        },
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(RLocalization.string.xac_thuc_giao_dich),
            onNavigationClick = { NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow) },
            onHomeClick = if (!_uiState.isLoginSuccess) {
                {
                    vn.com.bidv.sdkbase.navigation.NavigationHelper.navigationToLogin(navController)
                }
            } else null
        )
    )
}

private fun startRegenerateTimer(): Int {
    val currentInterval = System.currentTimeMillis()
    val timeRemaining =
        (Constants.TIME_ACTIVE_OTP - (currentInterval % Constants.TIME_ACTIVE_OTP)) / 1000
    return timeRemaining.toInt()
}

@Composable
private fun VerifyOtpContent(
    uiState: VerifyOtpReducer.VerifyOtpViewState,
    onEvent: (VerifyOtpReducer.VerifyOtpViewEvent) -> Unit,
    navController: NavHostController,
    viewModelFlow: VerifyByTypeTransactionFlowViewModel
) {
    if (!uiState.isInitSuccess) return

    var isShowTimeDeActiveTxn by remember { mutableStateOf(false) }

    var timeActiveOtp by remember { mutableIntStateOf(startRegenerateTimer()) }
    var numberRetryOtp by rememberSaveable { mutableIntStateOf(0) }
    LaunchedEffect(uiState.currentOtp) {
        if (numberRetryOtp < uiState.listOtp.size) {
            while (true) {
                timeActiveOtp = startRegenerateTimer()
                delay(1000)
                if (timeActiveOtp == 0) {
                    onEvent(VerifyOtpReducer.VerifyOtpViewEvent.OnGetNextOtpCode)
                    numberRetryOtp++
                    break
                }
            }
        } else {
            timeActiveOtp = 0
        }
    }
    var tempTime = (uiState.modelTransaction.timeEffectTxn - System.currentTimeMillis()) / 1000
    if (tempTime < 0) tempTime = 0
    var timeActiveTxn by remember { mutableLongStateOf(tempTime) }
    LaunchedEffect(true) {
        while (timeActiveTxn > 0) {
            delay(1000)
            timeActiveTxn -= 1
        }
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(
                top = IBSpacing.spacingM
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = IBSpacing.spacingM, end = IBSpacing.spacingM)
        ) {
            Icon(
                painter = painterResource(id = RDesignsystem.drawable.status_icon),
                contentDescription = null,
                modifier = Modifier
                    .size(64.dp)
                    .align(Alignment.CenterHorizontally),
                tint = Color.Unspecified
            )

            Spacer(modifier = Modifier.size(IBSpacing.spacingS))

            if (uiState.modelTransaction.authTxnType == AuthTxnType.FINANCIAL_TRANSACTION) {
                FinancialTransactionTitle(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.CenterHorizontally),
                    modelTransaction = uiState.modelTransaction
                )
                Spacer(modifier = Modifier.size(IBSpacing.spacingM))
                FinancialTransactionContent(uiState.modelTransaction)
            } else {
                NonFinancialTransactionTitle(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.CenterHorizontally),
                    modelTransaction = uiState.modelTransaction
                )
                Spacer(modifier = Modifier.size(IBSpacing.spacingM))
                NonFinancialTransactionContent(uiState.modelTransaction)
            }

            Spacer(modifier = Modifier.size(IBSpacing.spacingM))

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White, shape = RoundedCornerShape(IBSpacing.spacingXs))
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            top = IBSpacing.spacingS,
                            bottom = IBSpacing.spacingS,
                            start = IBSpacing.spacingM,
                            end = IBSpacing.spacingM
                        )
                ) {
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(RLocalization.string.ma_xac_thuc_otp_cho_giao_dich_duoc_thuc_hien_duoi_day),
                        style = LocalTypography.current.bodyBody_m,
                        color = LocalColorScheme.current.contentMainSecondary,
                        textAlign = TextAlign.Center
                    )
                    IBankTextAndTextSupport(
                        modifier = Modifier
                            .wrapContentWidth()
                            .align(Alignment.CenterHorizontally),
                        iBankTextAndTextSupportConfig = IBankTextAndTextSupportConfig(
                            title = stringResource(RLocalization.string.ma_otp_se_duoc_tu_dong_cap_nhat_sau),
                            highlights = "${timeActiveOtp}s",
                            titleStyle = bodyBody_m,
                            titleColor = LocalColorScheme.current.contentMainSecondary,
                            highlightsStyle = captionCaption_m,
                            highlightsColor = LocalColorScheme.current.contentBrand_01Primary,
                        )
                    )

                    Spacer(modifier = Modifier.size(IBSpacing.spacingM))
                    IBankOtpItemInputList(
                        modifier = Modifier.fillMaxWidth(),
                        otpItemNumber = 6,
                        otpText = uiState.currentOtp,
                        isFocus = false,
                        otpTextStyle = LocalTypography.current.titleTitle_m,
                    )
                }
            }
        }

        IBankActionBar(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .background(Color.White),
            isVertical = false,
            buttonPositive = DialogButtonInfo(
                label = if (uiState.modelTransaction.verifyMethodType in listOf(
                        VerifyMethodType.SCAN_QR,
                        VerifyMethodType.OTHER_DEVICE
                    )
                ) {
                    stringResource(RLocalization.string.xac_nhan)
                } else {
                    stringResource(RLocalization.string.xac_nhan_ss, timeActiveTxn)
                },
            ) {
                onEvent(VerifyOtpReducer.VerifyOtpViewEvent.OnConfirmVerifyOtpTransaction)
            },
        )

        if (timeActiveTxn <= 0) {
            if (uiState.modelTransaction.verifyMethodType == VerifyMethodType.SAME_DEVICE) {
                isShowTimeDeActiveTxn = true
            }
        }

        if (isShowTimeDeActiveTxn) {
            IBankModalConfirm(
                title = stringResource(RLocalization.string.giao_dich_het_hieu_luc),
                supportingText = stringResource(RLocalization.string.giao_dich_het_hieu_luc_vui_long_thuc_hien_lai),
                modalConfirmType = ModalConfirmType.Error,
                listDialogButtonInfo = listOf(
                    DialogButtonInfo(
                        label = stringResource(RLocalization.string.dong),
                        onClick = {
                            NavigationHelper.closeVerifyByTypeTransaction(navController, viewModelFlow)
                        }
                    )
                ),
                onDismissRequest = {
                    isShowTimeDeActiveTxn = false
                }
            )
        }
    }
}

@Composable
fun NonFinancialTransactionContent(modelTransaction: ModelVerifyTransactionUI) {
    Column(Modifier.fillMaxWidth()) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White, shape = RoundedCornerShape(IBSpacing.spacingXs))
        ) {
            Column(
                modifier = Modifier.padding(
                    top = IBSpacing.spacingS,
                    bottom = IBSpacing.spacingS,
                    start = IBSpacing.spacingM,
                    end = IBSpacing.spacingM
                )
            ) {

                modelTransaction.txnType?.let {
                    IBankInformation(
                        modifier = Modifier.fillMaxWidth(),
                        informationType = InformationType.DefaultInformation,
                        label = stringResource(RLocalization.string.loai_giao_dich),
                        dataValue = it
                    )
                }

                if (modelTransaction.totalTxn.toSafeInt() == 1 && modelTransaction.createdBy.isNotNullOrEmpty()) {
                    modelTransaction.createdBy?.let {
                        IBankInformation(
                            modifier = Modifier.fillMaxWidth(),
                            informationType = InformationType.DefaultInformation,
                            label = stringResource(RLocalization.string.nguoi_tao),
                            dataValue = it
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun FinancialTransactionContent(modelTransaction: ModelVerifyTransactionUI) {
    Column(Modifier.fillMaxWidth()) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White, shape = RoundedCornerShape(IBSpacing.spacingXs))
        ) {
            Column(
                modifier = Modifier.padding(
                    top = IBSpacing.spacingS,
                    bottom = IBSpacing.spacingS,
                    start = IBSpacing.spacingM,
                    end = IBSpacing.spacingM
                )
            ) {
                modelTransaction.txnType?.let {
                    IBankInformation(
                        modifier = Modifier.fillMaxWidth(),
                        informationType = InformationType.DefaultInformation,
                        label = stringResource(RLocalization.string.loai_giao_dich),
                        dataValue = it
                    )
                }

                if (modelTransaction.totalTxn.toSafeInt() == 1) {
                    modelTransaction.totalTxn?.let {
                        IBankInformation(
                            modifier = Modifier.fillMaxWidth(),
                            informationType = InformationType.DefaultInformation,
                            label = stringResource(RLocalization.string.so_luong_giao_dich),
                            dataValue = it
                        )
                    }
                    modelTransaction.createdBy?.let {
                        IBankInformation(
                            modifier = Modifier.fillMaxWidth(),
                            informationType = InformationType.DefaultInformation,
                            label = stringResource(RLocalization.string.nguoi_tao),
                            dataValue = it,
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun FinancialTransactionTitle(
    modifier: Modifier,
    modelTransaction: ModelVerifyTransactionUI
) {
    if ((modelTransaction.totalTxn?.toIntOrNull() ?: 0) == 1) {
        SingleFinancialTransactionTitle(modifier, modelTransaction)
    } else {
        MultiFinancialTransactionTitle(modifier, modelTransaction)
    }
}

@Composable
private fun MultiFinancialTransactionTitle(
    modifier: Modifier,
    modelTransaction: ModelVerifyTransactionUI
) {
    Column(modifier) {
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 1.dp),
            text = stringResource(RLocalization.string.so_luong_giao_dich),
            style = LocalTypography.current.bodyBody_l,
            color = LocalColorScheme.current.contentMainPrimary,
            textAlign = TextAlign.Center
        )
        Text(
            modifier = Modifier
                .testTagIBank("common_text_verify_transaction_total_txn")
                .fillMaxWidth()
                .padding(vertical = 1.dp),
            text = modelTransaction.totalTxn ?: "0",
            style = LocalTypography.current.headlineHeadline_s,
            color = LocalColorScheme.current.contentMainPrimary,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun SingleFinancialTransactionTitle(
    modifier: Modifier,
    modelTransaction: ModelVerifyTransactionUI
) {
    Column(modifier) {
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 1.dp),
            text = stringResource(RLocalization.string.tong_so_tien_giao_dich),
            style = LocalTypography.current.bodyBody_l,
            color = LocalColorScheme.current.contentMainPrimary,
            textAlign = TextAlign.Center
        )
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 1.dp),
            text = modelTransaction.totalAmount
                .formatMoney(modelTransaction.currCode, true),
            style = LocalTypography.current.headlineHeadline_s,
            color = LocalColorScheme.current.contentMainPrimary,
            textAlign = TextAlign.Center
        )
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 1.dp),
            text = modelTransaction.totalAmountText ?: "",
            style = LocalTypography.current.bodyBody_m,
            color = LocalColorScheme.current.contentMainTertiary,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun NonFinancialTransactionTitle(
    modifier: Modifier,
    modelTransaction: ModelVerifyTransactionUI
) {
    Column(modifier) {
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(RLocalization.string.so_luong_giao_dich),
            style = LocalTypography.current.bodyBody_m,
            color = LocalColorScheme.current.contentMainPrimary,
            textAlign = TextAlign.Center
        )
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = modelTransaction.totalTxn ?: "0",
            style = LocalTypography.current.headlineHeadline_s,
            color = LocalColorScheme.current.contentMainPrimary,
            textAlign = TextAlign.Center
        )
    }
}
