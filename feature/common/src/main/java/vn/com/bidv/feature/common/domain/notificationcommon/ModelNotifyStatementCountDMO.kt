package vn.com.bidv.feature.common.domain.notificationcommon

import com.google.gson.annotations.SerializedName

data class ModelNotifyStatementCountDMO(
    @SerializedName("totalNotifyUnreadBal")
    val totalNotifyUnreadBal: Long? = null,

    @SerializedName("totalNotifyUnreadTrans")
    val totalNotifyUnreadTrans: Long? = null,

    @SerializedName("totalNotifyUnreadPromo")
    val totalNotifyUnreadPromo: Long? = null,

    @SerializedName("totalNotifyUnreadSys")
    val totalNotifyUnreadSys: Long? = null,

    @SerializedName("totalNotifyUnread")
    val totalNotifyUnread: Long? = null,
) {
    fun getUnReadCount(tabNotiType: TabNotiType): Long {
        return when (tabNotiType) {
            TabNotiType.BALANCE_NOTIFICATION -> totalNotifyUnreadBal
            TabNotiType.TRANSACTION_NOTIFICATION -> totalNotifyUnreadTrans
            TabNotiType.PROMOTION_NOTIFICATION -> totalNotifyUnreadPromo
            TabNotiType.SYSTEM_NOTIFICATION -> totalNotifyUnreadSys
        } ?: 0
    }
}