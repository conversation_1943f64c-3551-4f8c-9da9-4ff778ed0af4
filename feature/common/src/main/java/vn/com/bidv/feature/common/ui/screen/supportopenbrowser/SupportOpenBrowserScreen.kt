package vn.com.bidv.feature.common.ui.screen.supportopenbrowser

import OpenBrowser
import androidx.compose.runtime.Composable
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.ui.screen.supportopenbrowser.SupportOpenBrowserReducer.SupportOpenBrowserViewEvent

@Composable
fun SupportOpenBrowserScreen(navController: NavHostController, urlType: String) {
    val vm: SupportOpenBrowserViewModel = hiltViewModel()
    BaseScreen(
        navController = navController,
        viewModel = vm,
        renderContent = { uiState, onEvent ->
            if (!uiState.isInitSuccess) {
                onEvent(SupportOpenBrowserViewEvent.InitData(urlType = urlType))
            }
            if (uiState.url.isNotEmpty()) {
                OpenBrowser(uiState.url)
                navController.popBackStack()
            }

            if (uiState.isInitSuccess && uiState.url.isEmpty()) {
                onEvent(SupportOpenBrowserViewEvent.ShowToast)
                navController.popBackStack()
            }
        },
        handleSideEffect = {},
        topAppBarConfig = TopAppBarConfig(
            isShowTopAppBar = false
        )
    )
}


