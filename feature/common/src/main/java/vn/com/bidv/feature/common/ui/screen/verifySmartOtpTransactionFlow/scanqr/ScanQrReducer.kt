package vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.scanqr

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.constants.SmartOTPError
import vn.com.bidv.feature.common.domain.data.QrCodeParseDMO

class ScanQrReducer :
    Reducer<ScanQrReducer.ScanQrViewState, ScanQrReducer.ScanQrViewEvent, ScanQrReducer.ScanQrViewEffect> {

    @Immutable
    data class ScanQrViewState(
        val numberRetryScanQR: Int = 0,
        val isInitSuccess: Boolean = false,
        val isLoginSuccess: Boolean = false
    ) : ViewState

    @Immutable
    sealed class ScanQrViewEvent : ViewEvent {
        data object OnInitScreen: ScanQrViewEvent()
        data class OnInitScreenSuccess(val isLoginSuccess: Boolean): ScanQrViewEvent()
        data class OnScanQrSuccess(val data: String): ScanQrViewEvent()
        data class OnParseQrSuccess(val data: QrCodeParseDMO, val isLoginSuccess: Boolean): ScanQrViewEvent()
        data class OnParseQrError(val errorCode: String, val errorMessage: String): ScanQrViewEvent()
        data class OnScanQrError(val error: SmartOTPError): ScanQrViewEvent()
        data class OnShareDataCreateTransaction(val data: String?): ScanQrViewEvent()
        data object OnLogout : ScanQrViewEvent()
        data object OnLogoutSuccess : ScanQrViewEvent()
    }

    @Immutable
    sealed class ScanQrViewEffect : SideEffect {
        data object InitScreen: ScanQrViewEffect()
        data class ParseQr(val data: String): ScanQrViewEffect()
        data class ParseQrSuccess(val data: QrCodeParseDMO, val isLoginSuccess: Boolean): ScanQrViewEffect(), UIEffect
        data class ParseQrError(val errorCode: String, val errorMessage: String): ScanQrViewEffect(), UIEffect
        data class ScanQrError(val error: SmartOTPError): ScanQrViewEffect(), UIEffect
        data class ShareDataCreateTransaction(val data: String?): ScanQrViewEffect()
        data object Logout : ScanQrViewEffect()
        data object LogoutSuccess : ScanQrViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: ScanQrViewState,
        event: ScanQrViewEvent,
    ): Pair<ScanQrViewState, ScanQrViewEffect?> {
        return handleScanQrViewState(previousState, event)

    }

    private fun handleScanQrViewState(
        previousState: ScanQrViewState,
        event: ScanQrViewEvent
    ): Pair<ScanQrViewState, ScanQrViewEffect?> {
        return when (event) {

            is ScanQrViewEvent.OnInitScreen -> {
                previousState to ScanQrViewEffect.InitScreen
            }

            is ScanQrViewEvent.OnInitScreenSuccess -> {
                previousState.copy(isInitSuccess = true, isLoginSuccess = event.isLoginSuccess) to null
            }

            is ScanQrViewEvent.OnScanQrSuccess -> {
                previousState to ScanQrViewEffect.ParseQr(event.data)
            }
            is ScanQrViewEvent.OnParseQrSuccess -> {
                previousState to ScanQrViewEffect.ParseQrSuccess(event.data, event.isLoginSuccess)
            }
            is ScanQrViewEvent.OnParseQrError -> {
                previousState to ScanQrViewEffect.ParseQrError(
                    errorCode = event.errorCode,
                    errorMessage = event.errorMessage
                )
            }
            is ScanQrViewEvent.OnScanQrError -> {
                previousState to ScanQrViewEffect.ScanQrError(event.error)
            }
            is ScanQrViewEvent.OnShareDataCreateTransaction -> {
                previousState to ScanQrViewEffect.ShareDataCreateTransaction(event.data)
            }

            is ScanQrViewEvent.OnLogout -> {
                previousState to ScanQrViewEffect.Logout
            }

            is ScanQrViewEvent.OnLogoutSuccess -> {
                previousState to ScanQrViewEffect.LogoutSuccess
            }
        }
    }
}
