package vn.com.bidv.feature.common.ui.screen.actionhistory

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.EmptyStateType
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.domain.data.TxnLogDMO
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.exts.toDateWithSecondsByFormatters

@Composable
fun ActionHistoryScreen(
    navController: NavHostController,
    txnId: String,
    txnCode: String
) {
    val viewModel: ActionHistoryViewModel = hiltViewModel()
    LaunchedEffect(Unit) {
        viewModel.sendEvent(
            ActionHistoryReducer.ActionHistoryEvent.GetActionLogs(txnId = txnId, txnCode = txnCode)
        )
    }
    BaseScreen(
        viewModel = viewModel,
        navController = navController,
        renderContent = { uiState, onEvent ->
            val lazyListState = rememberLazyListState()
            when {
                uiState.errorMessage != null -> {
                    Box(Modifier.fillMaxSize()) {
                        IBankEmptyState(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(top = IBSpacing.spacing3xl),
                            verticalArrangement = Arrangement.Center,
                            emptyStateType = EmptyStateType.EmptyBox,
                            supportingText = uiState.errorMessage,
                            textButton = stringResource(R.string.thu_lai),
                            onClickButton = {
                                onEvent(
                                    ActionHistoryReducer.ActionHistoryEvent.GetActionLogs(
                                        txnId = txnId,
                                        txnCode = txnCode
                                    )
                                )
                            }
                        )
                    }
                }

                uiState.actionLogs == null -> {
                    Box(Modifier.fillMaxSize())
                }

                uiState.actionLogs.isEmpty() -> {
                    Box(Modifier.fillMaxSize()) {
                        IBankEmptyState(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(top = IBSpacing.spacing3xl),
                            verticalArrangement = Arrangement.Center,
                            emptyStateType = EmptyStateType.EmptyBox,
                            supportingText = stringResource(R.string.khong_co_du_lieu),
                        )
                    }
                }

                else -> {
                    LazyColumn(
                        state = lazyListState, modifier = Modifier
                            .fillMaxSize()
                            .padding(
                                horizontal = IBSpacing.spacingM
                            )
                    ) {
                        uiState.actionLogs.let {
                            items(it.size) { index ->
                                ActionHistoryItem(it[index])
                            }
                        }
                    }
                }
            }
        },
        handleSideEffect = { },
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(R.string.lich_su_tac_dong),
        )
    )
}

@Composable
private fun ActionHistoryItem(item: TxnLogDMO) {
    val typography = LocalTypography.current
    val colorScheme = LocalColorScheme.current
    Column(modifier = Modifier.padding(vertical = IBSpacing.spacingS)) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                modifier = Modifier.weight(1f, false),
                text = item.createdBy ?: "",
                style = typography.titleTitle_s.copy(color = colorScheme.contentMainPrimary)
            )
            Text(
                modifier = Modifier.weight(1f, false),
                text = item.logTypeDesc ?: "",
                style = typography.bodyBody_s.copy(color = colorScheme.contentMainPrimary)
            )
        }
        Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
        if (!item.logNote.isNullOrEmpty()) {
            Text(
                text = item.logNote.toString(),
                style = typography.bodyBody_m.copy(color = colorScheme.contentMainPrimary)
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
        }
        if (!item.createdDate.isNullOrEmpty()) {
            Text(
                text = item.createdDate.toDateWithSecondsByFormatters(),
                style = typography.bodyBody_s.copy(color = colorScheme.contentMainPrimary)
            )
        }
        Spacer(modifier = Modifier.height(IBSpacing.spacingS))
        HorizontalDivider(
            color = colorScheme.borderMainSecondary,
            modifier = Modifier.height(1.dp)
        )
    }
}

@Composable
@Preview(showBackground = true)
fun HistoryDemo() {
    ActionHistoryItem(
        TxnLogDMO(
            createdBy = "Hello",
            createdDate = "2025-01-21T15:17:53.70505",
            logTypeDesc = "Reject",
            logNote = "Lam sao ma phai khoc"
        )
    )

}