package vn.com.bidv.feature.common.ui.screen.commonlisttransaction.model

import com.google.gson.Gson
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.RuleFilters

interface ListTransactionRuleFilter : RuleFilters {
    var search: String?

    fun getBadgeNumber(): Int

    fun <R : ListTransactionRuleFilter> copyGson(): R

    fun <R: ListTransactionRuleFilter> copySearch(search: String?): R
}
