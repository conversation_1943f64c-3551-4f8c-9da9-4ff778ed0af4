package vn.com.bidv.feature.common.constants

object Constants {
    const val MAX_NUMBER_RETRY_PIN_INPUT = 5
    const val VERIFY_OTP = "VERIFY_OTP"
    const val MAX_TIME_ACTIVE_TXN = 120 * 1000L
    const val TIME_ACTIVE_OTP = 30 * 1000L
    const val USER_PROFILE = "USER_PROFILE"
    const val FIRST_LOGIN_TIME = "FIRST_LOGIN_TIME"
    const val TRANSACTION_EXPIRED = "TRANSACTION_EXPIRED"
    const val POP_VERIFY_TRANSACTION = "POP_VERIFY_TRANSACTION"
    const val PARSE_QR_ERROR = "PARSE_QR_ERROR"
    const val VERIFY_PIN_SUCCESS = "VERIFY_PIN_SUCCESS"
    const val BASE_APPROVAL_ROUTE = "transaction_approval_main"
    const val BASE_REPORT_ROUTE = "transaction_report_main"
    const val ROUTE_KEY = "routeId"
    const val APPROVAL_ROUTE_WITH_PARAM = "$BASE_APPROVAL_ROUTE/{$ROUTE_KEY}"
    const val REPORT_ROUTE_WITH_PARAM = "$BASE_REPORT_ROUTE/{$ROUTE_KEY}"
    const val MAX_LENGTH_LIST_TRANSACTION = 40
    const val WAITING_CONVERT = "WAITING_CONVERT"
    const val M_VERIFY = "M_VERIFY"
    const val CA = "CA"
    object Biometric {
        const val BIOMETRIC_TURNON_SUCCESS = "BIOMETRIC_TURNON_SUCCESS"
        const val BIOMETRIC_TURNOFF_SUCCESS = "BIOMETRIC_TURNOFF_SUCCESS"
        const val BIOMETRIC_ACTION_SUCCESS = "BIOMETRIC_ACTION_SUCCESS"
    }

    object IBankDatabase {
        const val UTILITY_DATABASE = "iBank2db_Utility"
    }

    const val IMAGE_CROPPER_FILENAME = "image_cropped.jpeg"

    object AuthType {
        const val SMART_OTP = "SMARTOTP"
        const val CA = "CA"
        const val SMS_OTP = "SMSOTP"
        const val EMAIL_OTP = "EMAILOTP"
    }

    enum class UrlType {
        TERMS, FAQ, GUIDELINE
    }

    const val TEL_OPERATOR = "********"
    const val EMAIL_OPERATOR = "<EMAIL>"
    const val URL_TYPE = "URL_TYPE"
}