package vn.com.bidv.feature.common.ui.component

import android.app.Activity.RESULT_OK
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import com.yalantis.ucrop.UCrop
import vn.com.bidv.feature.common.constants.Constants
import vn.com.bidv.localization.R
import java.io.File

@Composable
fun IBankImageCropper(
    onImageCropped: (Uri) -> Unit,
    modifier: Modifier = Modifier,
    triggerOnBoxClick: Boolean = false,
    content: @Composable () -> Unit
) {
    val context = LocalContext.current

    var selectedImageUri by remember { mutableStateOf<Uri?>(null) }
    val destinationFile = File(context.cacheDir, Constants.IMAGE_CROPPER_FILENAME)

    // crop image
    val cropImageLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val resultUri = UCrop.getOutput(result.data!!)
            val uri = resultUri ?: Uri.EMPTY
            onImageCropped(uri)
        } else if (result.resultCode == UCrop.RESULT_ERROR) {
            if (destinationFile.exists()) {
                destinationFile.delete()
            }
        }
    }

    // select image from library device
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.PickVisualMedia()
    ) { uri: Uri? ->
        if (uri != null) {
            selectedImageUri = uri
            selectedImageUri?.let { sourceUri ->
                val destinationUri = Uri.fromFile(destinationFile)

                val options = UCrop.Options().apply {
                    setCropLabelText(context.getString(R.string.di_chuyen_va_chia_ty_le))
                    setCompressionQuality(85)
                }

                val uCropIntent = UCrop.of(sourceUri, destinationUri)
                    .withAspectRatio(1f, 1f)
                    .withMaxResultSize(512, 512)
                    .withOptions(options)
                    .getIntent(context)
                cropImageLauncher.launch(uCropIntent)
            }
        }
    }

    fun requestPermissionAndLaunch() {
        imagePickerLauncher.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly))
    }

    Box(
        modifier = if (triggerOnBoxClick) {
            modifier.clickable(
                onClick = { requestPermissionAndLaunch() }
            )
        } else {
            modifier
        }
    ) {
        if (!triggerOnBoxClick) {
            CompositionLocalProvider(
                LocalImagePicker provides { requestPermissionAndLaunch() }
            ) {
                content()
            }
        } else {
            content()
        }
    }
}

val LocalImagePicker = staticCompositionLocalOf<(() -> Unit)?> { null }