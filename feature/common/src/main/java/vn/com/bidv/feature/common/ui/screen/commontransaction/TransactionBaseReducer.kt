package vn.com.bidv.feature.common.ui.screen.commontransaction

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.MenuDMO
import vn.com.bidv.feature.common.domain.data.TransactionRouteID
import javax.inject.Inject

class TransactionBaseReducer @Inject constructor() :
    Reducer<TransactionBaseReducer.TransactionBaseState, TransactionBaseReducer.TransactionBaseEvent, TransactionBaseReducer.TransactionBaseEffect> {

    @Immutable
    data class TransactionBaseState(
        val menuData: List<MenuDMO>? = null,
        val currentRoute: TransactionRouteID? = null,
        val reloadMenu: Boolean = false
    ) : ViewState

    @Immutable
    sealed class TransactionBaseEvent : ViewEvent {
        data class GetData(val routeId: TransactionRouteID?) : TransactionBaseEvent()
        data class GetDataFail(val errorMessage: String?) : TransactionBaseEvent()
        data class GetDataSuccess(val menuData: List<MenuDMO>?) : TransactionBaseEvent()
        data class SelectedItemIndex(val currentMenu: MenuDMO) : TransactionBaseEvent()
    }

    @Immutable
    sealed class TransactionBaseEffect : SideEffect {
        data object FetData : TransactionBaseEffect()
    }

    override fun reduce(
        previousState: TransactionBaseState, event: TransactionBaseEvent
    ): Pair<TransactionBaseState, TransactionBaseEffect?> {
        return when (event) {
            is TransactionBaseEvent.GetData -> {
                previousState.copy(currentRoute = event.routeId, reloadMenu = true) to TransactionBaseEffect.FetData
            }

            is TransactionBaseEvent.GetDataSuccess -> {
                previousState.copy(
                    menuData = event.menuData,
                    currentRoute = previousState.currentRoute ?: event.menuData?.firstOrNull { !it.isHeader }?.route,
                    reloadMenu = false
                ) to null
            }

            is TransactionBaseEvent.GetDataFail -> {
                previousState.copy(menuData = null, currentRoute = null, reloadMenu = false) to null
            }

            is TransactionBaseEvent.SelectedItemIndex -> {
                previousState.copy(currentRoute = event.currentMenu.route) to null
            }
        }
    }
}