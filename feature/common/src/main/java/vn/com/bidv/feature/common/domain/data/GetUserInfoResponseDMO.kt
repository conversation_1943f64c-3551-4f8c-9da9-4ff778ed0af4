package vn.com.bidv.feature.common.domain.data

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable
import vn.com.bidv.feature.common.constants.Constants
import vn.com.bidv.feature.common.constants.TypePermission

data class GetUserInfoResponseDMO(
    @SerializedName("user")
    val user: UserResDMO? = null,

    @SerializedName("permissions")
    val permissions: List<PermissionResDMO>? = null,

    @SerializedName("defaultAcc")
    val defaultAcc: List<DefaultAccResDMO>? = null,

    @SerializedName("quickLinks")
    val quickLinks: List<String>? = null,

    @SerializedName("widgets")
    val widgets: List<String>? = null,

    @SerializedName("params")
    val params: Map<String, String>? = null,

    @SerializedName("userRole")
    private val userRole: kotlin.String? = null,

) {
    fun isAdminRole() = user?.isAdminRole()

    fun isMakerRole() = userRole?.uppercase() == "MAKER"

    fun isCheckerRole() = userRole?.uppercase() == "CHECKER"

    fun isInquirierRole() = userRole?.uppercase() == "INQUIRIER"
}

data class UserResDMO(
    /* userId */
    @SerializedName("userId")
    val userId: kotlin.Int? = null,

    /* username */
    @SerializedName("username")
    val username: kotlin.String? = null,

    @SerializedName("roleCode")
    val roleCode: String? = null,

    /* usid */
    @SerializedName("usid")
    val usid: kotlin.String? = null,

    /* fullname */
    @SerializedName("fullname")
    val fullname: kotlin.String? = null,

    /* cusId */
    @SerializedName("cusId")
    val cusId: java.math.BigDecimal? = null,

    /* cifNo */
    @SerializedName("cifNo")
    val cifNo: kotlin.String? = null,

    /* cifName */
    @SerializedName("cifName")
    val cifName: kotlin.String? = null,

    /* langCode */
    @SerializedName("langCode")
    val langCode: kotlin.String? = null,

    /* profileImage */
    @SerializedName("profileImage")
    val profileImage: kotlin.String? = null,

    /* authMethod */
    @SerializedName("authMethod")
    private val authMethod: kotlin.String? = null

) {
    fun isAdminRole() = roleCode?.uppercase() == "ADMIN"

    fun isHaveAuthMethod() = authMethod?.uppercase() == Constants.AuthType.SMART_OTP ||
            authMethod?.uppercase() == Constants.AuthType.CA
}

@Serializable
data class PermissionResDMO(
    /* code */
    @SerializedName("code")
    val code: kotlin.String? = null,

    /* type */
    @SerializedName("type")
    val type: kotlin.String? = null,

    @SerializedName("priority")
    val priority: String? = null,

    @SerializedName("priorityLevel")
    val priorityLevel: String? = null,

    /* children */
    @SerializedName("children")
    val children: List<PermissionResDMO>? = null
) {
    fun getTypePermission() = TypePermission.from(type)
}

data class DefaultAccResDMO(
    /* accNo */
    @SerializedName("accNo")
    val accNo: kotlin.String? = null,

    /* grpType */
    @SerializedName("grpType")
    val grpType: kotlin.String? = null,

    /* resrcType */
    @SerializedName("resrcType")
    val resrcType: kotlin.String? = null

)