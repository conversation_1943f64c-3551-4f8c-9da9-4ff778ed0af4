package vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.createdqr

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.designsystem.component.keyboard.KeyInput
import vn.com.bidv.feature.common.ui.screen.verifySmartOtpTransactionFlow.model.ModelVerifyTransactionUI

class CreatedQrReducer :
    Reducer<CreatedQrReducer.CreatedQrViewState, CreatedQrReducer.CreatedQrViewEvent, CreatedQrReducer.CreatedQrViewEffect> {

    @Immutable
    data class CreatedQrViewState(
        val isInitSuccess: Boolean = false,
        val modelVerifyTransactionUI: ModelVerifyTransactionUI = ModelVerifyTransactionUI(),
        val otp: String = "",
        val isFillOtp: Boolean = false
    ) : ViewState

    @Immutable
    sealed class CreatedQrViewEvent : ViewEvent {
        data class OnInitTransaction(val modelVerifyTransactionUI: ModelVerifyTransactionUI) : CreatedQrViewEvent()
        data object OnStartReceiverOtp: CreatedQrViewEvent()
        data class OnFinishQRFlow(val otpVerify: String): CreatedQrViewEvent()
        data class OnOTPChanged(val keyInput: KeyInput) : CreatedQrViewEvent()
        data class OnOTPGetFromService(val otpVerify: String) : CreatedQrViewEvent()
        data object OnOTPInputSuccess : CreatedQrViewEvent()
    }

    @Immutable
    sealed class CreatedQrViewEffect : SideEffect {
        data class StartReceiverOtp(val authId: String): CreatedQrViewEffect()
        data class FinishQRFlow(val otpVerify: String): CreatedQrViewEffect(), UIEffect
        data class OTPInputSuccess(val otp: String): CreatedQrViewEffect()
    }

    override fun reduce(
        previousState: CreatedQrViewState,
        event: CreatedQrViewEvent,
    ): Pair<CreatedQrViewState, CreatedQrViewEffect?> {
        return handleCreatedQrViewState(previousState, event)
    }

    private fun handleCreatedQrViewState(
        previousState: CreatedQrViewState,
        event: CreatedQrViewEvent
    ): Pair<CreatedQrViewState, CreatedQrViewEffect?> {
        return when (event) {
            is CreatedQrViewEvent.OnInitTransaction -> {
                previousState.copy(
                    isInitSuccess = true,
                    modelVerifyTransactionUI = event.modelVerifyTransactionUI
                ) to null
            }
            is CreatedQrViewEvent.OnFinishQRFlow -> {
                previousState to CreatedQrViewEffect.FinishQRFlow(event.otpVerify)
            }
            is CreatedQrViewEvent.OnStartReceiverOtp -> {
                previousState to CreatedQrViewEffect.StartReceiverOtp(previousState.modelVerifyTransactionUI.authId)
            }

            is CreatedQrViewEvent.OnOTPChanged -> {
                when (event.keyInput) {
                    is KeyInput.Delete -> {
                        if (previousState.otp.isNotEmpty()) {
                            previousState.copy(otp = previousState.otp.dropLast(1)) to null
                        } else previousState to null
                    }

                    is KeyInput.Number -> {
                        if (previousState.otp.length < 6) {
                            return previousState.copy(otp = previousState.otp + event.keyInput.value) to null
                        } else previousState to null
                    }

                    else -> {
                        previousState to null
                    }
                }
            }
            is CreatedQrViewEvent.OnOTPInputSuccess -> {
                previousState.copy(isFillOtp = true) to CreatedQrViewEffect.OTPInputSuccess(previousState.otp)
            }

            is CreatedQrViewEvent.OnOTPGetFromService -> {
                previousState.copy(otp = event.otpVerify) to null
            }
        }
    }
}
