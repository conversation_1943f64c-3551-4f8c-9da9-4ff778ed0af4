package vn.com.bidv.feature.common.data

import vn.com.bidv.feature.common.data.masterdata.apis.MasterDataApi
import vn.com.bidv.feature.common.data.masterdata.model.AccountDetailCriteriaDto
import vn.com.bidv.feature.common.data.masterdata.model.AccountListCriteriaDto
import vn.com.bidv.feature.common.data.masterdata.model.AuthorizedAccountListCriteriaDto
import vn.com.bidv.feature.common.data.masterdata.model.BalanceAccountDto
import vn.com.bidv.feature.common.data.masterdata.model.CategoryRequestDto
import vn.com.bidv.feature.common.data.masterdata.model.CustomerDto
import vn.com.bidv.feature.common.data.masterdata.model.DataListAccountDto
import vn.com.bidv.feature.common.data.masterdata.model.DataListBalanceAccountDto
import vn.com.bidv.feature.common.data.masterdata.model.DataListCategoryResponseDto
import vn.com.bidv.feature.common.data.masterdata.model.DataListParCurrencyDto
import vn.com.bidv.feature.common.data.masterdata.model.DataListParProductsResponeDto
import vn.com.bidv.feature.common.data.masterdata.model.InqCustomerCriteriaDto
import vn.com.bidv.feature.common.data.masterdata.model.ParProductsRequestDto
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class MasterDataRepository @Inject constructor(
    private val service: MasterDataApi
) : BaseRepository() {
    suspend fun getFinAccountList(
        accType: String?, transCode: String?
    ): NetworkResult<DataListBalanceAccountDto> = launch {
        val request =
            AccountListCriteriaDto(accType = accType.orEmpty(), transCode = transCode.orEmpty())
        service.finAccountList(request)
    }

    suspend fun getAccountDetail(
        accNo: String,
        transCode: String
    ): NetworkResult<BalanceAccountDto> = launch {
        val request = AccountDetailCriteriaDto(accNo, transCode)
        service.pmtAccountDetail(request)
    }

    suspend fun getParCurrency(): NetworkResult<DataListParCurrencyDto> = launch {
        service.getParCurrency()
    }

    suspend fun getListProductsByParentCodes(parProductsRequestDto: ParProductsRequestDto): NetworkResult<DataListParProductsResponeDto> = launch {
        service.getListProductsByParentCodes(parProductsRequestDto)
    }

    suspend fun getAuthorizedAccountList(authorizedAccountListCriteriaDto: AuthorizedAccountListCriteriaDto): NetworkResult<DataListAccountDto> = launch {
        service.authorizedAccountList(authorizedAccountListCriteriaDto)
    }

    suspend fun getCategoriesByType(type: String): NetworkResult<DataListCategoryResponseDto> =
        launch {
            service.getCategoriesByType(
                CategoryRequestDto(
                    categoryType = listOf(type),
                ),
            )
        }

    suspend fun getCustomerInfo(inqCustomerCriteriaDto: InqCustomerCriteriaDto): NetworkResult<CustomerDto> =
        launch {
            service.getCustomerInfo(inqCustomerCriteriaDto)
        }
}
