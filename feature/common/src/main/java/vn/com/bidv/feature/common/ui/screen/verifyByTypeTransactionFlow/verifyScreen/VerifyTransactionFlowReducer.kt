package vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.verifyScreen

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse

class VerifyTransactionFlowReducer :
    Reducer<VerifyTransactionFlowReducer.VerifyTransactionFlowViewState, VerifyTransactionFlowReducer.VerifyTransactionFlowViewEvent, VerifyTransactionFlowReducer.VerifyTransactionFlowViewEffect> {

    @Immutable
    data class VerifyTransactionFlowViewState(
        val isVerify: Boolean = false
    ): ViewState

    @Immutable
    sealed class VerifyTransactionFlowViewEvent : ViewEvent {
        data class DoVerifyTransactionEvent(val initResponse: InitVerifyTransactionResponse, val reqValue: String?, val type: String) :
            VerifyTransactionFlowViewEvent()

        data class  DoVerifyTransactionSuccessEvent(val data: String?) :
            VerifyTransactionFlowViewEvent()

        data class  DoVerifyTransactionFailEvent(val errorMessage: String?) : VerifyTransactionFlowViewEvent()
    }

    @Immutable
    sealed class VerifyTransactionFlowViewEffect : SideEffect {
        data class DoVerifyTransactionEffect(val initResponse: InitVerifyTransactionResponse, val reqValue: String?,val type: String) :
            VerifyTransactionFlowViewEffect()

        data class DoVerifyTransactionSuccessEffect(val data: String? = null) :
            VerifyTransactionFlowViewEffect(),
            UIEffect

        data class DoVerifyTransactionFailEffect(val errorMessage: String?) :
            VerifyTransactionFlowViewEffect(),
            UIEffect
    }

    override fun reduce(
        previousState: VerifyTransactionFlowViewState,
        event: VerifyTransactionFlowViewEvent,
    ): Pair<VerifyTransactionFlowViewState, VerifyTransactionFlowViewEffect?> {
        return when (event) {
            is VerifyTransactionFlowViewEvent.DoVerifyTransactionEvent -> {
                previousState.copy(
                    isVerify = true
                ) to VerifyTransactionFlowViewEffect.DoVerifyTransactionEffect(event.initResponse, event.reqValue, event.type)
            }
            is VerifyTransactionFlowViewEvent.DoVerifyTransactionFailEvent -> {
                previousState to VerifyTransactionFlowViewEffect.DoVerifyTransactionFailEffect(event.errorMessage)
            }
            is VerifyTransactionFlowViewEvent.DoVerifyTransactionSuccessEvent -> {
                previousState to VerifyTransactionFlowViewEffect.DoVerifyTransactionSuccessEffect(event.data)
            }
        }

    }
}
