package vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.verifyScreen

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.verifyScreen.VerifyTransactionFlowReducer.VerifyTransactionFlowViewEffect
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.verifyScreen.VerifyTransactionFlowReducer.VerifyTransactionFlowViewEvent
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.verifyScreen.VerifyTransactionFlowReducer.VerifyTransactionFlowViewState
import vn.com.bidv.localization.R
import vn.com.bidv.network.NetworkStatusCode
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class VerifyTransactionFlowViewModel @Inject constructor(
    val itemVerifyFlowScreenBuilder: Set<@JvmSuppressWildcards VerifyTransactionFlowScreenBuilder>,
) : ViewModelIBankBase<VerifyTransactionFlowViewState, VerifyTransactionFlowViewEvent, VerifyTransactionFlowViewEffect>(
    initialState = VerifyTransactionFlowViewState(),
    reducer = VerifyTransactionFlowReducer()
) {
    override fun handleEffect(
        sideEffect: VerifyTransactionFlowViewEffect,
        onResult: (VerifyTransactionFlowViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is VerifyTransactionFlowViewEffect.DoVerifyTransactionEffect -> {
                val itemVerifyFlowScreenBuilderType =
                    itemVerifyFlowScreenBuilder.find { it.type.code == sideEffect.type }
                val matchedUseCase = itemVerifyFlowScreenBuilderType?.useCase
                if (matchedUseCase != null) {
                    callDomain(
                        isListenAllError = true,
                        onSuccess = { result ->
                            onResult(
                                VerifyTransactionFlowViewEvent.DoVerifyTransactionSuccessEvent(
                                    data = result.data,
                                )
                            )
                        },
                        onFail = { error ->
                            val transAuth = sideEffect.initResponse.transAuth
                            val isSingleFinancialTransaction =
                                transAuth?.additionalInfo?.totalTrans == 1 && itemVerifyFlowScreenBuilderType.isFinancialTransaction
                            val errorMessage =
                                if (error?.errorCode == NetworkStatusCode.CONNECT_TIME_OUT && isSingleFinancialTransaction) {
                                    resourceProvider.getString(
                                        R.string.giao_dich_chua_xac_dinh_trang_thai_quy_khach_vui_long_kiem_tra_so_du_tai_khoan_va_bao_cao_giao_dich_truoc_khi_thuc_hien_lai_hoac_lien_he_hotline_19009248_de_duoc_ho_tro
                                    )
                                } else {
                                    error?.errorMessage
                                }
                            onResult(
                                VerifyTransactionFlowViewEvent.DoVerifyTransactionFailEvent(
                                    errorMessage = errorMessage
                                )
                            )
                        }
                    ) {
                       matchedUseCase.verifyTransaction(
                            initResponse = sideEffect.initResponse,
                            reqValue = sideEffect.reqValue
                        )
                    }
                }
            }

            else -> {
                //Do nothing
            }
        }
    }
}
