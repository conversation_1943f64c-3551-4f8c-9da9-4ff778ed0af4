package vn.com.bidv.feature.common.ui.screen.downloadflow

import kotlinx.coroutines.Job
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase

abstract class BaseDownloadDocumentViewModel<T> :
    ViewModelIBankBase<BaseDownloadDocumentReducer.BaseDownloadState<T>, BaseDownloadDocumentReducer.BaseDownloadEvent<T>, BaseDownloadDocumentReducer.BaseDownloadEffect<T>>(
        initialState = BaseDownloadDocumentReducer.BaseDownloadState(),
        reducer = BaseDownloadDocumentReducer()
    ) {

    override fun handleEffect(
        sideEffect: BaseDownloadDocumentReducer.BaseDownloadEffect<T>,
        onResult: (BaseDownloadDocumentReducer.BaseDownloadEvent<T>) -> Unit
    ) {
        when (sideEffect) {
            is BaseDownloadDocumentReducer.BaseDownloadEffect.FetDocumentUrl -> {
                fetchData(
                    requestDto = sideEffect.requestDto,
                    onLoadSuccess = { data ->
                        onResult(
                            BaseDownloadDocumentReducer.BaseDownloadEvent.GetDocumentUrlSuccess(
                                documentUrl = data
                            )
                        )
                    },
                    onLoadFail = { errorMessage ->
                        onResult(
                            BaseDownloadDocumentReducer.BaseDownloadEvent.GetDocumentUrlFailed(
                                errorMessage ?: ""
                            )
                        )
                    }
                )
            }
        }
    }

    protected abstract fun fetchData(
        requestDto: T? = null,
        onLoadSuccess: (data: String?) -> Unit,
        onLoadFail: (String?) -> Unit
    ): Job
}