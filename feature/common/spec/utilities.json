{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://*************", "description": "Generated server url"}], "paths": {"/utilities/userinfo/widget/update/1.0": {"post": {"tags": ["utilities"], "summary": "update widget info by user", "description": "update widget info by user", "operationId": "updateWidgetInfoByUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WidgetInfoUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/userinfo/quick-link/update/1.0": {"post": {"tags": ["utilities"], "summary": "Update QuickLink", "description": "Update User QuickLink", "operationId": "updateUserQuickLink", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserQuickLinkRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/userinfo/language/update/1.0": {"post": {"tags": ["utilities"], "summary": "Update Language", "description": "Update User Language", "operationId": "updateUserLanguage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLangUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/userinfo/default-account/update/1.0": {"post": {"tags": ["utilities"], "summary": "Update default account", "description": "Update default account", "operationId": "updateDefaultAccount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDefaultAcctUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/userinfo/avatar/upload/1.0": {"post": {"tags": ["utilities"], "summary": "upload avatar", "description": "upload avatar", "operationId": "uploadAvatar", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadAvatarRes"}}}}}}}, "/utilities/statement/periodic/update/1.0": {"post": {"tags": ["utilities"], "summary": "Update Periodic Statement", "description": "API Cập nhật thông tin danh sách sao kê từng lần", "operationId": "updatePeriodicStatement", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PeriodicStatementUpdateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/utilities/statement/periodic/list/1.0": {"post": {"tags": ["utilities"], "summary": "Get Periodic Statement List", "description": "API Lấy thông tin danh sách sao kê từng lần", "operationId": "getPeriodicStatementList", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PeriodicStatementListCriteriaDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListPeriodicStatementDto"}}}}}}}, "/utilities/statement/periodic/create/1.0": {"post": {"tags": ["utilities"], "summary": "Create Periodic Statement", "description": "API Lưu thông tin danh sách sao kê từng lần", "operationId": "createPeriodicStatement", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PeriodicStatementCreateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PeriodicStatementListCreateDto"}}}}}}}, "/utilities/statement/periodic/cancel/1.0": {"post": {"tags": ["utilities"], "summary": "Cancel Periodic Statement", "description": "API Hủy thông tin danh sách sao kê từng lần", "operationId": "cancelPeriodicStatement", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PeriodicStatementCancelDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultVoid"}}}}}}}, "/utilities/smartotp/user/sync-time/1.0": {"post": {"tags": ["utilities"], "summary": "Request to Sync time", "description": "Request to Sync time", "operationId": "syncTime", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartOtpSyncTimeRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmarOtpTimeRes"}}}}}}}, "/utilities/smartotp/user/self-lock/1.0": {"post": {"tags": ["utilities"], "summary": "Lock current User", "description": "Lock current User", "operationId": "selfLock", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/smartotp/user/request-active/1.0": {"post": {"tags": ["utilities"], "summary": "Request active smart Otp", "description": "Request active smart otp for user", "operationId": "requestActive", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartOtpRqActiveRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartOtpReqActiveRes"}}}}}}}, "/utilities/smartotp/user/request-active-retry/1.0": {"post": {"tags": ["utilities"], "summary": "Request to Retry OTP Activation for a Device", "description": "Request to Retry OTP Activation for a Device", "operationId": "requestActiveRetry", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartOtpReqActiveRetryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransUpdateBasicOtpRes"}}}}}}}, "/utilities/smartotp/user/lock/1.0": {"post": {"tags": ["utilities"], "summary": "Lock User", "description": "Lock User", "operationId": "lock", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartOtpLockRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/smartotp/user/delete/1.0": {"post": {"tags": ["utilities"], "summary": "delete User", "description": "delete User", "operationId": "deleteUser", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartOtpDeleteUserRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/smartotp/user/convert/1.0": {"post": {"tags": ["utilities"], "summary": "Convert active smartotp", "description": "Convert active smartotp", "operationId": "convertActive", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OtpConvertRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/smartotp/user/change-pin/1.0": {"post": {"tags": ["utilities"], "summary": "Request init PIN change", "description": "Request init PIN change", "operationId": "changePin", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InitChangePinRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransUpdateBasicOtpRes"}}}}}}}, "/utilities/smartotp/user/approve-pending/1.0": {"post": {"tags": ["utilities"], "summary": "Approve Trans Pending", "description": "Approve Trans with Basic OTP and waiting admin", "operationId": "approvePending", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartOtpApproveActiveRetryRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartOtpApprovePendingRes"}}}}}}}, "/utilities/smartotp/user/active/1.0": {"post": {"tags": ["utilities"], "summary": "Active smart Otp", "description": "Active smart Otp", "operationId": "active", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartOtpActiveRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartOtpActiveRes"}}}}}}}, "/utilities/smartotp/device/delete/1.0": {"post": {"tags": ["utilities"], "summary": "Delete other users on device", "description": "Delete other users on device", "operationId": "delete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartOtpDeletedRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/setting/security/get/1.0": {"post": {"tags": ["utilities"], "summary": "Get security setting info for user", "description": "Get security setting info for user", "operationId": "getSecuritySettings", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SecuritySettingResDto"}}}}}}}, "/utilities/security-question/save/1.0": {"post": {"tags": ["utilities"], "summary": "Save admin security question", "description": "Save admin security question", "operationId": "save", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SecurityQuestionSetupRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransUpdateRes"}}}}}}}, "/utilities/security-question/get/1.0": {"post": {"tags": ["utilities"], "summary": "Get security question by user", "description": "Get security question by user", "operationId": "get", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransUpdateRes"}}}}}}}, "/utilities/security-question/approve-get/1.0": {"post": {"tags": ["utilities"], "summary": "Approve Trans Get security question with OTP code", "description": "Approve Trans Get security question with OTP code", "operationId": "approveGet", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransApproveRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListSecurityQuestionAnswerDto"}}}}}}}, "/utilities/notification/register/device/1.0": {"post": {"tags": ["utilities"], "summary": "Register Device OTT", "description": "Register Device OTT", "operationId": "registerDevice", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OTTRegisterDeviceRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/notification/config/update/1.0": {"post": {"tags": ["utilities"], "summary": "API Lưu thông tin thay đổi cài đặt thông báo", "description": "API Lưu thông tin thay đổi cài đặt thông báo", "operationId": "updateConfigNotify", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserNotifyProdRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/notification/config/detail/1.0": {"post": {"tags": ["utilities"], "summary": "API Vấn tin cài đặt thông báo", "description": "API Vấn tin cài đặt thông báo", "operationId": "detailConfigNotify", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserNotifyProdResponse"}}}}}}}, "/utilities/interest-rate/link/inquiry/1.0": {"post": {"tags": ["utilities"], "summary": "<PERSON><PERSON><PERSON> vấn lãi suất tiền gửi", "description": "Deposit interest rate query", "operationId": "linkInterestRateUser", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InterestRateLinkResponse"}}}}}}}, "/utilities/forex/exchange-rates/list/1.0": {"post": {"tags": ["utilities"], "summary": "<PERSON><PERSON><PERSON> vấn tỷ giá niêm yết", "description": "Query listed exchange rate", "operationId": "listExchangeRates", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListExchangeRateResponse"}}}}}}}, "/utilities/fee/user/list/1.0": {"post": {"tags": ["utilities"], "summary": "<PERSON><PERSON><PERSON> vấn biểu phí niêm yết", "description": "Query listed fee scheduled", "operationId": "listFeeScheduledUser", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListFeePkgDtlResponse"}}}}}}}, "/utilities/fee/link/inquiry/1.0": {"post": {"tags": ["utilities"], "summary": "Truy vấn link bi<PERSON>u ph<PERSON> chung", "description": "Query link fee scheduled", "operationId": "linkFeeScheduled", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeeScheduledLinkResponse"}}}}}}}, "/utilities/auth/trans/verify-basic-otp/1.0": {"post": {"tags": ["utilities"], "summary": "Verify Basic SMS/EMAIL OTP", "description": "Verify Trans with OTP code SMS/EMAIL", "operationId": "verifyBasicOtp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransApproveRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/auth/trans/retrieve-status/1.0": {"post": {"tags": ["utilities"], "summary": "Request retrieve auth value", "description": "Request retrieve auth value", "operationId": "retrieveAuthStatus", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransRetrieveAuthValueRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransRetrieveAuthValueRes"}}}}}}}, "/utilities/auth/trans/retrieve-otp/1.0": {"post": {"tags": ["utilities"], "summary": "Request retrieve otp code", "description": "Request retrieve otp code", "operationId": "retrieveOtp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransRetrieveOtpRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransRetrieveOtpRes"}}}}}}}, "/utilities/auth/trans/push-otp/1.0": {"post": {"tags": ["utilities"], "summary": "Request push otp", "description": "Request push otp", "operationId": "pushOtp", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransPushOtpRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/auth/trans/approve/1.0": {"post": {"tags": ["utilities"], "summary": "Approve Trans", "description": "Approve Trans with OTP code", "operationId": "approveTrans", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransApproveRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/utilities/auth/qrcode/parse/1.0": {"post": {"tags": ["utilities"], "summary": "Parse QR", "description": "Parse QR", "operationId": "parseQR", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransParseQRRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QrCodeParseRes"}}}}}}}, "/utilities/auth-method/priority/update/1.0": {"post": {"tags": ["utilities"], "summary": "Request update priority auth", "description": "Request update priority auth", "operationId": "updatePriorityAuthMethod", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriorityAuthChangeRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransUpdateRes"}}}}}}}, "/utilities/admin/approval-request/verify/1.0": {"post": {"tags": ["utilities"], "summary": "Verify Trans Otp Code", "description": "Approve Trans with OTP code", "operationId": "verify", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransApproveRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminTransVerifyRes"}}}}}}}, "/utilities/admin/approval-request/search/1.0": {"post": {"tags": ["utilities"], "summary": "Search Trans Pending", "description": "Search Trans Pending", "operationId": "search", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminSearchApprovalRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataListTransRequestApprovalDto"}}}}}}}, "/utilities/admin/approval-request/reject/1.0": {"post": {"tags": ["utilities"], "summary": "Action Trans Pending", "description": "Action Trans Pending and Init Trans", "operationId": "reject", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminTransActionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminTransRejectRes"}}}}}}}, "/utilities/admin/approval-request/action/1.0": {"post": {"tags": ["utilities"], "summary": "Action Trans Pending", "description": "Action Trans Pending and Init Trans", "operationId": "action", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminTransActionRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransUpdateRes"}}}}}}}}, "components": {"schemas": {"WidgetInfoUpdateRequest": {"required": ["listWidget"], "type": "object", "properties": {"listWidget": {"type": "array", "items": {"type": "string"}}}}, "ResponseError": {"type": "object", "properties": {"errorCode": {"type": "string"}, "errorDesc": {"type": "string"}, "refVal": {"type": "object"}}, "description": "Addition Error List"}, "ResultString": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"type": "string", "description": "Data"}}}, "UserQuickLinkRequest": {"required": ["listQuickLink"], "type": "object", "properties": {"listQuickLink": {"type": "array", "items": {"type": "string"}}}}, "UserLangUpdateRequest": {"required": ["lang"], "type": "object", "properties": {"lang": {"maxLength": 5, "minLength": 5, "type": "string", "description": "language user", "example": "vi_vn"}}, "description": "Request to update Language Code"}, "UserDefaultAcctUpdateRequest": {"required": ["accType", "acctNo", "enable", "grpType"], "type": "object", "properties": {"acctNo": {"maxLength": 20, "minLength": 0, "type": "string", "description": "Số tài <PERSON>n"}, "accType": {"type": "string", "description": "Loại tài k<PERSON>n : DDA/CD/LN"}, "grpType": {"type": "string", "description": "Nhóm tài k<PERSON>n Tài chính/ Phi tài chính : ACC_FIN/ACC_INQ"}, "enable": {"type": "boolean", "description": "<PERSON><PERSON><PERSON>t lập/ hủy thiết lập TK mặc định : true/false"}}}, "ResultUploadAvatarRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/UploadAvatarRes"}}}, "UploadAvatarRes": {"type": "object", "properties": {"avatarUrl": {"type": "string", "description": "avatar url", "example": "/avatar/123.png"}}, "description": "Data"}, "PeriodicStatementUpdateDto": {"required": ["stmtAcc", "stmtFile", "stmtFreq"], "type": "object", "properties": {"stmtAcc": {"type": "string"}, "stmtFreq": {"pattern": "DD|WW|MM", "type": "string"}, "stmtFile": {"pattern": "PDF|EXCEL", "type": "string"}}}, "ResultVoid": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"type": "object", "description": "Data"}}}, "Filter": {"type": "object", "properties": {"operator": {"type": "string", "description": "ct/ eq/ neq/ gt/ gte/ lt/ lte"}, "field": {"type": "string"}, "value": {"type": "string"}}}, "Order": {"type": "object", "properties": {"field": {"type": "string"}, "direction": {"type": "string"}}}, "Page": {"type": "object", "properties": {"pageSize": {"minimum": 1, "type": "integer", "description": "Row number/ page, min = 1", "format": "int32"}, "pageNum": {"minimum": 1, "type": "integer", "description": "Page index (start from 1), min = 1", "format": "int32"}, "getTotal": {"type": "boolean", "description": "Get total record size flag"}}}, "PeriodicStatementListCriteriaDto": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "keyword": {"type": "string", "description": "keyword filter"}}}, "DataListPeriodicStatementDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/PeriodicStatementDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "PeriodicStatementDto": {"type": "object", "properties": {"stmtAcc": {"type": "string"}, "stmtFreq": {"type": "string"}, "stmtFreqDesc": {"type": "string"}, "stmtFile": {"type": "string"}}, "description": "List data"}, "ResultListPeriodicStatementDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListPeriodicStatementDto"}}}, "PeriodicStatementCreateDto": {"required": ["stmtAccs", "stmtFile", "stmtFreq"], "type": "object", "properties": {"stmtAccs": {"type": "array", "items": {"type": "string"}}, "stmtFreq": {"pattern": "DD|WW|MM", "type": "string"}, "stmtFile": {"pattern": "PDF|EXCEL", "type": "string"}}}, "PeriodicStatementListCreateDto": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}}, "description": "Data"}, "ResultPeriodicStatementListCreateDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/PeriodicStatementListCreateDto"}}}, "PeriodicStatementCancelDto": {"required": ["stmtAccs"], "type": "object", "properties": {"stmtAccs": {"type": "array", "items": {"type": "string"}}}}, "SmartOtpSyncTimeRequest": {"required": ["deviceId", "smToken", "userId"], "type": "object", "properties": {"userId": {"type": "integer", "description": "user id", "format": "int64"}, "deviceId": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Unique identifier of the device", "example": "abc12345"}, "smToken": {"type": "string", "description": "token smartotp", "example": "abc12345"}}, "description": "Request to delete user smartotp"}, "ResultSmarOtpTimeRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/SmarOtpTimeRes"}}}, "SmarOtpTimeRes": {"type": "object", "properties": {"time": {"type": "integer", "description": "time", "format": "int64", "example": 123455}}, "description": "Data"}, "SmartOtpRqActiveRequest": {"required": ["deviceId"], "type": "object", "properties": {"deviceId": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Unique identifier of the device", "example": "abc12345"}, "os": {"maxLength": 50, "minLength": 1, "type": "string", "description": "OS", "example": "IOS"}, "osVersion": {"maxLength": 20, "minLength": 1, "type": "string", "description": "OS Version", "example": "17.1.1"}, "deviceModel": {"maxLength": 255, "minLength": 1, "type": "string", "description": "Device Model", "example": "MD123456"}}, "description": "Request ReqActivate OTP for a device"}, "ResultSmartOtpReqActiveRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/SmartOtpReqActiveRes"}}}, "SmartOtpReqActiveRes": {"type": "object", "properties": {"resendOtp": {"type": "integer", "description": "Resend Otp time", "format": "int64", "example": 20}, "countTime": {"type": "integer", "description": "count time", "format": "int64", "example": 60}, "content": {"type": "string", "description": "text view onscreen user", "example": "<PERSON>ui long nhap ma kich hoat da gui den nguoi dung abc***gmail.com"}, "secretKey": {"type": "string", "description": "Secret key", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}, "smToken": {"type": "string", "description": "smartotp token", "example": "eyjlk.67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b.yumknnglkdfldkf"}}, "description": "Data"}, "SmartOtpReqActiveRetryRequest": {"required": ["deviceId"], "type": "object", "properties": {"deviceId": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Unique identifier of the device", "example": "abc12345"}}, "description": "Request Retry Activate OTP for a device"}, "ResultTransUpdateBasicOtpRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TransUpdateBasicOtpRes"}}}, "TransUpdateBasicOtpRes": {"type": "object", "properties": {"authType": {"type": "string", "description": "EMAILOTP|SMSOTP", "example": "EMAILOTP"}, "authId": {"type": "string", "description": "auth Id", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}, "transKey": {"type": "string", "description": "transKey", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}, "resendOtp": {"type": "integer", "description": "Resend Otp time", "format": "int64", "example": 20}, "countTime": {"type": "integer", "description": "count time", "format": "int64", "example": 60}, "content": {"type": "string", "description": "text view onscreen user", "example": "<PERSON>ui long nhap ma kich hoat da gui den nguoi dung abc***gmail.com"}}, "description": "Data"}, "SmartOtpLockRequest": {"required": ["deviceId", "smToken", "userId"], "type": "object", "properties": {"userId": {"type": "integer", "description": "user id", "format": "int64"}, "deviceId": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Unique identifier of the device", "example": "abc12345"}, "smToken": {"type": "string", "description": "token smartotp", "example": "abc12345"}}, "description": "Request to lock user smartotp"}, "SmartOtpDeleteUserRequest": {"required": ["deviceId", "smToken", "userId"], "type": "object", "properties": {"userId": {"type": "integer", "description": "user id", "format": "int64"}, "deviceId": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Unique identifier of the device", "example": "abc12345"}, "smToken": {"type": "string", "description": "token smartotp", "example": "abc12345"}}, "description": "Request to delete user smartotp"}, "OtpConvertRequest": {"type": "object", "properties": {"otp": {"maxLength": 6, "minLength": 6, "type": "string", "description": "otp code", "example": "111111"}}}, "InitChangePinRequest": {"required": ["deviceId"], "type": "object", "properties": {"deviceId": {"type": "string", "description": "deviceId", "example": "123456"}}, "description": "Pin Change Request"}, "SmartOtpApproveActiveRetryRequest": {"required": ["authValue", "deviceId", "transKey"], "type": "object", "properties": {"transKey": {"type": "string", "description": "trans key", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}, "authValue": {"type": "string", "description": "otpcode | signature", "example": "000000"}, "deviceId": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Unique identifier of the device", "example": "abc12345"}}, "description": "Request Retry Activate OTP for a device"}, "ResultSmartOtpApprovePendingRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/SmartOtpApprovePendingRes"}}}, "SmartOtpApprovePendingRes": {"type": "object", "properties": {"authType": {"type": "string", "description": "EMAILOTP|SMSOTP", "example": "EMAILOTP"}, "transKey": {"type": "string", "description": "transKey", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}, "hotline": {"type": "string", "description": "Hotline", "example": "0123456789"}, "waitTime": {"type": "integer", "description": "time: hours", "format": "int64", "example": 12}}, "description": "Data"}, "SmartOtpActiveRequest": {"required": ["deviceId", "deviceModel", "os", "osVersion", "otp"], "type": "object", "properties": {"deviceId": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Unique identifier of the device", "example": "abc12345"}, "os": {"maxLength": 50, "minLength": 1, "type": "string", "description": "OS", "example": "IOS"}, "osVersion": {"maxLength": 20, "minLength": 1, "type": "string", "description": "OS Version", "example": "17.1.1"}, "deviceModel": {"maxLength": 255, "minLength": 1, "type": "string", "description": "Device Model", "example": "MD123456"}, "fcmId": {"maxLength": 255, "minLength": 0, "type": "string", "description": "FCM ID", "example": "123456"}, "otp": {"maxLength": 6, "minLength": 6, "type": "string", "description": "otp code", "example": "123456"}}, "description": "Request Activate OTP for a device"}, "ResultSmartOtpActiveRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/SmartOtpActiveRes"}}}, "SmartOtpActiveRes": {"type": "object", "properties": {"secretKey": {"type": "string", "description": "Secret key", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}, "smToken": {"type": "string", "description": "smartotp token", "example": "eyjlk.67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b.yumknnglkdfldkf"}}, "description": "Data"}, "SmartOtpDeletedRequest": {"type": "object", "properties": {"userId": {"type": "integer", "description": "user id", "format": "int64", "example": 123456}}, "description": "Request to delete SmartOtp Device"}, "ResultSecuritySettingResDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/SecuritySettingResDto"}}}, "SecQnSettingDto": {"type": "object", "properties": {"enable": {"type": "boolean"}}}, "SecuritySettingResDto": {"type": "object", "properties": {"smartOtp": {"$ref": "#/components/schemas/SmartOtpSettingDto"}, "userAuthDto": {"$ref": "#/components/schemas/UserAuthDto"}, "secQnSettingDto": {"$ref": "#/components/schemas/SecQnSettingDto"}}, "description": "Data"}, "SmartOtpSettingDto": {"type": "object", "properties": {"status": {"type": "string"}, "statusName": {"type": "string"}, "deviceModel": {"type": "string"}, "deviceId": {"type": "string"}, "deviceIdActiveSmartOTP": {"type": "boolean"}, "enableReRegister": {"type": "boolean"}, "enableChangePin": {"type": "boolean"}}}, "UserAuthDto": {"type": "object", "properties": {"types": {"type": "array", "items": {"type": "string"}}, "priorityType": {"type": "string"}}}, "SecurityQuestionAnswerDto": {"required": ["answer", "questionCode"], "type": "object", "properties": {"questionCode": {"type": "string"}, "answer": {"type": "string"}}}, "SecurityQuestionSetupRequestDto": {"required": ["securityQuestions"], "type": "object", "properties": {"securityQuestions": {"maxItems": 3, "minItems": 3, "type": "array", "items": {"$ref": "#/components/schemas/SecurityQuestionAnswerDto"}}}}, "QrTransGroupDto": {"type": "object", "properties": {"ccy": {"type": "string", "description": "CCY", "example": "VND"}, "amount": {"type": "string", "description": "amount", "example": "10000"}, "amountText": {"type": "string", "description": "amountText", "example": "Mot nghin dong"}, "total": {"type": "string", "description": "total", "example": "10"}}, "description": "Group trans: ccy amount total"}, "QrTransRes": {"type": "object", "properties": {"type": {"type": "string", "description": "TYPE SINGLE|MULTI", "example": "SINGLE"}, "productCode": {"type": "string", "description": "product Code", "example": "CTT-TTCN"}, "subProductCode": {"type": "string", "description": "sub product Code", "example": "CTT-TTCN-CTK"}, "authId": {"type": "string", "description": "auth Id", "example": "f890a732-4c73-4d06-8567-61a098c6d787"}, "userId": {"type": "string", "description": "userId", "example": "123455"}, "totalTrans": {"type": "integer", "description": "total trans", "format": "int32", "example": 10}, "trans": {"type": "array", "description": "Group trans: ccy amount total", "items": {"$ref": "#/components/schemas/QrTransGroupDto"}}, "createdBy": {"type": "string", "description": "createBy"}, "qrType": {"type": "string", "description": "type QR", "example": "BID_TRANS_OTP|VIETQR"}, "productName": {"type": "string", "description": "product Name", "example": "CTT-TTCN"}, "subProductName": {"type": "string", "description": "subProduct Name", "example": "CTT-TTCN"}}, "description": "additional Info trans"}, "ResultTransUpdateRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TransUpdateRes"}}}, "TransUpdateRes": {"type": "object", "properties": {"authType": {"type": "string", "description": "SMARTOTP|CA", "example": "SMARTOTP"}, "isSameDevice": {"type": "string", "description": "Y|N", "example": "Y"}, "transKey": {"type": "string", "description": "transKey", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}, "qrCode": {"type": "string", "description": "qr code", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}, "expTime": {"type": "string", "description": "exp time seconds", "example": "120"}, "userId": {"type": "integer", "description": "ID of user", "format": "int64", "example": 12345}, "authId": {"type": "string", "description": "auth Id", "example": "f890a732-4c73-4d06-8567-61a098c6d787"}, "additionalInfo": {"$ref": "#/components/schemas/QrTransRes"}}, "description": "Data"}, "TransApproveRequest": {"required": ["authValue", "transKey"], "type": "object", "properties": {"transKey": {"type": "string", "description": "trans key", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}, "authValue": {"type": "string", "description": "otpcode | signature", "example": "000000"}}, "description": "Trans Verify Request"}, "DataListSecurityQuestionAnswerDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/SecurityQuestionAnswerDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListSecurityQuestionAnswerDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListSecurityQuestionAnswerDto"}}}, "OTTRegisterDeviceRequest": {"required": ["deviceId", "deviceModel", "lang", "os", "osVersion"], "type": "object", "properties": {"deviceId": {"maxLength": 50, "minLength": 1, "type": "string", "description": "Unique identifier of the device", "example": "abc12345"}, "os": {"maxLength": 50, "minLength": 1, "type": "string", "description": "OS", "example": "IOS"}, "osVersion": {"maxLength": 20, "minLength": 1, "type": "string", "description": "OS Version", "example": "17.1.1"}, "deviceModel": {"maxLength": 255, "minLength": 1, "type": "string", "description": "Device Model", "example": "MD123456"}, "fcmId": {"maxLength": 255, "minLength": 0, "type": "string", "description": "FCM ID", "example": "123456"}, "lang": {"maxLength": 5, "minLength": 2, "type": "string", "description": "language user", "example": "vi"}}, "description": "Register Device OTT"}, "UserNotifyProdRequest": {"type": "object", "properties": {"isBalanceNotificationEnabled": {"type": "boolean"}, "transNotificationMethod": {"type": "string"}, "resultNotificationMethod": {"type": "string"}}}, "ResultUserNotifyProdResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/UserNotifyProdResponse"}}}, "UserNotifyProdResponse": {"type": "object", "properties": {"isBalanceNotificationEnabled": {"type": "boolean"}, "transNotificationMethod": {"type": "string"}, "resultNotificationMethod": {"type": "string"}}, "description": "Data"}, "InterestRateLinkResponse": {"type": "object", "properties": {"redirectUrl": {"type": "string"}}, "description": "Data"}, "ResultInterestRateLinkResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/InterestRateLinkResponse"}}}, "DataListExchangeRateResponse": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/ExchangeRateResponse"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ExchangeRateResponse": {"type": "object", "properties": {"jfxCod": {"type": "string"}, "cash": {"type": "string"}, "vbidBu": {"type": "string"}, "vbidSe": {"type": "string"}}, "description": "List data"}, "ResultListExchangeRateResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListExchangeRateResponse"}}}, "DataListFeePkgDtlResponse": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/FeePkgDtlResponse"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "FeeConditionResponse": {"type": "object", "properties": {"feeType": {"type": "string"}, "feeValue": {"type": "string"}, "feeMin": {"type": "string"}, "feeMax": {"type": "string"}, "thresholdAmount": {"type": "string"}}}, "FeePkgDtlResponse": {"type": "object", "properties": {"feeCode": {"type": "string"}, "feeName": {"type": "string"}, "vndFeePkgDtl": {"$ref": "#/components/schemas/FeeConditionResponse"}, "usdFeePkgDtl": {"$ref": "#/components/schemas/FeeConditionResponse"}}, "description": "List data"}, "ResultListFeePkgDtlResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListFeePkgDtlResponse"}}}, "FeeScheduledLinkResponse": {"type": "object", "properties": {"redirectUrl": {"type": "string"}}, "description": "Data"}, "ResultFeeScheduledLinkResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/FeeScheduledLinkResponse"}}}, "TransRetrieveAuthValueRequest": {"required": ["authId"], "type": "object", "properties": {"authId": {"type": "string", "description": "ID xac thuc giao dich", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}}, "description": "Retrieve Auth Request"}, "ResultTransRetrieveAuthValueRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TransRetrieveAuthValueRes"}}}, "TransRetrieveAuthValueRes": {"type": "object", "properties": {"authValue": {"type": "string", "description": "auth value"}, "status": {"type": "string", "description": "status value"}}, "description": "Data"}, "TransRetrieveOtpRequest": {"required": ["authId"], "type": "object", "properties": {"authId": {"type": "string", "description": "ID xac thuc giao dich", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}}, "description": "Retrieve Otp Request"}, "ResultTransRetrieveOtpRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/TransRetrieveOtpRes"}}}, "TransRetrieveOtpRes": {"type": "object", "properties": {"otp": {"type": "string", "description": "otp code", "example": "000000"}}, "description": "Data"}, "TransPushOtpRequest": {"required": ["authId", "deviceId", "otp", "smtoken"], "type": "object", "properties": {"authId": {"type": "string", "description": "ID xac thuc giao dich", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}, "otp": {"type": "string", "description": "otp Code", "example": "000000"}, "smtoken": {"type": "string", "description": "smartotp token", "example": "eyjlk.67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b.yumknnglkdfldkf"}, "deviceId": {"type": "string", "description": "device Id", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}}, "description": "Push Otp Request"}, "TransParseQRRequest": {"required": ["data"], "type": "object", "properties": {"data": {"type": "string", "description": "text QR", "example": "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"}}, "description": "Parse QR Request"}, "QrCodeParseRes": {"type": "object", "properties": {"navigateId": {"type": "string", "description": "navigate ID", "example": "1@"}, "externalInfo": {"type": "string", "description": "QR External data", "example": "json value"}, "additionalInfo": {"$ref": "#/components/schemas/QrTransRes"}}, "description": "Data"}, "ResultQrCodeParseRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/QrCodeParseRes"}}}, "PriorityAuthChangeRequest": {"required": ["authMethod"], "type": "object", "properties": {"authMethod": {"type": "string", "description": "PTXT priority", "example": "SMARTOTP"}, "deviceId": {"type": "string", "description": "<PERSON>ce Id", "example": "1234567890"}}, "description": "PriorityAuth Change Request"}, "AdminTransVerifyRes": {"type": "object", "properties": {"total": {"type": "integer", "description": "total record", "format": "int32"}, "totalError": {"type": "integer", "description": "total error", "format": "int32"}, "totalSuccess": {"type": "integer", "description": "total Success", "format": "int32"}, "errors": {"type": "array", "description": "Y|N", "items": {"$ref": "#/components/schemas/TransRequestApprovalDto"}}, "transInfo": {"$ref": "#/components/schemas/TransRequestApprovalDto"}}, "description": "Data"}, "ResultAdminTransVerifyRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/AdminTransVerifyRes"}}}, "TransRequestApprovalDto": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "fullName": {"type": "string"}, "roleGroup": {"type": "string"}, "phoneNumber": {"type": "string"}, "otpEmail": {"type": "string"}, "requestType": {"type": "string"}, "createdDate": {"type": "string"}, "createdBy": {"type": "string"}, "errorDesc": {"type": "string"}, "errorCode": {"type": "string"}, "userName": {"type": "string"}}}, "AdminSearchApprovalRequest": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/Filter"}}, "page": {"$ref": "#/components/schemas/Page"}, "searchTerm": {"type": "string"}}}, "DataListTransRequestApprovalDto": {"type": "object", "properties": {"items": {"type": "array", "description": "List data", "items": {"$ref": "#/components/schemas/TransRequestApprovalDto"}}, "total": {"type": "integer", "description": "Total size", "format": "int64"}}, "description": "Data"}, "ResultListTransRequestApprovalDto": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/DataListTransRequestApprovalDto"}}}, "AdminTransActionRequest": {"required": ["actionCode", "ids"], "type": "object", "properties": {"ids": {"type": "array", "description": "<PERSON><PERSON> s<PERSON>ch <PERSON> giao d<PERSON>ch", "items": {"type": "string", "description": "<PERSON><PERSON> s<PERSON>ch <PERSON> giao d<PERSON>ch"}}, "actionCode": {"type": "string", "description": "APPROVE | REJECT"}, "reason": {"type": "string", "description": "<PERSON>ý do từ chối"}, "deviceId": {"type": "string", "description": "<PERSON>ce Id", "example": "1234567890"}}, "description": "Trans Approval Request"}, "AdminTransRejectRes": {"type": "object", "properties": {"total": {"type": "integer", "description": "total record", "format": "int32"}, "totalError": {"type": "integer", "description": "total error", "format": "int32"}, "totalSuccess": {"type": "integer", "description": "total Success", "format": "int32"}, "transInfo": {"$ref": "#/components/schemas/TransRequestApprovalDto"}}, "description": "Data"}, "ResultAdminTransRejectRes": {"type": "object", "properties": {"status": {"type": "integer", "description": "Result status", "format": "int32"}, "code": {"type": "string", "description": "Result code detail"}, "message": {"type": "string", "description": "Result code description"}, "errors": {"type": "array", "description": "Addition Error List", "items": {"$ref": "#/components/schemas/ResponseError"}}, "traceId": {"type": "string", "description": "Trace ID"}, "data": {"$ref": "#/components/schemas/AdminTransRejectRes"}}}}}, "tags": ["utilities"], "formated": "1"}