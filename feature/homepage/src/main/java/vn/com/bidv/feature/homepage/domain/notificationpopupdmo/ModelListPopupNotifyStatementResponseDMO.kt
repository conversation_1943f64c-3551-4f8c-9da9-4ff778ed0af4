package vn.com.bidv.feature.homepage.domain.notificationpopupdmo

import com.google.gson.annotations.SerializedName

data class ModelListPopupNotifyStatementResponseDMO(
    @SerializedName("items")
    val items: List<ModelPopupNotifyStatementResponseDMO>? = null,

    @SerializedName("total")
    val total: Long? = null,

    var listNotificationPopup: List<ModelNotificationPopupDMO>?
)

data class ModelPopupNotifyStatementResponseDMO(
    @SerializedName("id")
    val id: Long? = null,

    @SerializedName("cusTemplateId")
    val cusTemplateId: Long? = null,

    @SerializedName("title")
    val title: String? = null,

    @SerializedName("content")
    val content: String? = null,

    @SerializedName("collectFeedback")
    val collectFeedback: String? = null,

    @SerializedName("priorityLevel")
    val priorityLevel: Int? = null,

    @SerializedName("additionalInfo")
    val additionalInfo: String? = null
)
