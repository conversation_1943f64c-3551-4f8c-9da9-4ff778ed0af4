package vn.com.bidv.feature.homepage.ui.avatarpicker

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.homepage.domain.AvatarUseCase
import vn.com.bidv.feature.homepage.ui.avatarpicker.AvatarPickerReducer.AvatarPickerViewEffect
import vn.com.bidv.feature.homepage.ui.avatarpicker.AvatarPickerReducer.AvatarPickerViewEvent
import vn.com.bidv.feature.homepage.ui.avatarpicker.AvatarPickerReducer.AvatarPickerViewState
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class AvatarPickerViewModel @Inject constructor(
    private val avatarUseCase: AvatarUseCase,
    private val userUseCase: UserInfoUseCase,
) : ViewModelIBankBase<AvatarPickerViewState, AvatarPickerViewEvent, AvatarPickerViewEffect>(
    initialState = AvatarPickerViewState(),
    reducer = AvatarPickerReducer()
) {
    override fun handleEffect(
        sideEffect: AvatarPickerViewEffect,
        onResult: (AvatarPickerViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is AvatarPickerViewEffect.OnInitData -> {
                callDomain(
                    showLoadingIndicator = false,
                    onSuccess = { result ->
                        val userInfo = result.data?.user
                        onResult(
                            AvatarPickerViewEvent.OnInitDataSuccess(
                                userInfo?.profileImage
                            )
                        )
                    },
                ) {
                    userUseCase.getUserInfoFromStorage()
                }
            }

            is AvatarPickerViewEffect.ChangeAvatar -> {
                callDomain(
                    onSuccess = { result ->
                        val avatarUrl = result.data?.avatarUrl
                        // Update url avatar in storage
                        userUseCase.changeAvatarUserInfoFromStorage(avatarUrl)
                        // Update avatar in home page
                        viewModelScope.launch(Dispatchers.Default) {
                            avatarUseCase.reloadAvatarInHomePage()
                        }
                        // Update avatar in side effect
                        onResult(
                            AvatarPickerViewEvent.OnChangeAvatarSuccess(
                                avatarUrl
                            )
                        )
                        showSnackBar(
                            IBankSnackBarInfo(
                                message = resourceProvider.getString(R.string.thay_doi_hinh_dai_dien_thanh_cong),
                                primaryButtonText = resourceProvider.getString(
                                    R.string.dong
                                )
                            )
                        )
                    },
                ) {
                    avatarUseCase.updateAvatar(sideEffect.imageUri)
                }
            }
        }
    }
}
