package vn.com.bidv.feature.homepage.ui.paymentaccountdetails

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.common.extenstion.isNull
import vn.com.bidv.feature.homepage.constants.HomePageConstants.ACCESS_FORBIDDEN_CODE
import vn.com.bidv.feature.homepage.domain.PaymentAccountDetailsUseCase
import vn.com.bidv.feature.homepage.ui.paymentaccountdetails.PaymentAccountDetailsReducer.PaymentAccountDetailsViewEffect
import vn.com.bidv.feature.homepage.ui.paymentaccountdetails.PaymentAccountDetailsReducer.PaymentAccountDetailsViewEvent
import vn.com.bidv.feature.homepage.ui.paymentaccountdetails.PaymentAccountDetailsReducer.PaymentAccountDetailsViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class PaymentAccountDetailsViewModel @Inject constructor(
    private val paymentAccountDetailsUseCase: PaymentAccountDetailsUseCase
) : ViewModelIBankBase<PaymentAccountDetailsViewState, PaymentAccountDetailsViewEvent, PaymentAccountDetailsViewEffect>(
    initialState = PaymentAccountDetailsViewState(),
    reducer = PaymentAccountDetailsReducer()
) {
    override fun handleEffect(
        sideEffect: PaymentAccountDetailsViewEffect,
        onResult: (PaymentAccountDetailsViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is PaymentAccountDetailsViewEffect.GetFinAcctDetailViewEffect -> {
                callDomain(
                    isListenAllError = true,
                    showLoadingIndicator = false,
                    onSuccess = {
                        onResult(
                            PaymentAccountDetailsViewEvent.GetFinAcctDetailSuccess(finAccDetail = it.data)
                        )
                    },
                    onFail = {
                        if (it?.isHttpError(ACCESS_FORBIDDEN_CODE) == true) {
                            onResult(
                                PaymentAccountDetailsViewEvent.GetFinAcctDetailFailCif
                            )
                        } else {
                            onResult(
                                PaymentAccountDetailsViewEvent.GetFinAcctDetailFail(messageError = it?.errorMessage, false)
                            )
                        }
                    }
                ) {
                    paymentAccountDetailsUseCase.getAccountDetail()
                }
            }

            is PaymentAccountDetailsViewEffect.GetDataAccount -> {
                callDomain(
                    isListenAllError = true,
                    showLoadingIndicator = false,
                    onSuccess = {
                        onResult(
                            PaymentAccountDetailsViewEvent.GetListAccountSuccess(listAccount = it.data?.items
                                ?: emptyList(),
                                currentAccount = it.data?.items?.find { item -> item.accountNo == sideEffect.finAccDetail?.accountNo }
                            )
                        )
                    },
                    onFail = {
                        onResult(
                            PaymentAccountDetailsViewEvent.GetListAccountFail
                        )
                    }
                ) {
                    paymentAccountDetailsUseCase.getAuthorizedAccountList()
                }
            }

            is PaymentAccountDetailsViewEffect.UpdateAccountDefault -> {
                if (sideEffect.newAccount == sideEffect.currentAccount || sideEffect.newAccount.isNull()) {
                    return
                }
                callDomain(
                    isListenAllError = true,
                    showLoadingIndicator = true,
                    onSuccess = {
                        onResult(
                            PaymentAccountDetailsViewEvent.SetDefaultAccountSuccess
                        )
                    },
                    onFail = {
                        showPopupError(it?.errorMessage)
                    }
                ) {
                    paymentAccountDetailsUseCase.setDefaultAccount(
                        accNo = sideEffect.newAccount?.accountNo.orEmpty(),
                    )
                }
            }

            is PaymentAccountDetailsViewEffect.GetAccountBalanceViewEffect -> {
                callDomain(
                    isListenAllError = true,
                    showLoadingIndicator = false,
                    onSuccess = {
                        onResult(
                            PaymentAccountDetailsViewEvent.GetAccountBalanceSuccess(
                                accountBalance = it.data
                            )
                        )
                    },
                    onFail = {
                        if (it?.isHttpError(ACCESS_FORBIDDEN_CODE) == true) {
                            onResult(
                                PaymentAccountDetailsViewEvent.GetFinAcctDetailFailCif
                            )
                        } else {
                            onResult(
                                PaymentAccountDetailsViewEvent.GetFinAcctDetailFail(messageError = it?.errorMessage, true)
                            )
                        }
                    }
                ) {
                    paymentAccountDetailsUseCase.getAccountBalance(sideEffect.accountNo)
                }
            }
        }
    }
}
