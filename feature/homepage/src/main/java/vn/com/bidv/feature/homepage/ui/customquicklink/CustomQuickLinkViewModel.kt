package vn.com.bidv.feature.homepage.ui.customquicklink

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.homepage.domain.CustomQuickLinkUseCase
import vn.com.bidv.feature.homepage.ui.customquicklink.CustomQuickLinkReducer.CustomQuickLinkViewEffect
import vn.com.bidv.feature.homepage.ui.customquicklink.CustomQuickLinkReducer.CustomQuickLinkViewEvent
import vn.com.bidv.feature.homepage.ui.customquicklink.CustomQuickLinkReducer.CustomQuickLinkViewState
import vn.com.bidv.feature.homepage.ui.service.modelUI.removeDefaultModel
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class CustomQuickLinkViewModel @Inject constructor(
    private val userInfoUseCase: UserInfoUseCase,
    private val customQuickLinkUseCase: CustomQuickLinkUseCase,
) : ViewModelIBankBase<CustomQuickLinkViewState, CustomQuickLinkViewEvent, CustomQuickLinkViewEffect>(
    initialState = CustomQuickLinkViewState(),
    reducer = CustomQuickLinkReducer()
) {
    override fun handleEffect(
        sideEffect: CustomQuickLinkViewEffect,
        onResult: (CustomQuickLinkViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is CustomQuickLinkViewEffect.GetPermissionResFromStorage -> {
                onResult(
                    CustomQuickLinkViewEvent.GetServicesFromStorageSuccess(
                        listQuickLink = userInfoUseCase.getQuickLinks().removeDefaultModel().take(5),
                        listChildrenService = customQuickLinkUseCase.getListChildrenService(
                            userInfoUseCase.getPermissionRes(),
                            userInfoUseCase.getQuickLinks()
                        )
                    )
                )
            }

            is CustomQuickLinkViewEffect.GetDataSearch -> {
                onResult(
                    CustomQuickLinkViewEvent.GetSearchListChildrenService(
                        listSearchChildrenService = customQuickLinkUseCase.getSearchListChildrenService(
                            listChildrenService = sideEffect.listChildrenService ?: emptyList(),
                            keyWorks = sideEffect.searchText
                        )
                    )
                )
            }

            is CustomQuickLinkViewEffect.HandleSelectItem -> {
                if ((!sideEffect.item.isChecked && sideEffect.listChildrenService.count { it.isChecked } < 5) || sideEffect.item.isChecked) {
                    onResult(
                        CustomQuickLinkViewEvent.HandleSelectItemResult(
                            listChildrenService = customQuickLinkUseCase.selectItemListChildrenService(
                                sideEffect.listChildrenService,
                                sideEffect.item
                            ),
                            listQuickLink = customQuickLinkUseCase.selectItemQuickLinkService(
                                sideEffect.listQuickLink,
                                sideEffect.item
                            ),
                            listSearchChildrenService = customQuickLinkUseCase.selectItemListSearchChildrenService(
                                sideEffect.listSearchChildrenService,
                                sideEffect.item
                            )
                        )
                    )
                }
            }

            is CustomQuickLinkViewEffect.UpdateUserQuickLinkAction -> {
                if (sideEffect.listQuickLink.isEmpty()) {
                    showPopupError(
                        errorMessage = resourceProvider.getString(
                            R.string.vui_long_chon_tu_1_den_5_tinh_nang
                        )
                    )
                } else {
                    callDomain(
                        isListenAllError = true,
                        onSuccess = {
                            userInfoUseCase.changeQuickLinkUserInfoFromStorage(sideEffect.listQuickLink.mapNotNull { it.code })

                            onResult(
                                CustomQuickLinkViewEvent.UpdateUserQuickLinkSuccessEvent
                            )
                            viewModelScope.launch(Dispatchers.Default) {
                                customQuickLinkUseCase.needReloadDataHome()
                            }
                        },
                        onFail = { result ->
                            showPopupError(
                                errorMessage = result?.errorMessage.orEmpty()
                            )
                        }
                    ) {
                        customQuickLinkUseCase.updateUserQuickLink(sideEffect.listQuickLink.mapNotNull { it.code })
                    }
                }
            }

            else -> {}

        }
    }
}
