package vn.com.bidv.feature.homepage.domain.model

import com.google.gson.annotations.SerializedName

data class OverallDMO (
    @SerializedName("totalDepositBal")
    val totalDepositBal: java.math.BigDecimal? = null,

    @SerializedName("paymentBal")
    val paymentBal: java.math.BigDecimal? = null,

    @SerializedName("termDepositBal")
    val termDepositBal: java.math.BigDecimal? = null,

    @SerializedName("totalLoanBal")
    val totalLoanBal: java.math.BigDecimal? = null,

    @SerializedName("shortTermBal")
    val shortTermBal: java.math.BigDecimal? = null,

    @SerializedName("midLongTermBal")
    val midLongTermBal: java.math.BigDecimal? = null,

    @SerializedName("overdraftBal")
    val overdraftBal: java.math.BigDecimal? = null,

    @SerializedName("latestUpdated")
    val latestUpdated: String? = null

)