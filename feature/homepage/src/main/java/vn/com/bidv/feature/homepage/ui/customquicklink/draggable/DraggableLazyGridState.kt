package vn.com.bidv.feature.homepage.ui.customquicklink.draggable

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.VectorConverter
import androidx.compose.animation.core.VisibilityThreshold
import androidx.compose.animation.core.spring
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.scrollBy
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.grid.LazyGridItemInfo
import androidx.compose.foundation.lazy.grid.LazyGridItemScope
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableIntState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.zIndex
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.android.awaitFrame
import java.lang.reflect.Field
import kotlin.math.absoluteValue
import kotlin.math.pow

@Composable
fun rememberDraggableLazyGridState(
    state: LazyGridState = rememberLazyGridState(),
    speedFactor: Float = 1f,
    autoscroller: Autoscroller? = null,
    onSwap: (ItemInfo, ItemInfo) -> Unit,
    isItemLocked: ((itemToSwap: ItemInfo) -> Boolean)? = null,
): DraggableLazyGridState {
    val coroutineScope: CoroutineScope = rememberCoroutineScope()
    rememberLazyGridStateHijacker(gridState = state, enabled = true)
    val draggableState = remember(state) {
        DraggableLazyGridState(
            state,
            speedFactor,
            coroutineScope,
            autoscroller,
            onSwap,
            isItemLocked,
        )
    }

    return draggableState
}

@Composable
fun LazyGridItemScope.DraggableItem(
    modifier: Modifier = Modifier,
    index: Int? = null,
    key: Any? = null,
    state: DraggableState<*>,
    content: @Composable (isDragging: Boolean, hoveredItemKey: Any?) -> Unit,
) = DraggableItem(
    index = index,
    key = key,
    state = state,
    modifier = modifier,
    defaultModifier = Modifier.animateItem(),
    content = content,
)

@Composable
fun LazyItemScope.DraggableItem(
    modifier: Modifier = Modifier,
    index: Int? = null,
    key: Any? = null,
    state: DraggableState<*>,
    content: @Composable (isDragging: Boolean, hoveredItemKey: Any?) -> Unit,
) = DraggableItem(
    index = index,
    key = key,
    state = state,
    modifier = modifier,
    defaultModifier = Modifier.animateItem(),
    content = content,
)

@Composable
private fun DraggableItem(
    index: Int?,
    key: Any?,
    state: DraggableState<*>,
    modifier: Modifier,
    defaultModifier: Modifier = Modifier,
    content: @Composable (isDragging: Boolean, hoveredItemKey: Any?) -> Unit,
) {
    val isDragging = if (key != null) {
        key == state.draggingItemKey
    } else {
        index == state.draggingItemIndex
    }

    val released: Boolean =  if (index != null) {
        index == state.cancelDragAnimation.position
    } else {
        key == state.cancelDragAnimation.key
    }

    val contentModifier = if (isDragging) {
        Modifier
            .zIndex(1f)
            .graphicsLayer {
                if (state.isVertical || state.isGrid) translationY = state.draggingItemTop
                if (!state.isVertical || state.isGrid) translationX = state.draggingItemLeft
            }
    } else if (released) {
        Modifier
            .zIndex(1f)
            .graphicsLayer {
                if (state.isVertical || state.isGrid) translationY = state.cancelDragAnimation.offset.y
                if (!state.isVertical || state.isGrid) translationX = state.cancelDragAnimation.offset.x
            }
    } else {
        defaultModifier
    }

    Box(
        modifier = modifier.then(contentModifier)
    ) {
        content(isDragging, state.hoveredItemKey)
    }
}


class DraggableLazyGridState(
    val gridState: LazyGridState,
    speedFactor: Float,
    coroutineScope: CoroutineScope,
    autoscroller: Autoscroller?,
    onSwap: (ItemInfo, ItemInfo) -> Unit,
    isItemLocked: ((itemToSwap: ItemInfo) -> Boolean)?,
) : DraggableState<LazyGridItemInfo>(
    coroutineScope,
    onSwap,
    autoscroller,
    speedFactor,
    isItemLocked,
) {

    override val isGrid: Boolean
        get() = true
    override val isVertical: Boolean
        get() = gridState.layoutInfo.orientation == Orientation.Vertical
    override val viewportStartOffset: Int
        get() = gridState.layoutInfo.viewportStartOffset
    override val viewportEndOffset: Int
        get() = gridState.layoutInfo.viewportEndOffset
    override val firstVisibleItemScrollOffset: Int
        get() = gridState.firstVisibleItemScrollOffset
    override val firstVisibleItemIndex: Int
        get() = gridState.firstVisibleItemIndex
    override val visibleItemsInfo: List<LazyGridItemInfo>
        get() = gridState.layoutInfo.visibleItemsInfo
    override val LazyGridItemInfo.itemIndex: Int
        get() = index
    override val LazyGridItemInfo.itemKey: Any
        get() = key
    override val LazyGridItemInfo.left: Int
        get() = offset.x
    override val LazyGridItemInfo.right: Int
        get() = offset.x + size.width
    override val LazyGridItemInfo.top: Int
        get() = offset.y
    override val LazyGridItemInfo.bottom: Int
        get() = offset.y + size.height
    override val LazyGridItemInfo.height: Int
        get() = size.height
    override val LazyGridItemInfo.width: Int
        get() = size.width
    override val layoutMode: LayoutMode
        get() = LayoutMode.GRID

    override suspend fun scrollBy(distance: Float) {
        gridState.scrollBy(distance)
    }

    override suspend fun scrollToItem(index: Int, scrollOffset: Int) {
        gridState.scrollToItem(index, scrollOffset)
    }
}

@Composable
fun rememberLazyGridStateHijacker(
    gridState: LazyGridState,
    enabled: Boolean = true
): LazyStateHijacker {
    return remember(gridState) {
        LazyStateHijacker(LazyGridHijackerReflectionProvider(gridState), enabled)
    }.apply {
        this.enabled = enabled
    }
}

@Composable
fun rememberLazyListHijacker(
    listState: LazyListState,
    enabled: Boolean = true
): LazyStateHijacker {
    return remember(listState) {
        LazyStateHijacker(LazyListHijackerReflectionProvider(listState), enabled)
    }.apply {
        this.enabled = enabled
    }
}

class LazyStateHijacker(
    private val fieldProvider: LazyHijackerReflectionProvider,
    enabled: Boolean = true
) {
    private val scrollPositionField = fieldProvider.getScrollPositionField()
    private val scrollPositionObj = scrollPositionField.get(fieldProvider.getState())

    private val lastKeyRemover: () -> Unit =
        scrollPositionField.type.getDeclaredField("lastKnownFirstItemKey").run {
            isAccessible = true

            fun() { set(scrollPositionObj, null) }
        }

    private val indexField = scrollPositionField.type.getDeclaredField("index\$delegate").apply {
        isAccessible = true
    }

    var enabled: Boolean = enabled
        set(value) {
            if (field == value) {
                return
            }

            field = value

            setProps(value)
        }

    init {
        setProps(enabled)
    }

    private fun setProps(enable: Boolean) {
        val oldValue = indexField.get(scrollPositionObj).run {
            if (this is IntStateHijacker) {
                intValueDirect
            } else {
                fieldProvider.firstVisibleItemIndex
            }
        }

        val mutableIntState: MutableIntState = if (enable) {
            IntStateHijacker(
                state = mutableIntStateOf(oldValue),
                keyRemover = lastKeyRemover
            )
        } else {
            mutableIntStateOf(oldValue)
        }

        indexField.set(scrollPositionObj, mutableIntState)
    }
}

private const val BASE_SPEED = 20f // Default base speed

interface Autoscroller {
    suspend fun start(
        dragDirection: DragDirection,
        overscroll: Float,
        scrollBy: suspend (Float) -> Unit,
    )
}

internal class DefaultAutoscroller(
    private val speedFactor: Float = 1f,
): Autoscroller {

    init {
        if (speedFactor <= 0f) {
            throw IllegalArgumentException("speedFactor must be higher than 0")
        }
    }

    override suspend fun start(
        dragDirection: DragDirection,
        overscroll: Float,
        scrollBy: suspend (Float) -> Unit,
    ) {
        val directionMultiplier = when (dragDirection) {
            DragDirection.START -> -1
            DragDirection.END -> 1
            else -> 0
        }

        awaitFrame()
        // Flip overscroll if going up
        val adjustedOverscroll = if (dragDirection == DragDirection.START) -overscroll else overscroll

        val scaledScroll = calculateScaledScroll(adjustedOverscroll, speedFactor)
        scrollBy(scaledScroll * directionMultiplier)
    }

    private fun calculateScaledScroll(overscroll: Float, speedFactor: Float): Float {
        // Apply a non-linear transformation to make the speed increase faster with bigger overscroll
        val nonLinearInterpolator = overscroll.coerceIn(-1f, 1f).absoluteValue.pow(2f)

        // Calculate scroll speed directly from overscroll percentage and speed factor
        return nonLinearInterpolator * BASE_SPEED * speedFactor
    }
}

internal class CancelDragAnimation {

    private val animatable = Animatable(Offset.Zero, Offset.VectorConverter)

    internal var position by mutableStateOf<Int?>(null)
    internal var key by mutableStateOf<Any?>(null)
    internal val offset
        get() = animatable.value

    suspend fun animatableChangePosition(itemPosition: Int?, itemKey: Any?, snapTo: Offset) {
        key = itemKey
        position = itemPosition
        animatable.snapTo(snapTo)
        animatable.animateTo(
            Offset.Zero,
            spring(stiffness = Spring.StiffnessMediumLow, visibilityThreshold = Offset.VisibilityThreshold)
        )
        position = null
        key = null
    }
}

internal class IntStateHijacker(
    private val state: MutableIntState,
    private val keyRemover: () -> Unit
) : MutableIntState by state {
    override var intValue: Int
        get() {
            keyRemover()
            return state.intValue
        }
        set(value) {
            state.intValue = value
        }

    override var value: Int
        get() {
            keyRemover()
            return state.value
        }
        set(value) {
            state.value = value
        }

    val intValueDirect: Int get() = state.intValue
}

interface LazyHijackerReflectionProvider {
    val firstVisibleItemIndex: Int

    fun getScrollPositionField(): Field
    fun getState(): Any
}

internal class LazyListHijackerReflectionProvider(
    private val listState: LazyListState,
): LazyHijackerReflectionProvider {
    override val firstVisibleItemIndex: Int
        get() = listState.firstVisibleItemIndex

    override fun getScrollPositionField(): Field {
        return listState.javaClass.getDeclaredField("scrollPosition").apply {
            isAccessible = true
        }
    }

    override fun getState(): Any {
        return listState
    }
}

internal class LazyGridHijackerReflectionProvider(
    private val gridState: LazyGridState,
): LazyHijackerReflectionProvider {
    override val firstVisibleItemIndex: Int
        get() = gridState.firstVisibleItemIndex

    override fun getScrollPositionField(): Field {
        return gridState.javaClass.getDeclaredField("scrollPosition").apply {
            isAccessible = true
        }
    }

    override fun getState(): Any {
        return gridState
    }
}

internal class LazyStaggeredGridHijackerReflectionProvider(
    private val gridState: LazyStaggeredGridState,
): LazyHijackerReflectionProvider {
    override val firstVisibleItemIndex: Int
        get() = gridState.firstVisibleItemIndex

    override fun getScrollPositionField(): Field {
        return gridState.javaClass.getDeclaredField("scrollPosition").apply {
            isAccessible = true
        }
    }

    override fun getState(): Any {
        return gridState
    }
}

