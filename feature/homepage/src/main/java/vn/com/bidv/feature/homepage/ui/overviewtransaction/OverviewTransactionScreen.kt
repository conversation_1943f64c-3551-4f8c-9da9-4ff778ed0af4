package vn.com.bidv.feature.homepage.ui.overviewtransaction

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.hilt.navigation.compose.hiltViewModel
import vn.com.bidv.common.extenstion.isNull
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankBottomSheet
import vn.com.bidv.designsystem.theme.IBBorderDivider
import vn.com.bidv.feature.homepage.ui.overviewtransaction.OverviewTransactionReducer.OverviewTransactionViewEvent
import vn.com.bidv.feature.homepage.ui.overviewtransaction.OverviewTransactionReducer.OverviewTransactionViewState
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.feature.homepage.constants.HomePageConstants
import vn.com.bidv.feature.homepage.domain.WidgetState
import vn.com.bidv.feature.homepage.ui.overviewtransaction.modelUI.ListDefault
import vn.com.bidv.feature.homepage.ui.overviewtransaction.modelUI.OverViewMoneyType
import vn.com.bidv.feature.homepage.ui.overviewtransaction.modelUI.OverViewTransactionMoney
import vn.com.bidv.feature.homepage.ui.overviewtransaction.modelUI.OverViewTransactionType
import vn.com.bidv.feature.homepage.ui.overviewtransaction.modelUI.toOverViewTransactionMoney
import vn.com.bidv.feature.homepage.ui.widgetcommon.WidgetCommonScreen
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants.DateTimeConstants.FORMAT_DD_MM_YYYY_HH_MM_SS
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants.DateTimeConstants.INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS_SSSSSS
import vn.com.bidv.sdkbase.utils.exts.toFormattedDate
import vn.com.bidv.sdkbase.utils.formatMoney
import java.math.BigDecimal

@Composable
fun OverviewTransactionScreen() {
    val overviewTransactionViewModel: OverviewTransactionViewModel = hiltViewModel()
    BaseMVIScreen(
        viewModel = overviewTransactionViewModel,
        renderContent = { uiState, onEvent ->
            OverviewTransactionContent(
                uiState = uiState,
                onEvent = onEvent,
            )
        },
        handleSideEffect = {

        }
    )
}

@Composable
private fun OverviewTransactionContent(
    uiState: OverviewTransactionViewState,
    onEvent: (OverviewTransactionViewEvent) -> Unit,
) {
    val colorSchema = LocalColorScheme.current

    Box(
        modifier = Modifier
            .wrapContentHeight()
            .padding(IBSpacing.spacingM)
            .clip(
                RoundedCornerShape(
                    IBSpacing.spacingS
                )
            )
            .background(color = colorSchema.bgMainTertiary)
            .fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)
        ) {
            HorizontalPager(
                state = uiState.pagerState,
                modifier = Modifier
                    .wrapContentHeight()
                    .fillMaxWidth()
            ) { page ->
                if (page == 0) {
                    DepositOverviewBoard(
                        title = stringResource(R.string.tong_quan_tien_gui),
                        totalMoney = (uiState.overallDEPDMO?.items?.firstOrNull()?.totalBalance ?: BigDecimal.ZERO).toString()
                            .formatMoney(SdkBaseConstants.MoneyCurrencyConstants.VND, isShowCurrCode = false),
                        list = uiState.overallDEPDMO?.items?.firstOrNull()?.balAccTypeList?.toOverViewTransactionMoney() ?:  ListDefault(OverViewTransactionType.DEP),
                        onEvent = onEvent,
                        onClickShowData = {
                            onEvent(OverviewTransactionViewEvent.SetShowDataDeposit(!uiState.isShowDataDeposit))
                        },
                        isShowData = uiState.isShowDataDeposit,
                        state = uiState.stateDEP,
                        latestUpdated = uiState.overallDEPDMO?.items?.firstOrNull()?.updatedDate?.toFormattedDate(
                            inputPattern = INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS_SSSSSS,
                            outputPattern = FORMAT_DD_MM_YYYY_HH_MM_SS
                        ) ?: HomePageConstants.NOT_SHOW_DATE,
                        isDataNull = uiState.overallDEPDMO.isNull(),
                        errorMessage = uiState.messageErrorDEP ?: stringResource(R.string.khong_tai_duoc_du_lieu)
                    )
                } else {
                    DepositOverviewBoard(
                        title = stringResource(R.string.tong_quan_tien_vay),
                        totalMoney = (uiState.overallLOANDMO?.items?.firstOrNull()?.totalBalance ?: BigDecimal.ZERO).toString()
                            .formatMoney(SdkBaseConstants.MoneyCurrencyConstants.VND, isShowCurrCode = false),
                        list = uiState.overallLOANDMO?.items?.firstOrNull()?.balAccTypeList?.toOverViewTransactionMoney() ?: ListDefault(OverViewTransactionType.LOAN),
                        onEvent = onEvent,
                        onClickShowData = {
                            onEvent(OverviewTransactionViewEvent.SetShowDataLoan(!uiState.isShowDataLoan))
                        },
                        isShowData = uiState.isShowDataLoan,
                        state = uiState.stateLN,
                        latestUpdated = uiState.overallLOANDMO?.items?.firstOrNull()?.updatedDate?.toFormattedDate(
                            inputPattern = INPUT_FORMAT_DD_MM_YYYY_HH_MM_SS_SSSSSS,
                            outputPattern = FORMAT_DD_MM_YYYY_HH_MM_SS
                        ) ?: HomePageConstants.NOT_SHOW_DATE,
                        isDataNull = uiState.overallLOANDMO.isNull(),
                        errorMessage = uiState.messageErrorDEP ?: stringResource(R.string.khong_tai_duoc_du_lieu)
                    )
                }
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = IBSpacing.spacingXs),
                horizontalArrangement = Arrangement.spacedBy(
                    IBSpacing.spacingXs,
                    Alignment.CenterHorizontally
                ),
            ) {
                repeat(2) { index ->
                    if (uiState.pagerState.currentPage == index) {
                        Box(
                            modifier = Modifier
                                .size(width = IBSpacing.spacing2xl, height = IBSpacing.spacingXs)
                                .clip(RoundedCornerShape(50))
                                .background(colorSchema.bgBrand_01Primary)
                        )
                    } else {
                        Box(
                            modifier = Modifier
                                .size(IBSpacing.spacingXs)
                                .alpha(0.05f)
                                .background(
                                    color = colorSchema.contentMainPrimary,
                                    shape = CircleShape
                                )
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun DepositOverviewBoard(
    title: String,
    totalMoney: String,
    list: List<OverViewTransactionMoney>,
    onEvent: (OverviewTransactionViewEvent) -> Unit,
    onClickShowData: (() -> Unit) = {},
    isShowData: Boolean,
    state: WidgetState,
    latestUpdated: String,
    isDataNull: Boolean,
    errorMessage: String
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    var isShowBottomSheet by remember { mutableStateOf(false) }

    if (isShowBottomSheet) {
        BottomSheetInformation(
            title = title,
            listOf(
                "*${stringResource(R.string.theo_phan_quyen_dang_ky)}",
                "*${stringResource(R.string.quy_doi_vnd)}"
            ),
            onDismiss = {
                isShowBottomSheet = false
            }
        )
    }

    WidgetCommonScreen(
        state = state,
        onClickRetry = {
            onEvent(OverviewTransactionViewEvent.RefreshData)
        },
        message = errorMessage,
        messageNoData = stringResource(R.string.khong_co_du_lieu),
        messageNoAccountCif = stringResource(R.string.khong_co_phan_quyen_dich_vu),
        modifier = Modifier
            .wrapContentHeight()
            .fillMaxWidth(),
        headerView = {
            Row(
                modifier = Modifier.fillMaxWidth().padding(horizontal = IBSpacing.spacingM).padding(top = IBSpacing.spacingM),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = title,
                    color = colorSchema.contentMainPrimary,
                    style = typography.titleTitle_m,
                    textAlign = TextAlign.Start,
                )

                Spacer(Modifier.width(IBSpacing.spacing2xs))

                Icon(
                    modifier = Modifier
                        .size(IBSpacing.spacingM)
                        .clickable {
                            isShowBottomSheet = !isShowBottomSheet
                        },
                    imageVector = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.information_circle),
                    contentDescription = null,
                    tint = colorSchema.contentMainPrimary,
                )

                Spacer(modifier = Modifier.weight(1f))


                Icon(
                    modifier = Modifier
                        .size(IBSpacing.spacingL)
                        .clickable {
                            onClickShowData()
                        },
                    imageVector = ImageVector.vectorResource(id = (if (isShowData) vn.com.bidv.designsystem.R.drawable.eyes_closed_outline else vn.com.bidv.designsystem.R.drawable.eyes_open_outline)),
                    contentDescription = null,
                    tint = colorSchema.contentMainPrimary,
                )

                Spacer(Modifier.width(IBSpacing.spacingM))

                Icon(
                    modifier = Modifier
                        .size(IBSpacing.spacingL)
                        .graphicsLayer(scaleX = -1f)
                        .clickable {
                            onEvent(OverviewTransactionViewEvent.RefreshData)
                        },
                    imageVector = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.refresh),
                    contentDescription = null,
                    tint = colorSchema.contentMainPrimary,
                )

            }
        }
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs),
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
        ) {
            Column(
                verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs),
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(horizontal =  IBSpacing.spacingM)
            ) {
                Row(
                    verticalAlignment = Alignment.Bottom,
                    horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacing2xs)
                ) {
                    Text(
                        text = if (isShowData) totalMoney else HomePageConstants.NOT_SHOW_DATA_MONEY,
                        color = colorSchema.contentMainPrimary,
                        style = typography.headlineHeadline_s,
                        textAlign = TextAlign.Start
                    )

                    Text(
                        text = stringResource(R.string.vnd),
                        color = colorSchema.contentMainTertiary,
                        style = typography.labelLabel_l,
                        textAlign = TextAlign.Start
                    )

                }
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(
                            RoundedCornerShape(
                                IBSpacing.spacing2xs
                            )
                        )
                        .height(IBSpacing.spacing3xl)
                ) {
                    if (!isDataNull && totalMoney != BigDecimal.ZERO.toString()) {
                        Row(
                            modifier = Modifier
                                .matchParentSize()
                        ) {
                            list.filter { it.typeMoney != OverViewMoneyType.NONE && it.money != BigDecimal.ZERO }
                                .forEach { item ->
                                    Box(
                                        modifier = Modifier
                                            .background(item.typeMoney.color)
                                            .fillMaxHeight()
                                            .weight(item.money.toFloat())

                                    )
                                }
                        }
                    } else {
                        Box(
                            modifier = Modifier
                                .background(colorSchema.bgMainPrimary)
                                .fillMaxHeight()
                                .fillMaxWidth()
                        )
                    }
                }

                list.forEach { item ->
                    if (item.typeMoney == OverViewMoneyType.NONE) {
                        Text(
                            text = HomePageConstants.Space_Text,
                            color = colorSchema.contentMainTertiary,
                            style = typography.bodyBody_m,
                            textAlign = TextAlign.Start,
                        )
                    } else {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingXs)
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(width = IBSpacing.spacing2xs, height = IBSpacing.spacingM)
                                    .clip(RoundedCornerShape(50))
                                    .background(item.typeMoney.color)
                            )

                            Text(
                                text = stringResource(item.typeMoney.title),
                                color = colorSchema.contentMainTertiary,
                                style = typography.bodyBody_m,
                                textAlign = TextAlign.Start,
                            )

                            Text(
                                text = if (isShowData) (item.money).toString()
                                    .formatMoney(SdkBaseConstants.MoneyCurrencyConstants.VND) else "${
                                    HomePageConstants.NOT_SHOW_DATA_MONEY
                                } ${stringResource(R.string.vnd)}",

                                color = colorSchema.contentMainPrimary,
                                style = typography.labelLabel_l,
                                textAlign = TextAlign.End,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
            }

            HorizontalDivider(
                color = colorSchema.borderSolidPrimary,
                thickness = IBBorderDivider.borderDividerS,
                modifier = Modifier.fillMaxWidth()
            )

            Text(
                text = stringResource(R.string.cap_nhat_s,  latestUpdated),
                color = colorSchema.contentMainTertiary,
                style = typography.bodyBody_m,
                textAlign = TextAlign.Start,
                modifier = Modifier.padding(start = IBSpacing.spacingM)
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BottomSheetInformation(
    title: String,
    listString: List<String>,
    onDismiss: (() -> Unit)? = {},
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current

    IBankBottomSheet(
        title = title,
        onDismiss = onDismiss,
        applyMinHeight = false
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingM)
        ) {
            HorizontalDivider(
                color = colorSchema.borderSolidPrimary,
                thickness = IBBorderDivider.borderDividerXs
            )

            listString.forEach {
                Text(
                    text = it,
                    color = colorSchema.contentMainPrimary,
                    style = typography.bodyBody_l,
                    textAlign = TextAlign.Start,
                    modifier = Modifier.padding(horizontal = IBSpacing.spacingM)
                )
            }
        }
    }

}