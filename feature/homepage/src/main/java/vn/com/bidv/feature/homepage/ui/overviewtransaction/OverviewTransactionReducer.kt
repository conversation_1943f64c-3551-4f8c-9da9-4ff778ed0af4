package vn.com.bidv.feature.homepage.ui.overviewtransaction

import androidx.compose.foundation.pager.PagerState
import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.homepage.constants.HomePageConstants
import vn.com.bidv.feature.homepage.domain.WidgetState
import vn.com.bidv.feature.homepage.domain.model.DataListOverallBalanceDMO

class OverviewTransactionReducer :
    Reducer<OverviewTransactionReducer.OverviewTransactionViewState, OverviewTransactionReducer.OverviewTransactionViewEvent, OverviewTransactionReducer.OverviewTransactionViewEffect> {

    @Immutable
    data class OverviewTransactionViewState(
        val overallDEPDMO: DataListOverallBalanceDMO? = null,
        val overallLOANDMO: DataListOverallBalanceDMO? = null,
        val stateDEP: WidgetState = WidgetState.CONTENT,
        val stateLN: WidgetState = WidgetState.CONTENT,
        val isShowDataDeposit: Boolean = false,
        val isShowDataLoan: Boolean = false,
        val pagerState: PagerState = PagerState(pageCount = {2}),
        val messageErrorDEP: String? = null,
        val messageErrorLN: String? = null
    ) : ViewState

    @Immutable
    sealed class OverviewTransactionViewEvent : ViewEvent {
        data object RefreshData : OverviewTransactionViewEvent()
        data class GetOverallInfoSuccess(val overallDMO: DataListOverallBalanceDMO?,val type: String) :
            OverviewTransactionViewEvent()
        data class SetShowDataDeposit(val isShowDataDeposit: Boolean): OverviewTransactionViewEvent()
        data class SetShowDataLoan(val isShowDataLoan: Boolean): OverviewTransactionViewEvent()
        data class GetOverallInfoFail(val type: String, val messageError: String?) : OverviewTransactionViewEvent()
        data class GetOverallInfoFailCif(val type: String) : OverviewTransactionViewEvent()
    }

    @Immutable
    sealed class OverviewTransactionViewEffect : SideEffect {
        data class GetOverallInfo(val type: String) : OverviewTransactionViewEffect()
    }

    override fun reduce(
        previousState: OverviewTransactionViewState,
        event: OverviewTransactionViewEvent,
    ): Pair<OverviewTransactionViewState, OverviewTransactionViewEffect?> {
        return when (event) {
            is OverviewTransactionViewEvent.GetOverallInfoFail -> {
                if (event.type == HomePageConstants.DEP) {
                    previousState.copy(
                        stateDEP = WidgetState.ERROR,
                        messageErrorDEP = event.messageError
                    ) to null
                } else {
                    previousState.copy(
                        stateLN = WidgetState.ERROR,
                        messageErrorLN = event.messageError
                    ) to null
                }
            }

            is OverviewTransactionViewEvent.GetOverallInfoSuccess -> {
                if (event.type == HomePageConstants.DEP) {
                    previousState.copy(
                        stateDEP = if (event.overallDMO == null || event.overallDMO.items.isNullOrEmpty()) WidgetState.NOCONTENT else WidgetState.CONTENT,
                        overallDEPDMO = event.overallDMO,
                    ) to null
                } else {
                    previousState.copy(
                        stateLN = if (event.overallDMO == null || event.overallDMO.items.isNullOrEmpty()) WidgetState.NOCONTENT else WidgetState.CONTENT,
                        overallLOANDMO = event.overallDMO,
                    ) to null
                }
            }

            is OverviewTransactionViewEvent.RefreshData -> {
                if (previousState.pagerState.currentPage == 0) {
                    previousState.copy(
                        stateDEP = WidgetState.LOADING,
                        isShowDataDeposit = true
                    ) to OverviewTransactionViewEffect.GetOverallInfo(type = HomePageConstants.DEP)
                } else
                {
                    previousState.copy(
                        stateLN = WidgetState.LOADING,
                        isShowDataLoan = true
                    ) to OverviewTransactionViewEffect.GetOverallInfo(type = HomePageConstants.LN)
                }
            }

            is OverviewTransactionViewEvent.SetShowDataDeposit -> {
                if (previousState.overallDEPDMO == null || previousState.overallDEPDMO.items.isNullOrEmpty()) {
                    if (event.isShowDataDeposit) {
                        previousState.copy(
                            stateDEP = WidgetState.LOADING,
                            isShowDataDeposit = event.isShowDataDeposit
                        ) to OverviewTransactionViewEffect.GetOverallInfo(type = HomePageConstants.DEP)
                    } else {
                        previousState.copy(
                            isShowDataDeposit = event.isShowDataDeposit
                        ) to null
                    }
                } else {
                    previousState.copy(
                        stateDEP = WidgetState.CONTENT,
                        isShowDataDeposit = event.isShowDataDeposit
                    ) to null
                }
            }

            is OverviewTransactionViewEvent.SetShowDataLoan -> {
                if (previousState.overallLOANDMO == null || previousState.overallLOANDMO.items.isNullOrEmpty()) {
                    if (event.isShowDataLoan) {
                        previousState.copy(
                            stateLN = WidgetState.LOADING,
                            isShowDataLoan = event.isShowDataLoan
                        ) to OverviewTransactionViewEffect.GetOverallInfo(type = HomePageConstants.LN)
                    } else {
                        previousState.copy(
                            isShowDataLoan = event.isShowDataLoan
                        ) to null
                    }
                } else {
                    previousState.copy(
                        stateLN = WidgetState.CONTENT,
                        isShowDataLoan = event.isShowDataLoan
                    ) to null
                }
            }

            is OverviewTransactionViewEvent.GetOverallInfoFailCif -> {
                if (event.type == HomePageConstants.DEP) {
                    previousState.copy(
                        stateDEP = WidgetState.NO_ACCOUNT_CIF,
                    ) to null
                } else {
                    previousState.copy(
                        stateLN = WidgetState.NO_ACCOUNT_CIF,
                    ) to null
                }
            }
        }
    }
}
