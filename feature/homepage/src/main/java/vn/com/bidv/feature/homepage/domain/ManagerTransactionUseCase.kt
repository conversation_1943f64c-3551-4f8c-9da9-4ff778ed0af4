package vn.com.bidv.feature.homepage.domain

import vn.com.bidv.feature.common.domain.data.DataListTxnCountResponseDMO
import vn.com.bidv.feature.homepage.data.HomepageRepository
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class ManagerTransactionUseCase @Inject constructor(
    private val homepageRepository: HomepageRepository
) {
    suspend fun getTxnCount(): DomainResult<DataListTxnCountResponseDMO> {
        val domain = homepageRepository.getTxnCount()
        return domain.convert(DataListTxnCountResponseDMO::class.java)
    }
}