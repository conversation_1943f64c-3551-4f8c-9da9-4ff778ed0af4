package vn.com.bidv.feature.homepage.ui.service

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.PermissionResDMO

class ServiceReducer :
    Reducer<ServiceReducer.ServiceViewState, ServiceReducer.ServiceViewEvent, ServiceReducer.ServiceViewEffect> {

    data class ServiceViewState(
        val isInitSuccess: Boolean = false,
        val textSearch: String = "",
        val isSearchView: Boolean = false,
        val listChildrenService: List<PermissionResDMO> = emptyList(),
        val listSearchChildrenService: List<PermissionResDMO> = emptyList(),
        val listService: List<PermissionResDMO> = emptyList()
    ) : ViewState

    @Immutable
    sealed interface ServiceViewEvent : ViewEvent {
        data object OnInitDataService : ServiceViewEvent
        data class UpdateSearchText(val searchText: String) : ServiceViewEvent
        data class GetServicesFromStorageSuccess(val listServices: List<PermissionResDMO>?, val listChildrenService: List<PermissionResDMO>?) : ServiceViewEvent
        data class GetSearchListChildrenService(val listSearchChildrenService: List<PermissionResDMO>?): ServiceViewEvent
        data class SetSearchView(val isSearchView: Boolean): ServiceViewEvent
    }


    @Immutable
    sealed class ServiceViewEffect : SideEffect {
        data object GetPermissionResFromStorage: ServiceViewEffect()
        data class GetDataSearch(val listChildrenService: List<PermissionResDMO>?, val searchText: String) : ServiceViewEffect()
    }




    override fun reduce(
        previousState: ServiceViewState,
        event: ServiceViewEvent,
    ): Pair<ServiceViewState, ServiceViewEffect?> {
        return when (event) {
            is ServiceViewEvent.OnInitDataService -> {
                previousState.copy(
                    isInitSuccess = true
                ) to ServiceViewEffect.GetPermissionResFromStorage
            }

            is ServiceViewEvent.GetServicesFromStorageSuccess -> {
                previousState.copy(
                    listService = event.listServices ?: emptyList(),
                    listChildrenService = event.listChildrenService ?: emptyList()
                ) to null
            }

            is ServiceViewEvent.UpdateSearchText -> {
                if (previousState.textSearch == event.searchText) {
                    previousState to null
                } else {
                    previousState.copy(
                        textSearch = event.searchText
                    ) to ServiceViewEffect.GetDataSearch(listChildrenService = previousState.listService,searchText = event.searchText)
                }
            }

            is ServiceViewEvent.GetSearchListChildrenService -> {
                previousState.copy(
                    listSearchChildrenService  = event.listSearchChildrenService ?: emptyList()
                ) to null
            }

            is ServiceViewEvent.SetSearchView -> {
                if (previousState.isSearchView == event.isSearchView) {
                    previousState to null
                } else {
                    if (event.isSearchView) {
                        previousState.copy(
                            isSearchView = event.isSearchView
                        ) to null
                    } else {
                        previousState.copy(
                            isSearchView = event.isSearchView,
                            textSearch = "",
                            listSearchChildrenService = emptyList()
                        ) to null
                    }
                }
            }

            else -> {
                previousState to null
            }
        }
    }
}
