package vn.com.bidv.feature.homepage.ui.accountinquiry

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.BaseState
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.homepage.domain.model.InquiryAccountType

class AccountInquiryReducer :
    Reducer<AccountInquiryReducer.AccountInquiryViewState, AccountInquiryReducer.AccountInquiryViewEvent, AccountInquiryReducer.AccountInquiryViewEffect> {

    @Immutable
    sealed class AccountInquiryViewState : ViewState {

        open fun handleEvent(
            event: AccountInquiryViewEvent
        ): Pair<AccountInquiryViewState, AccountInquiryViewEffect?> {
            return this to null
        }

        data class ShowContent(val data: List<InquiryAccountType>) : AccountInquiryViewState()

        data class CommonState(val baseState: BaseState) : AccountInquiryViewState() {
            override fun handleEvent(event: AccountInquiryViewEvent): Pair<AccountInquiryViewState, AccountInquiryViewEffect?> {
                return when (baseState) {
                    is BaseState.InitScreen -> {
                        when (event) {
                            AccountInquiryViewEvent.FetchUserData -> {
                                this to AccountInquiryViewEffect.FetchUserInfo
                            }
                            is AccountInquiryViewEvent.FetchUserDataSuccess -> {
                                ShowContent(event.allowInquiryPermissionList) to null
                            }

                            is AccountInquiryViewEvent.FetchUserDataFailure -> {
                                CommonState(BaseState.Error(event.errorMessage)) to AccountInquiryViewEffect.ShowError(
                                    event.errorMessage
                                )
                            }

                        }
                    }

                    is BaseState.Error -> {
                        when (event) {
                            AccountInquiryViewEvent.FetchUserData -> {
                                this to AccountInquiryViewEffect.FetchUserInfo
                            }

                            else -> this to null
                        }
                    }

                     else -> this to null
                }
            }
        }
    }

    @Immutable
    sealed interface AccountInquiryViewEvent : ViewEvent {
        data object FetchUserData : AccountInquiryViewEvent
        data class FetchUserDataSuccess(val allowInquiryPermissionList: List<InquiryAccountType>) :
            AccountInquiryViewEvent
        data class FetchUserDataFailure(val errorMessage: String? = null) : AccountInquiryViewEvent
    }

    @Immutable
    sealed interface AccountInquiryViewEffect : SideEffect {
        data object FetchUserInfo : AccountInquiryViewEffect
        data class ShowError(val errorMessage: String?) : AccountInquiryViewEffect, UIEffect
    }

    override fun reduce(
        previousState: AccountInquiryViewState,
        event: AccountInquiryViewEvent,
    ): Pair<AccountInquiryViewState, AccountInquiryViewEffect?> {
        return previousState.handleEvent(event)
    }
}
