package vn.com.bidv.feature.homepage.utils

import androidx.compose.ui.text.toUpperCase
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.utils.ASIA_CCY
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale
import java.util.Locale.US
import androidx.compose.ui.text.intl.Locale as AndroidLocale

fun String.removeSpecialCharacters(): String = this.replace("[^a-zA-Z0-9\\s]".toRegex(), "")

// specific function to format exchange rate in home screen
fun String?.formatExchangeRate(currCode: String?): String {
    val isAsiaFormat =
        (ASIA_CCY.contains(currCode.orEmpty().trim().toUpperCase(AndroidLocale.current)))
    val defaultZero =
        if (isAsiaFormat) {
            "0"
        } else {
            "0.00"
        }
    val formatter: DecimalFormat =
        if (isAsiaFormat) {
            DecimalFormat("#,###", DecimalFormatSymbols(US))
        } else {
            DecimalFormat("#,##0.################", DecimalFormatSymbols(US))
        }
    return try {
        val numberAmount =
            this?.toBigDecimalOrNull() ?: BigDecimal.ZERO
        if (this.isNullOrBlank() || numberAmount.compareTo(BigDecimal.ZERO) == 0) {
            defaultZero
        } else {
            formatter.format(numberAmount).trim()
        }
    } catch (e: Exception) {
        BLogUtil.logException(e)
        defaultZero
    }
}

// if decimal part is .00  then remove else keep it
fun String.formatZeroDecimalValue(): String =
    try {
        val number = replace(",", "").toDouble()
        if (number == 0.0) {
            ""
        } else {
            this
        }
    } catch (e: NumberFormatException) {
        this
    }
