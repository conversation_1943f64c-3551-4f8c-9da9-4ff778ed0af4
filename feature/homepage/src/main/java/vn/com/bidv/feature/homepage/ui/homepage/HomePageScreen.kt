package vn.com.bidv.feature.homepage.ui.homepage

import android.annotation.SuppressLint
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.navigation.NavHostController
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.designsystem.R
import vn.com.bidv.designsystem.component.bottomNavigation.BottomNavItemData
import vn.com.bidv.designsystem.component.bottomNavigation.IBankBottomNavigation
import vn.com.bidv.designsystem.component.navigation.button.IBankNormalButton
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonSize
import vn.com.bidv.designsystem.component.navigation.button.NormalButtonType
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.domain.notificationcommon.TabNotiType
import vn.com.bidv.feature.homepage.navigation.HomepageRoute.HOME_PAGE_ROUTE
import vn.com.bidv.feature.homepage.ui.accountinquiry.AccountInquiryScreen
import vn.com.bidv.feature.homepage.ui.home.HomeScreen
import vn.com.bidv.feature.homepage.ui.homepage.HomePageReducer.HomePageViewEvent
import vn.com.bidv.feature.homepage.ui.homepage.HomePageReducer.HomePageViewState
import vn.com.bidv.feature.homepage.ui.notificationpopupscreen.NotificationPopupScreen
import vn.com.bidv.feature.homepage.ui.service.ServiceScreen
import vn.com.bidv.feature.homepage.ui.setting.SettingScreen
import vn.com.bidv.sdkbase.navigation.IBankMainRouting.Companion.allRoutes
import vn.com.bidv.sdkbase.navigation.notifyroute.NotificationBuilderUtils
import vn.com.bidv.feature.common.navigation.NavigationHelper as CommonNavigationHelper
import vn.com.bidv.localization.R as localizationR

@Composable
fun HomePageScreen(navController: NavHostController) {
    val homePageViewModel: HomePageViewModel = hiltViewModel()

    HandleNavigateNotification(homePageViewModel, navController)

    HandleCreateTransaction(homePageViewModel, navController)

    BaseScreen(
        navController = navController,
        viewModel = homePageViewModel,
        isLightStatusBar = false,
        renderContent = {uiState, onEvent ->
            HomePageContent(
                uiState = uiState,
                onEvent = onEvent,
                navigation = navController
            )
        },
        topAppBarConfig = TopAppBarConfig(isShowTopAppBar = false),
    )


}

@Composable
private fun HandleCreateTransaction(
    homePageViewModel: HomePageViewModel,
    navController: NavHostController
) {
    CollectSideEffect(homePageViewModel.localRepository.transactionData) {
        it?.let {
            CommonNavigationHelper.navigateToCreateTransactionScreen(navController = navController, transactionData = it.replace("\n", ""))
            homePageViewModel.localRepository.clearTransactionData()
        }
    }
}

@Composable
private fun HandleNavigateNotification(
    homePageViewModel: HomePageViewModel,
    navController: NavHostController
) {
    CollectSideEffect(homePageViewModel.localRepository.notificationModel) {
        it.notificationData?.run {
            homePageViewModel.cleanNotificationData()
            TabNotiType.safeValueOf(notiType)?.let { tabNotiType ->
                homePageViewModel.markNotificationAsRead(
                    notifyId = notifyId,
                    displayTab = displayTab,
                    tabNotiType = tabNotiType
                )
            }
            NotificationBuilderUtils.routeToDestinationByNotyInfo(
                navController = navController,
                navigateId = navigateId,
                displayTab = displayTab,
                listParam = listParam,
                isRedirectDefault = true
            )
        }
    }
}

data class BottomNavItem(
    val title: String,
    val route: String,
    val item: Int,
    val itemSelected: Int,
)

@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter")
@Composable
private fun HomePageContent(
    uiState: HomePageViewState,
    onEvent: (HomePageViewEvent) -> Unit,
    navigation: NavHostController
) {

    val showBottomBar = remember { mutableStateOf(true) }
    val homePageTabIndexKey = "homeTabIndex"
    val homePageTabIndex =
        navigation.currentBackStackEntry?.savedStateHandle?.get<Int>(homePageTabIndexKey)

    val items = listOf(
        BottomNavItem(stringResource(id = localizationR.string.trang_chu), "home", R.drawable.home_outline, R.drawable.home),
        BottomNavItem(stringResource(id = localizationR.string.tai_khoan), "account", R.drawable.tai_khoan_outline, R.drawable.tai_khoan),
        BottomNavItem(stringResource(id = localizationR.string.dich_vu), "service", R.drawable.san_pham_outline, R.drawable.san_pham),
        BottomNavItem(stringResource(id = localizationR.string.cai_dat), "setting", R.drawable.settings_outline, R.drawable.settings),
    )

    // Retrieve the homePageTabIndexKey from SavedStateHandle and trigger an event to select the home tab.
    LaunchedEffect(Unit) {
        homePageTabIndex?.let {
            onEvent(HomePageViewEvent.OnClickItemBottomBar(it))
            // Remove the key after handling to avoid selecting this tab whenever HomePageScreen is opened.
            navigation.currentBackStackEntry?.savedStateHandle?.remove<Int>(homePageTabIndexKey)
        }

        onEvent(HomePageViewEvent.CheckShowQrEvent)
    }

    val buttons = items.mapIndexed { index, iconData ->
        BottomNavItemData(
            drawableResIdUnSelection = items[index].item,
            drawableResIdSelection = items[index].itemSelected,
            selected = index == uiState.selectedIndex,
            onClick = { onEvent(HomePageViewEvent.OnClickItemBottomBar(index)) },
            label = items[index].title
        )
    }

    Scaffold(
        bottomBar = {
            AnimatedVisibility(
                visible = showBottomBar.value,
                enter = slideInVertically(initialOffsetY = { it }),
                exit = slideOutVertically(targetOffsetY = { it })
            ) {
                IBankBottomNavigation(
                    buttons = buttons,
                    fabSize = 60.dp,
                    barHeight = 68.dp,
                    fabIcon = R.drawable.qr_code_outline,
                    isShowQr = uiState.isShowQr,
                    onFabClick = {
                        CommonNavigationHelper.navigateToScanQRScreen(navController = navigation)
                    }
                )
            }
        },
        modifier = Modifier.fillMaxSize()
    ) {
        ScreenContent(selectedIndex = uiState.selectedIndex, navigation = navigation, showBottomBar = showBottomBar)
    }

    NotificationPopupScreen(navigation)
    HandleShouldFetchTotalNotifyUnread(uiState, onEvent)
}

@Composable
private fun HandleShouldFetchTotalNotifyUnread(
    uiState: HomePageViewState,
    onEvent: (HomePageViewEvent) -> Unit
) {
    val lifecycleOwner = LocalLifecycleOwner.current

    if (uiState.selectedIndex != 0) {
        onEvent(HomePageViewEvent.MarkTotalNotifyAsShouldFetch(true))
    }

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_START -> {
                    onEvent(HomePageViewEvent.MarkTotalNotifyAsShouldFetch(true))
                }

                Lifecycle.Event.ON_STOP -> {
                    onEvent(HomePageViewEvent.MarkTotalNotifyAsShouldFetch(false))
                }

                else -> {}
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}

@Composable
fun ScreenContent(selectedIndex: Int, navigation: NavHostController, showBottomBar: MutableState<Boolean>) {
    when (selectedIndex) {
        0 ->  HomeScreen(navigation)
        1 ->  AccountInquiryScreen(navigation)
        2 ->  ServiceScreen(navigation, showBottomBar = showBottomBar)
        3 ->  SettingScreen(navigation)
    }
}

@Composable
fun MockNavigate(navigation: NavHostController) {
    Box(
        modifier = Modifier.fillMaxSize().background(LocalColorScheme.current.bgMainPrimary),
        contentAlignment = Alignment.Center
    ) {
        ButtonColumn(navigation)
    }
}

@Composable
fun ButtonColumn(navigation: NavHostController) {
    val buttonLabels = allRoutes

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        LazyColumn (
            modifier = Modifier
                .fillMaxWidth(0.8f)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            items(items = buttonLabels) { buttonLabel ->
                IBankNormalButton(
                    modifier = Modifier.fillMaxWidth(),
                    size = NormalButtonSize.L(LocalTypography.current),
                    type = NormalButtonType.PRIMARY(LocalColorScheme.current),
                    text = buttonLabel,
                    onClick = { navigation.navigate(buttonLabel) }
                )
            }
        }
    }
}


