package vn.com.bidv.feature.homepage.ui.customwidget

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.feature.homepage.ui.customquicklink.draggable.DraggableItem
import vn.com.bidv.feature.homepage.ui.customquicklink.draggable.draggable
import vn.com.bidv.designsystem.component.dataentry.IBankCheckBox
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankBottomSheet
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.feature.homepage.ui.customwidget.CustomWidgetReducer.CustomWidgetViewEvent
import vn.com.bidv.feature.homepage.ui.customwidget.CustomWidgetReducer.CustomWidgetViewState
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.homepage.constants.WidgetType
import vn.com.bidv.feature.homepage.ui.customquicklink.draggable.rememberDraggableLazyListState
import vn.com.bidv.localization.R

@Composable
fun CustomWidgetScreen(navController: NavHostController) {
    val customWidgetViewModel: CustomWidgetViewModel = hiltViewModel()
    val colorSchema = LocalColorScheme.current
    var isShowBottomSheet by remember { mutableStateOf(false) }
    BaseScreen(
        navController = navController,
        viewModel = customWidgetViewModel,
        renderContent = { uiState, onEvent ->
            if (!uiState.isInitSuccess) {
                onEvent(CustomWidgetViewEvent.OnInitDataWidget)
            }

            CustomWidgetContent(
                uiState = uiState,
                onEvent = onEvent,
                navController
            )

            if (isShowBottomSheet) {
                CustomWidgetBottomSheet(
                    uiState = uiState,
                    onEvent = onEvent,
                    onDismiss = {
                        onEvent(CustomWidgetViewEvent.DismissApplySetListWidget)
                        isShowBottomSheet = false
                    },
                    onAccept = {
                        onEvent(CustomWidgetViewEvent.ApplySetListWidget)
                        isShowBottomSheet = false
                    }
                )
            }
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is CustomWidgetReducer.CustomWidgetViewEffect.UpdateWidgetInfoByUserSuccessSideEffect -> {
                    navController.popBackStack()
                }

                else -> {
                    //Do nothing
                }
            }
        },
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(R.string.tien_ich_cua_ban),
            isShowNavigationIcon = true,
            actionItems = {
                Box(modifier = Modifier.padding()) {
                    IconButton(
                        modifier = Modifier
                            .size(IBSpacing.spacing3xl)
                            .clip(RoundedCornerShape(IBSpacing.spacingXs))
                            .background(colorSchema.bgBrand_01Primary)
                            .padding(IBSpacing.spacing2xs),
                        onClick = {
                            isShowBottomSheet = true
                        }
                    ) {
                        Icon(
                            painter = painterResource(id = vn.com.bidv.designsystem.R.drawable.edit_outline),
                            contentDescription = null,
                            tint = colorSchema.contentOn_specialPrimary,
                            modifier = Modifier.size(IBSpacing.spacingL)
                        )
                    }
                }
            }, showHomeIcon = false
        ),
        backgroundColor = colorSchema.bgMainTertiary
    )

}

@Composable
private fun CustomWidgetContent(
    uiState: CustomWidgetViewState,
    onEvent: (CustomWidgetViewEvent) -> Unit,
    navController: NavHostController
) {
    val colorSchema = LocalColorScheme.current
    val lazyState = rememberDraggableLazyListState(onSwap = { from, to ->
        onEvent(CustomWidgetViewEvent.RearrangeItemsWidget(from.index, to.index))
    })

    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = IBSpacing.spacingM),
            verticalArrangement = Arrangement.spacedBy(4.dp),
            state = lazyState.state
        ) {
            items(items = uiState.listWidget, key = { it.name }) { item ->
                DraggableItem(
                    key = item.name,
                    state = lazyState
                ) { _, _ ->
                    ItemCustomWidget(
                        type = item,
                        Modifier.draggable(draggableState = lazyState, key = item.name)
                    )
                }
            }
        }

        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .background(colorSchema.bgMainPrimary)
        ) {
            IBankActionBar(
                isVertical = false,
                buttonPositive = DialogButtonInfo(
                    label = stringResource(R.string.ap_dung),
                    onClick = {
                        onEvent(CustomWidgetViewEvent.UpdateWidgetInfoByUser)
                    }
                ),
            )
        }

    }
}

@Composable
private fun ItemCustomWidget(
    type: WidgetType,
    modifier: Modifier
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(IBSpacing.spacingXs))
            .background(colorSchema.bgMainPrimary)
            .drawBehind {
                drawRoundRect(
                    color = colorSchema.borderMainPrimary,
                    style = Stroke(
                        width = 10f,
                        pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
                    ),
                )
            }
            .padding(IBSpacing.spacing2xs)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(IBSpacing.spacing2xs))
                .background(color = colorSchema.bgMainTertiary)
                .padding(IBSpacing.spacingM)
        ) {
            Row {
                Icon(
                    modifier = Modifier
                        .size(IBSpacing.spacing2xl)
                        .clickable {

                        },
                    imageVector = ImageVector.vectorResource(id = type.resId),
                    contentDescription = null,
                )

                Spacer(modifier = Modifier.width(IBSpacing.spacingM))

                Text(
                    text = stringResource(type.titleId),
                    color = colorSchema.contentMainPrimary,
                    style = typography.bodyBody_l,
                    textAlign = TextAlign.Start,
                    modifier = Modifier.weight(1f)
                )

                Icon(
                    modifier = modifier
                        .size(IBSpacing.spacing2xl),
                    imageVector = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.menu_outline),
                    contentDescription = null,
                    tint = colorSchema.contentMainPrimary,
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CustomWidgetBottomSheet(
    uiState: CustomWidgetViewState,
    onEvent: (CustomWidgetViewEvent) -> Unit,
    onDismiss: (() -> Unit) = {},
    onAccept: (() -> Unit) = {},
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    IBankBottomSheet(
        title = stringResource(R.string.chinh_sua_tien_ich),
        onDismiss = onDismiss
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingS)
        ) {
            Column(
                modifier = Modifier.padding(horizontal = IBSpacing.spacingM)
            ) {
                Text(
                    text = stringResource(R.string.vui_long_chon_it_nhat_d_tien_ich, uiState.numberMinPickup),
                    color = colorSchema.contentMainTertiary,
                    style = typography.bodyBody_m,
                )

                LazyColumn {
                   items(items = uiState.listAllWidget) { item ->
                       ItemWidgetCheckBox(
                           data = item,
                           onEvent = onEvent
                       )
                   }
                }
            }

            IBankActionBar(
                isVertical = false,
                buttonNegative = DialogButtonInfo(
                    label = stringResource(R.string.huy),
                    onClick = {
                        onDismiss()
                    }
                ),
                buttonPositive = DialogButtonInfo(
                    label = stringResource(R.string.ap_dung),
                    isEnable = uiState.isEnableApplyBtn,
                    onClick = {
                        if (uiState.isEnableApplyBtn) {
                            onAccept()
                        }
                    }
                ),
            )
        }
    }
}

@Composable
private fun ItemWidgetCheckBox(
    data: ModelCheckAble<WidgetType>,
    onEvent: (CustomWidgetViewEvent) -> Unit,
) {
    val colorSchema = LocalColorScheme.current
    val typography = LocalTypography.current
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = IBSpacing.spacingS),
        horizontalArrangement = Arrangement.spacedBy(IBSpacing.spacingS),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            modifier = Modifier.size(IBSpacing.spacing2xl),
            imageVector = ImageVector.vectorResource(id = data.data.resId),
            contentDescription = null,
            tint = Color.Unspecified,
        )

        Text(
            text = stringResource(data.data.titleId),
            color = colorSchema.contentMainPrimary,
            style = typography.bodyBody_l,
            textAlign = TextAlign.Start
        )

        Spacer(modifier = Modifier.weight(1f))

        IBankCheckBox(
            indeterminate = false,
            checked = data.isChecked,
            onCheckedChange = {
                onEvent(CustomWidgetViewEvent.SelectItem(data))
            }
        )
    }
}