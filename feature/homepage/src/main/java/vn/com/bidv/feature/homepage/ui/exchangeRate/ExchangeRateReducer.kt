package vn.com.bidv.feature.homepage.ui.exchangeRate

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.homepage.constants.HomePageConstants
import vn.com.bidv.feature.homepage.domain.WidgetState
import vn.com.bidv.feature.homepage.domain.model.DataListExchangeRateDMO
import vn.com.bidv.feature.homepage.domain.model.ExchangeRateDMO
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants.DateTimeConstants.FORMAT_DD_MM_YYYY_HH_MM_SS
import vn.com.bidv.sdkbase.utils.exts.getCurrentDate

class ExchangeRateReducer :
    Reducer<ExchangeRateReducer.ExchangeRateViewState, ExchangeRateReducer.ExchangeRateViewEvent, ExchangeRateReducer.ExchangeRateViewEffect> {
    @Immutable
    data class ExchangeRateViewState(
        val widgetState: WidgetState = WidgetState.LOADING,
        val exchangeRateList: List<ExchangeRateDMO> = emptyList(),
        val updatedDate: String = "",
        val errorMessage: String? = ""
    ) : ViewState

    @Immutable
    sealed interface ExchangeRateViewEvent : ViewEvent {
        data object GetExchangeRateList : ExchangeRateViewEvent

        data class GetExchangeRateListSuccess(
            val exchangeRateList: DataListExchangeRateDMO?,
        ) : ExchangeRateViewEvent

        data class GetExchangeRateListFailed(
            val errorCode: String,
            val errorMessage: String?,
        ) : ExchangeRateViewEvent

        data object OnRetryGetExchangeRateList : ExchangeRateViewEvent

        data object OnClickSeeMore : ExchangeRateViewEvent
    }

    @Immutable
    sealed interface ExchangeRateViewEffect : SideEffect {
        data object FetchExchangeRateList : ExchangeRateViewEffect

        data object NavigateToExchangeRateRoute : ExchangeRateViewEffect, UIEffect
    }

    override fun reduce(
        previousState: ExchangeRateViewState,
        event: ExchangeRateViewEvent,
    ): Pair<ExchangeRateViewState, ExchangeRateViewEffect?> =

        when (event) {
            is ExchangeRateViewEvent.GetExchangeRateListSuccess -> {
                val exchangeRateList = event.exchangeRateList?.items
                if (exchangeRateList.isNullOrEmpty()) {
                    previousState.copy(
                        widgetState = WidgetState.NOCONTENT,
                        errorMessage = null,
                    ) to null
                } else {
                    previousState.copy(
                        exchangeRateList = exchangeRateList,
                        updatedDate = getCurrentDate(pattern = FORMAT_DD_MM_YYYY_HH_MM_SS),
                        widgetState = WidgetState.CONTENT,
                        errorMessage = null,
                    ) to null
                }
            }

            is ExchangeRateViewEvent.GetExchangeRateListFailed -> {
                val screenState =
                    if (event.errorCode.contains(HomePageConstants.ACCESS_FORBIDDEN_CODE)) {
                        WidgetState.NO_ACCOUNT_CIF
                    } else {
                        WidgetState.ERROR
                    }
                previousState.copy(
                    widgetState = screenState,
                    errorMessage = event.errorMessage,
                ) to null
            }

            ExchangeRateViewEvent.OnRetryGetExchangeRateList, ExchangeRateViewEvent.GetExchangeRateList ->
                previousState.copy(
                    widgetState = WidgetState.LOADING,
                ) to ExchangeRateViewEffect.FetchExchangeRateList

            ExchangeRateViewEvent.OnClickSeeMore -> previousState to ExchangeRateViewEffect.NavigateToExchangeRateRoute
        }
}
