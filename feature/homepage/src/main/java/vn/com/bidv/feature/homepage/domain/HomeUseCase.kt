package vn.com.bidv.feature.homepage.domain

import vn.com.bidv.feature.homepage.data.HomepageRepository
import vn.com.bidv.sdkbase.domain.DomainResult
import java.util.Calendar
import javax.inject.Inject

class HomeUseCase @Inject constructor(
    private val homeRepository: HomepageRepository
) {
    suspend fun updateUserLanguage(serverLanguage: String?, clientLanguage: String): DomainResult<Boolean> {
        if (serverLanguage != clientLanguage) {
            homeRepository.updateLanguage(clientLanguage)
        }
        return DomainResult.Success(true)
    }
}