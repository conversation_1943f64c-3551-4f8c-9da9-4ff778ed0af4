package vn.com.bidv.feature.homepage.constants

object HomePageConstants {
    const val BIOMETRIC_TOUCH_ID = "TOUCH_ID"
    const val Key_Features_List = "2"
    const val Features_List = "1"
    const val RELOAD_DATA_HOME = "RELOAD_DATA_HOME"
    const val NOT_SHOW_DATA_MONEY = "*****"
    const val NOT_SHOW_DATE = "--/--/----"
    const val RELOAD_QUICK_LINK = "RELOAD_QUICK_LINK"
    const val RELOAD_WIDGET = "RELOAD_WIDGET"
    const val MAX_LENGTH_SEARCH_SERVICE = 100
    const val MAX_LENGTH_SEARCH_QUICK_LINK = 50
    const val Space_Text = " "
    //Lấy giá trị tiền gửi Widget Tổng quan
    const val DEP = "DEP"
    //Lấy giá trị tiền vay Widget Tổng quan
    const val LN = "LN"
    const val LEVER0 = "0" // sitemap
    const val LEVER1 = "1" // sitemap + quick link
    const val LEVER2 = "2" // sitemap + quick link ưu tiên
    const val LEVER3 = "3" // quick link
    const val LEVER4 = "4" // quick link ưu tiên
    val validPrioritySiteMap = setOf(LEVER0, LEVER1, LEVER2)
    val validPriorityQuickLink = setOf(LEVER1, LEVER2, LEVER3, LEVER4)
    val validPriorityNormalQuickLink = setOf(LEVER1, LEVER3)
    val validPriorityKeyQuickLink = setOf(LEVER2, LEVER4)
    const val RELOAD_AVATAR = "RELOAD_AVATAR"
    const val ACCESS_FORBIDDEN_CODE = "403"
    const val MAKE_CALL_ERROR = "MAKE_CALL_ERROR"
    const val SEND_EMAIL_ERROR = "SEND_EMAIL_ERROR"

    enum class ActionType {
        CALL,
        EMAIL,
    }
}