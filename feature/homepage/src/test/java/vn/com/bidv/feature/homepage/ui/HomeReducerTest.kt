package vn.com.bidv.feature.homepage.ui

import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.feature.common.domain.data.UserResDMO
import vn.com.bidv.feature.homepage.ui.home.HomeReducer

class HomeReducerTest {

    private lateinit var reducer: HomeReducer

    @Before
    fun setUp() {
        reducer = HomeReducer()
    }

    @Test
    fun `test OnInitWelcomeUser event`() {
        val initialState = HomeReducer.HomeViewState()
        val event = HomeReducer.HomeViewEvent.OnInitWelcomeUser

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(isInitSuccess = true), newState)
        assertEquals(HomeReducer.HomeViewEffect.GetWelcomeUserInfoFromStorage, effect)
    }

    @Test
    fun `test GetWelcomeUserInfoFromStorageSuccess event`() {
        val initialState = HomeReducer.HomeViewState()
        val userInfo = UserResDMO()
        val quickLinks = listOf(PermissionResDMO())
        val widgets = listOf("Widget1")
        val event = HomeReducer.HomeViewEvent.GetWelcomeUserInfoFromStorageSuccess(userInfo, quickLinks, widgets)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(userInfo = userInfo, quickLinks = quickLinks, widgets = widgets), newState)
        assertEquals(HomeReducer.HomeViewEffect.UpdateFCMIdSideEffect, effect)
    }

    @Test
    fun `test UpdateQuickLink event`() {
        val initialState = HomeReducer.HomeViewState()
        val event = HomeReducer.HomeViewEvent.UpdateQuickLink

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState, newState)
        assertEquals(HomeReducer.HomeViewEffect.UpdateQuickLinkSideEffect, effect)
    }

    @Test
    fun `test UpdateQuickLinkSuccess event`() {
        val initialState = HomeReducer.HomeViewState()
        val quickLinks = listOf(PermissionResDMO())
        val event = HomeReducer.HomeViewEvent.UpdateQuickLinkSuccess(quickLinks)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(quickLinks = quickLinks), newState)
        assertEquals(null, effect)
    }

    @Test
    fun `test OnUpdateLoginStatusSuccess event`() {
        val initialState = HomeReducer.HomeViewState()
        val event = HomeReducer.HomeViewEvent.OnUpdateLoginStatusSuccess

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState, newState)
        assertEquals(HomeReducer.HomeViewEffect.UpdateLoginStatusSuccess, effect)
    }

    @Test
    fun `test OnUpdateUserLanguage event`() {
        val initialState = HomeReducer.HomeViewState()
        val event = HomeReducer.HomeViewEvent.OnUpdateUserLanguage

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState, newState)
        assertEquals(HomeReducer.HomeViewEffect.UpdateUserLanguage, effect)
    }

    @Test
    fun `test UpdateWidget event`() {
        val initialState = HomeReducer.HomeViewState()
        val event = HomeReducer.HomeViewEvent.UpdateWidget

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState, newState)
        assertEquals(HomeReducer.HomeViewEffect.UpdateWidgetSideEffect, effect)
    }

    @Test
    fun `test UpdateWidgetSuccess event`() {
        val initialState = HomeReducer.HomeViewState()
        val widgets = listOf("Widget1")
        val event = HomeReducer.HomeViewEvent.UpdateWidgetSuccess(widgets)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(widgets = widgets), newState)
        assertEquals(null, effect)
    }

    @Test
    fun `test OnLogout event`() {
        val initialState = HomeReducer.HomeViewState()
        val event = HomeReducer.HomeViewEvent.OnLogout

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState, newState)
        assertEquals(HomeReducer.HomeViewEffect.Logout, effect)
    }

    @Test
    fun `test OnLogoutSuccess event`() {
        val initialState = HomeReducer.HomeViewState()
        val event = HomeReducer.HomeViewEvent.OnLogoutSuccess

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState, newState)
        assertEquals(HomeReducer.HomeViewEffect.LogoutSuccess, effect)
    }
}