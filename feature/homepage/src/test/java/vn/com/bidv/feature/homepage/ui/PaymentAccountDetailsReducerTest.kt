package vn.com.bidv.feature.homepage.ui

import vn.com.bidv.feature.homepage.ui.paymentaccountdetails.PaymentAccountDetailsReducer
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import vn.com.bidv.feature.common.domain.data.AccountDMO
import vn.com.bidv.feature.homepage.domain.WidgetState
import vn.com.bidv.feature.homepage.domain.model.FinAcctDetailDMO
import vn.com.bidv.feature.homepage.domain.model.AccountBalanceDMO
import vn.com.bidv.designsystem.component.feedback.bottomsheet.SearchDialogState

class PaymentAccountDetailsReducerTest {

    private lateinit var reducer: PaymentAccountDetailsReducer

    @Before
    fun setUp() {
        reducer = PaymentAccountDetailsReducer()
    }

    @Test
    fun `test OnFetchData event`() {
        val initialState = PaymentAccountDetailsReducer.PaymentAccountDetailsViewState()
        val event = PaymentAccountDetailsReducer.PaymentAccountDetailsViewEvent.OnFetchData

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(state = WidgetState.LOADING, isInitData = true), newState)
        assertEquals(PaymentAccountDetailsReducer.PaymentAccountDetailsViewEffect.GetFinAcctDetailViewEffect, effect)
    }

    @Test
    fun `test GetFinAcctDetailFail event`() {
        val initialState = PaymentAccountDetailsReducer.PaymentAccountDetailsViewState()
        val event = PaymentAccountDetailsReducer.PaymentAccountDetailsViewEvent.GetFinAcctDetailFail(messageError = null)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(state = WidgetState.ERROR, isInitData = false), newState)
        assertEquals(null, effect)
    }

    @Test
    fun `test GetFinAcctDetailSuccess event`() {
        val initialState = PaymentAccountDetailsReducer.PaymentAccountDetailsViewState()
        val finAccDetail = FinAcctDetailDMO()
        val event = PaymentAccountDetailsReducer.PaymentAccountDetailsViewEvent.GetFinAcctDetailSuccess(finAccDetail)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(state = WidgetState.NOCONTENT, finAccDetail = finAccDetail), newState)
        assertEquals(null, effect)
    }

    @Test
    fun `test SetShowData event`() {
        val initialState = PaymentAccountDetailsReducer.PaymentAccountDetailsViewState()
        val event = PaymentAccountDetailsReducer.PaymentAccountDetailsViewEvent.SetShowData(true)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(state = WidgetState.LOADING, isShowData = true), newState)
        assertEquals(PaymentAccountDetailsReducer.PaymentAccountDetailsViewEffect.GetAccountBalanceViewEffect(null), effect)
    }

    @Test
    fun `test SetShowBottomSheet event`() {
        val initialState = PaymentAccountDetailsReducer.PaymentAccountDetailsViewState()
        val account = AccountDMO()
        val event = PaymentAccountDetailsReducer.PaymentAccountDetailsViewEvent.SetShowBottomSheet(true, account)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(isShowBottomSheetAccount = true, bottomSheetState = SearchDialogState.LOADING), newState)
        assertEquals(PaymentAccountDetailsReducer.PaymentAccountDetailsViewEffect.GetDataAccount(null), effect)
    }

    @Test
    fun `test GetListAccountFail event`() {
        val initialState = PaymentAccountDetailsReducer.PaymentAccountDetailsViewState()
        val event = PaymentAccountDetailsReducer.PaymentAccountDetailsViewEvent.GetListAccountFail

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(bottomSheetState = SearchDialogState.ERROR), newState)
        assertEquals(null, effect)
    }

    @Test
    fun `test GetListAccountSuccess event`() {
        val initialState = PaymentAccountDetailsReducer.PaymentAccountDetailsViewState()
        val listAccount = listOf(AccountDMO())
        val event = PaymentAccountDetailsReducer.PaymentAccountDetailsViewEvent.GetListAccountSuccess(listAccount, null)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(bottomSheetState = SearchDialogState.CONTENT, listAccount = listAccount), newState)
        assertEquals(null, effect)
    }

    @Test
    fun `test SetDefaultAccountSuccess event`() {
        val initialState = PaymentAccountDetailsReducer.PaymentAccountDetailsViewState()
        val event = PaymentAccountDetailsReducer.PaymentAccountDetailsViewEvent.SetDefaultAccountSuccess

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(state = WidgetState.LOADING, isShowData = false, accountBalance = null), newState)
        assertEquals(PaymentAccountDetailsReducer.PaymentAccountDetailsViewEffect.GetFinAcctDetailViewEffect, effect)
    }

    @Test
    fun `test GetAccountBalanceSuccess event`() {
        val initialState = PaymentAccountDetailsReducer.PaymentAccountDetailsViewState()
        val accountBalance = AccountBalanceDMO()
        val event = PaymentAccountDetailsReducer.PaymentAccountDetailsViewEvent.GetAccountBalanceSuccess(accountBalance)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(state = WidgetState.CONTENT, accountBalance = accountBalance), newState)
        assertEquals(null, effect)
    }
}