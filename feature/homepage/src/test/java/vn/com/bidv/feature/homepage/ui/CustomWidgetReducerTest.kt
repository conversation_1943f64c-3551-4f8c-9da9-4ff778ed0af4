package vn.com.bidv.feature.homepage.ui

import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import vn.com.bidv.feature.homepage.constants.WidgetType
import vn.com.bidv.designsystem.ui.listwithloadmorev2.model.ModelCheckAble
import vn.com.bidv.feature.homepage.ui.customwidget.CustomWidgetReducer

class CustomWidgetReducerTest {

    private lateinit var reducer: CustomWidgetReducer

    @Before
    fun setUp() {
        reducer = CustomWidgetReducer()
    }

    @Test
    fun `OnInitDataWidget event should set isInitSuccess to true and trigger GetWidgetFromStorage effect`() {
        val initialState = CustomWidgetReducer.CustomWidgetViewState()
        val event = CustomWidgetReducer.CustomWidgetViewEvent.OnInitDataWidget

        val (newState, effect) = reducer.reduce(initialState, event)

        assertTrue(newState.isInitSuccess)
        assertEquals(CustomWidgetReducer.CustomWidgetViewEffect.GetWidgetFromStorage, effect)
    }

    @Test
    fun `GetWidgetFormStorageSuccess event should update state with provided data`() {
        val initialState = CustomWidgetReducer.CustomWidgetViewState()
        val listWidget = listOf(WidgetType.Widget_OverView)
        val listAllWidget = listOf(ModelCheckAble(WidgetType.Widget_OverView, true))
        val event = CustomWidgetReducer.CustomWidgetViewEvent.GetWidgetFormStorageSuccess(listWidget, listAllWidget, 1)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(listWidget, newState.listWidget)
        assertEquals(listAllWidget, newState.listAllWidget)
        assertTrue(newState.isEnableApplyBtn)
        assertEquals(1, newState.numberMinPickup)
        assertNull(effect)
    }

    @Test
    fun `RearrangeItemsWidget event should rearrange listWidget`() {
        val initialState = CustomWidgetReducer.CustomWidgetViewState(listWidget = listOf(WidgetType.Widget_OverView, WidgetType.Widget_TransManage))
        val event = CustomWidgetReducer.CustomWidgetViewEvent.RearrangeItemsWidget(0, 1)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(listOf(WidgetType.Widget_TransManage, WidgetType.Widget_OverView), newState.listWidget)
        assertNull(effect)
    }

    @Test
    fun `SelectItem event should trigger HandleSelectedWidget effect`() {
        val initialState = CustomWidgetReducer.CustomWidgetViewState(listAllWidget = listOf(ModelCheckAble(WidgetType.Widget_OverView, false)))
        val item = ModelCheckAble(WidgetType.Widget_OverView, false)
        val event = CustomWidgetReducer.CustomWidgetViewEvent.SelectItem(item)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(CustomWidgetReducer.CustomWidgetViewEffect.HandleSelectedWidget(initialState.listAllWidget, item), effect)
        assertEquals(initialState, newState)
    }

    @Test
    fun `HandleSelectedWidgetResult event should update listAllWidget and isEnableApplyBtn`() {
        val initialState = CustomWidgetReducer.CustomWidgetViewState(numberMinPickup = 1)
        val listAllWidget = listOf(ModelCheckAble(WidgetType.Widget_OverView, true))
        val event = CustomWidgetReducer.CustomWidgetViewEvent.HandleSelectedWidgetResult(listAllWidget)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(listAllWidget, newState.listAllWidget)
        assertTrue(newState.isEnableApplyBtn)
        assertNull(effect)
    }

    @Test
    fun `ApplySetListWidget event should trigger ApplySetListWidgetSideEffect`() {
        val initialState = CustomWidgetReducer.CustomWidgetViewState(listAllWidget = listOf(ModelCheckAble(WidgetType.Widget_OverView, true)), listWidget = listOf(WidgetType.Widget_OverView))
        val event = CustomWidgetReducer.CustomWidgetViewEvent.ApplySetListWidget

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(CustomWidgetReducer.CustomWidgetViewEffect.ApplySetListWidgetSideEffect(initialState.listAllWidget, initialState.listWidget), effect)
        assertEquals(initialState, newState)
    }

    @Test
    fun `DismissApplySetListWidget event should trigger GetListAllWidgetWhenDismiss effect`() {
        val initialState = CustomWidgetReducer.CustomWidgetViewState(listWidget = listOf(WidgetType.Widget_OverView))
        val event = CustomWidgetReducer.CustomWidgetViewEvent.DismissApplySetListWidget

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(CustomWidgetReducer.CustomWidgetViewEffect.GetListAllWidgetWhenDismiss(initialState.listWidget), effect)
        assertEquals(initialState, newState)
    }

    @Test
    fun `ApplySetListWidgetSuccess event should update listWidget`() {
        val initialState = CustomWidgetReducer.CustomWidgetViewState()
        val listWidget = listOf(WidgetType.Widget_OverView)
        val event = CustomWidgetReducer.CustomWidgetViewEvent.ApplySetListWidgetSuccess(listWidget)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(listWidget, newState.listWidget)
        assertNull(effect)
    }

    @Test
    fun `UpdateWidgetInfoByUser event should trigger UpdateWidgetInfoByUserSideEffect`() {
        val initialState = CustomWidgetReducer.CustomWidgetViewState(listWidget = listOf(WidgetType.Widget_OverView))
        val event = CustomWidgetReducer.CustomWidgetViewEvent.UpdateWidgetInfoByUser

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(CustomWidgetReducer.CustomWidgetViewEffect.UpdateWidgetInfoByUserSideEffect(initialState.listWidget), effect)
        assertEquals(initialState, newState)
    }

    @Test
    fun `UpdateWidgetInfoByUserSuccess event should trigger UpdateWidgetInfoByUserSuccessSideEffect`() {
        val initialState = CustomWidgetReducer.CustomWidgetViewState()
        val event = CustomWidgetReducer.CustomWidgetViewEvent.UpdateWidgetInfoByUserSuccess

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(CustomWidgetReducer.CustomWidgetViewEffect.UpdateWidgetInfoByUserSuccessSideEffect, effect)
        assertEquals(initialState, newState)
    }

    @Test
    fun `GetListAllWidgetWhenDismissSuccess event should update listAllWidget`() {
        val initialState = CustomWidgetReducer.CustomWidgetViewState()
        val listAllWidget = listOf(ModelCheckAble(WidgetType.Widget_OverView, true))
        val event = CustomWidgetReducer.CustomWidgetViewEvent.GetListAllWidgetWhenDismissSuccess(listAllWidget)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(listAllWidget, newState.listAllWidget)
        assertNull(effect)
    }
}