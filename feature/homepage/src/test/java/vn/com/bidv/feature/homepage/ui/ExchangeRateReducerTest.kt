import io.mockk.every
import io.mockk.mockkStatic
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import vn.com.bidv.feature.homepage.domain.WidgetState
import vn.com.bidv.feature.homepage.domain.model.DataListExchangeRateDMO
import vn.com.bidv.feature.homepage.domain.model.ExchangeRateDMO
import vn.com.bidv.feature.homepage.ui.exchangeRate.ExchangeRateReducer
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants.DateTimeConstants.FORMAT_DD_MM_YYYY_HH_MM_SS
import vn.com.bidv.sdkbase.utils.exts.getCurrentDate

class ExchangeRateReducerTest {
    private lateinit var reducer: ExchangeRateReducer

    @Before
    fun setUp() {
        reducer = ExchangeRateReducer()
        mockkStatic(::getCurrentDate)
        every { getCurrentDate(pattern = FORMAT_DD_MM_YYYY_HH_MM_SS) } returns "01-01-2024 12:00:00"
    }

    @Test
    fun `reduce should return loading state when GetExchangeRateList event occurs`() {
        val initialState = ExchangeRateReducer.ExchangeRateViewState()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                ExchangeRateReducer.ExchangeRateViewEvent.GetExchangeRateList,
            )

        assertEquals(WidgetState.LOADING, newState.widgetState)
        assertEquals(ExchangeRateReducer.ExchangeRateViewEffect.FetchExchangeRateList, effect)
    }

    @Test
    fun `reduce should update state with exchange rate list on success`() {
        val initialState =
            ExchangeRateReducer.ExchangeRateViewState(widgetState = WidgetState.LOADING)
        val exchangeRateList =
            listOf(ExchangeRateDMO("USD", "23000.0"), ExchangeRateDMO("JPY", "23000.0"))
        val event =
            ExchangeRateReducer.ExchangeRateViewEvent.GetExchangeRateListSuccess(
                DataListExchangeRateDMO(exchangeRateList),
            )

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(WidgetState.CONTENT, newState.widgetState)
        assertEquals(exchangeRateList, newState.exchangeRateList)
        assertEquals("01-01-2024 12:00:00", newState.updatedDate)
        assertEquals(null, effect)
    }

    @Test
    fun `reduce should set NOCONTENT when exchange rate list is empty`() {
        val initialState =
            ExchangeRateReducer.ExchangeRateViewState(widgetState = WidgetState.LOADING)
        val event =
            ExchangeRateReducer.ExchangeRateViewEvent.GetExchangeRateListSuccess(
                DataListExchangeRateDMO(emptyList()),
            )

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(WidgetState.NOCONTENT, newState.widgetState)
        assertEquals(emptyList<ExchangeRateDMO>(), newState.exchangeRateList)
        assertEquals(null, effect)
    }

    @Test
    fun `reduce should set ERROR state when GetExchangeRateListFailed event occurs`() {
        val initialState = ExchangeRateReducer.ExchangeRateViewState()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                ExchangeRateReducer.ExchangeRateViewEvent.GetExchangeRateListFailed(
                    errorMessage = "error",
                    errorCode = "403"
                ),
            )

        assertEquals(WidgetState.NO_ACCOUNT_CIF, newState.widgetState)
        assertEquals(null, effect)
    }

    @Test
    fun `reduce should return loading state when OnRetryGetExchangeRateList event occurs`() {
        val initialState = ExchangeRateReducer.ExchangeRateViewState()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                ExchangeRateReducer.ExchangeRateViewEvent.OnRetryGetExchangeRateList,
            )

        assertEquals(WidgetState.LOADING, newState.widgetState)
        assertEquals(ExchangeRateReducer.ExchangeRateViewEffect.FetchExchangeRateList, effect)
    }
}
