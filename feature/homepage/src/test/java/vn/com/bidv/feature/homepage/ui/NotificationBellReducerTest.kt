package vn.com.bidv.feature.homepage.ui

import org.junit.Assert.assertEquals
import org.junit.Test
import vn.com.bidv.feature.homepage.ui.notificationbellscreen.NotificationBellReducer
import vn.com.bidv.feature.homepage.ui.notificationbellscreen.NotificationBellReducer.NotificationBellViewState
import vn.com.bidv.feature.homepage.ui.notificationbellscreen.NotificationBellReducer.NotificationBellViewEvent
import vn.com.bidv.feature.homepage.ui.notificationbellscreen.NotificationBellReducer.NotificationBellViewEffect
import kotlin.test.assertNull

class NotificationBellReducerTest {
    private val reducer = NotificationBellReducer()

    @Test
    fun `reduce should return GetTotalNotifyUnread effect when GetTotalNotifyUnread event occurs`() {
        val initialState = NotificationBellViewState()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                NotificationBellViewEvent.GetTotalNotifyUnread,
            )
        assertEquals(initialState, newState)
        assertEquals(NotificationBellViewEffect.GetTotalNotifyUnread, effect)
    }

    @Test
    fun `reduce should update totalNotifyUnread state when GetTotalNotifyUnreadSuccess event occurs`() {
        val initialState = NotificationBellViewState()
        val (newState, effect) =
            reducer.reduce(
                initialState,
                NotificationBellViewEvent.GetTotalNotifyUnreadSuccess(
                    total = 10L
                ),
            )
        assertEquals(10L, newState.modelCountNotifyUnreadUI.totalNotifyUnread)
        assertNull(effect)
    }
}