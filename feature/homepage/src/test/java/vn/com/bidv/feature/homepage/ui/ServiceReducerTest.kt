package vn.com.bidv.feature.homepage.ui

import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import vn.com.bidv.feature.common.domain.data.PermissionResDMO
import vn.com.bidv.feature.homepage.ui.service.ServiceReducer
import vn.com.bidv.feature.homepage.ui.service.ServiceReducer.ServiceViewEffect
import vn.com.bidv.feature.homepage.ui.service.ServiceReducer.ServiceViewEvent
import vn.com.bidv.feature.homepage.ui.service.ServiceReducer.ServiceViewState

class ServiceReducerTest {

    private lateinit var reducer: ServiceReducer

    @Before
    fun setUp() {
        reducer = ServiceReducer()
    }

    @Test
    fun `test OnInitDataService event`() {
        val initialState = ServiceViewState()
        val event = ServiceViewEvent.OnInitDataService

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(isInitSuccess = true), newState)
        assertEquals(ServiceViewEffect.GetPermissionResFromStorage, effect)
    }

    @Test
    fun `test GetServicesFromStorageSuccess event`() {
        val initialState = ServiceViewState()
        val listServices = listOf(PermissionResDMO())
        val listChildrenService = listOf(PermissionResDMO())
        val event = ServiceViewEvent.GetServicesFromStorageSuccess(listServices, listChildrenService)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(listService = listServices, listChildrenService = listChildrenService), newState)
        assertEquals(null, effect)
    }

    @Test
    fun `test UpdateSearchText event`() {
        val initialState = ServiceViewState()
        val searchText = "test"
        val event = ServiceViewEvent.UpdateSearchText(searchText)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(textSearch = searchText), newState)
        assertEquals(ServiceViewEffect.GetDataSearch(listChildrenService = initialState.listService, searchText = searchText), effect)
    }

    @Test
    fun `test GetSearchListChildrenService event`() {
        val initialState = ServiceViewState()
        val listSearchChildrenService = listOf(PermissionResDMO())
        val event = ServiceViewEvent.GetSearchListChildrenService(listSearchChildrenService)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(listSearchChildrenService = listSearchChildrenService), newState)
        assertEquals(null, effect)
    }

    @Test
    fun `test SetSearchView event`() {
        val initialState = ServiceViewState()
        val event = ServiceViewEvent.SetSearchView(true)

        val (newState, effect) = reducer.reduce(initialState, event)

        assertEquals(initialState.copy(isSearchView = true), newState)
        assertEquals(null, effect)
    }
}