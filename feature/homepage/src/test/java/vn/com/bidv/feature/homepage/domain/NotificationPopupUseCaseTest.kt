package vn.com.bidv.feature.homepage.domain

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import vn.com.bidv.common.sharePreference.Storage
import vn.com.bidv.feature.common.data.utilitiesnotify.model.DataListPopupNotifyStatementResponse
import vn.com.bidv.feature.common.data.utilitiesnotify.model.PopupNotifyStatementResponse
import vn.com.bidv.feature.common.data.utilitiesnotify.model.ResultString
import vn.com.bidv.feature.homepage.common.enum.NotificationDataKeys
import vn.com.bidv.feature.homepage.data.NotificationPopupRepository
import vn.com.bidv.feature.homepage.domain.notificationpopupdmo.AdditionalInfo
import vn.com.bidv.feature.homepage.domain.notificationpopupdmo.ModelListPopupNotifyStatementResponseDMO
import vn.com.bidv.feature.homepage.domain.notificationpopupdmo.ModelNotificationPopupDMO
import vn.com.bidv.feature.homepage.domain.notificationpopupdmo.ModelPopupNotifyUserStatementRequestDMO
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.NetworkStatusCode.CONNECT_TIME_OUT
import vn.com.bidv.sdkbase.data.LocalRepository
import vn.com.bidv.sdkbase.domain.DomainResult
import kotlin.test.assertIs
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class NotificationPopupUseCaseTest {
    private val repository: NotificationPopupRepository = mockk()
    private val localRepo: LocalRepository = mockk()
    private val useCase = NotificationPopupUseCase(repository, localRepo)
    private lateinit var mockPopupListResponse: List<PopupNotifyStatementResponse>
    private lateinit var mockPopupListDMO: List<ModelNotificationPopupDMO>
    private val typeListPopup = object : TypeToken<List<ModelNotificationPopupDMO>>() {}.type
    private val mockGson = mockk<Gson>()

    @Before
    fun initData() {
        mockkObject(Storage)
        every { localRepo.setPopupLoadedStatus(any()) } answers { }
        mockPopupListResponse = listOf(
            mockPopupDataResponse(1L, "Y", 1, "ID"),
            mockPopupDataResponse(2L, "N", 3, "ID"),
            mockPopupDataResponse(3L, "N", 5, ""),
            mockPopupDataResponse(4L, "Y", 2, ""),
            mockPopupDataResponse(5L, "", 4, ""),
        )
        mockPopupListDMO = listOf(
            mockPopupDataDMO(1L, true, 1, "ID"),
            mockPopupDataDMO(2L, false, 3, "ID"),
            mockPopupDataDMO(3L, false, 5, ""),
            mockPopupDataDMO(4L, true, 2, ""),
            mockPopupDataDMO(5L, false, 4, ""),
        )
    }

    private fun mockPopupDataResponse(
        id: Long,
        collectFeedback: String,
        priority: Int,
        redirectId: String
    ) = PopupNotifyStatementResponse(
        id = id,
        title = "popup $id",
        content = "popup content $id",
        collectFeedback = collectFeedback,
        priorityLevel = priority,
        additionalInfo = "{'redirectId':'$redirectId'}"
    )

    private fun mockPopupDataDMO(
        id: Long,
        collectFeedback: Boolean,
        priority: Int,
        redirectId: String
    ) = ModelNotificationPopupDMO(
        id = id,
        title = "popup $id",
        content = "popup content $id",
        collectFeedback = collectFeedback,
        priorityLevel = priority,
        additionalInfo = AdditionalInfo(null)
    )

    @Test
    fun `getNotificationPopup should return success when get popup list from Storage`() =
        runTest {
            val fakeJson = Gson().toJson(mockPopupListDMO)
            every { localRepo.setPopupLoadedStatus(any()) } answers { }
            every { Storage.get(NotificationDataKeys.NOTIFICATION_POPUP_LIST) } returns fakeJson
            every { mockGson.fromJson<List<ModelNotificationPopupDMO>>(fakeJson, typeListPopup) } returns mockPopupListDMO
            val result = useCase.getNotificationPopup(isFromStorage = true)
            assertTrue(result is DomainResult.Success)
            val data = result.data
            assertIs<ModelListPopupNotifyStatementResponseDMO>(data)
            assertEquals(5, data.listNotificationPopup?.size)
            assertEquals(1L, data.listNotificationPopup?.first()?.id)
        }

    @Test
    fun `getNotificationPopup should return success when repository returns ModelListPopupNotifyStatementResponseDMO`() =
        runTest {
            val mockPopupResponse = DataListPopupNotifyStatementResponse(
                items = mockPopupListResponse,
                total = mockPopupListResponse.size.toLong()
            )
            coEvery { repository.getPopupUser() } returns NetworkResult.Success(mockPopupResponse)
            val result = useCase.getNotificationPopup(isFromStorage = false)
            assertTrue(result is DomainResult.Success)
            val data = result.data
            assertIs<ModelListPopupNotifyStatementResponseDMO>(data)
            assertEquals(5, data.listNotificationPopup?.size)
            assertEquals(1L, data.listNotificationPopup?.first()?.id)
            coVerify { repository.getPopupUser() }
        }

    @Test
    fun `getNotificationPopup should return sorted list when repository returns ModelListPopupNotifyStatementResponseDMO`() =
        runTest {
            val mockPopupResponse = DataListPopupNotifyStatementResponse(
                items = mockPopupListResponse,
                total = mockPopupListResponse.size.toLong()
            )
            coEvery { repository.getPopupUser() } returns NetworkResult.Success(mockPopupResponse)
            val result = useCase.getNotificationPopup(false)
            assertTrue(result is DomainResult.Success)
            val data = result.data
            assertNotNull(data)
            assertEquals(1L, data.listNotificationPopup?.get(0)?.id)
            assertEquals(4L, data.listNotificationPopup?.get(1)?.id)
            assertEquals(2L, data.listNotificationPopup?.get(2)?.id)
            assertEquals(5L, data.listNotificationPopup?.get(3)?.id)
            assertEquals(3L, data.listNotificationPopup?.get(4)?.id)
            assertEquals(true, data.listNotificationPopup?.get(0)?.collectFeedback)
            assertEquals(false, data.listNotificationPopup?.get(2)?.collectFeedback)
        }

    @Test
    fun `getNotificationPopup should return empty list when repository returns empty data`() =
        runTest {
            val mockPopupResponse = DataListPopupNotifyStatementResponse(
                items = emptyList(),
                total = 0
            )
            coEvery { repository.getPopupUser() } returns NetworkResult.Success(mockPopupResponse)
            val result = useCase.getNotificationPopup(false)
            assertTrue(result is DomainResult.Success)
            val data = result.data
            assertIs<ModelListPopupNotifyStatementResponseDMO>(data)
            assertEquals(0, data.listNotificationPopup?.size)
            coVerify { repository.getPopupUser() }
        }

    @Test
    fun `getNotificationPopup should return error when repository returns fails`() =
        runTest {
            coEvery { repository.getPopupUser() } returns NetworkResult.Error(
                errorMessage = "CONNECT_TIME_OUT",
                errorCode = CONNECT_TIME_OUT
            )
            val result = useCase.getNotificationPopup(false)
            assertTrue(result is DomainResult.Error)
            assertEquals(CONNECT_TIME_OUT, result.errorCode)
            assertEquals("CONNECT_TIME_OUT", result.errorMessage)
            coVerify { repository.getPopupUser() }
        }

    @Test
    fun `updateStatusNotificationPopup should call repository and return success`() = runTest {
        val mockRequestDMO = ModelPopupNotifyUserStatementRequestDMO(
            id = 1L,
            type = "DISPLAY"
        )
        coEvery {
            repository.updateStatusPopupUser(mockRequestDMO)
        } returns NetworkResult.Success(ResultString())

        val result = useCase.updateStatusNotificationPopup(mockRequestDMO.id, mockRequestDMO.type)
        assertTrue(result is DomainResult.Success)
        coVerify { repository.updateStatusPopupUser(mockRequestDMO) }
    }

    @Test
    fun `updateStatusNotificationPopup should return success when id or type is null`() = runTest {
        val result = useCase.updateStatusNotificationPopup(null, "DISPLAY")
        assertTrue(result is DomainResult.Success)
        coVerify(exactly = 0) { repository.updateStatusPopupUser(any()) }
    }
}