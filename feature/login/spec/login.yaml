openapi: 3.0.1
info:
  title: IBank Service OpenAPI
  description: IBank Service OpenAPI
  termsOfService: ''
  contact:
    email: ''
  license:
    name: BIDV
    url: http://bidv.com.vn
  version: v1.0
servers:
- url: http://api-specs-dev-ibank2.apps.devttptnhs.ldapudtest.com
  description: Generated server url
paths:
  /auth/settings/user-info/personal/1.0:
    post:
      tags:
        - Others
      summary: User Info Personal Settings
      description: User Info Personal Settings
      operationId: userPersonalInfoSettings
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompositeOtpResDto'
  /auth/settings/user-info/1.0:
    post:
      tags:
        - Others
      summary: User Info Settings
      description: User Info Settings
      operationId: userInfoSettings
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserInfoSettingsResponse'
  /auth/security-question/save/1.0:
    post:
      tags:
      - login
      summary: Create Secret Question Save Request
      description: API to initiate a secret question save request
      operationId: secQnSave
      parameters: null
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SecQnSaveRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NetworkResponse'
  /auth/pw/update-expire:
    post:
      tags:
      - login
      summary: Update Expire Password Request
      description: API to update expire password request
      operationId: pwUpdateExpire
      parameters: null
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NetworkResponse'
  /auth/pw/wrong-attempts/1.0:
    post:
      tags:
        - "Password"
      summary: "Get Wrong Password Attempts"
      description: "API to get Get Wrong Password Attempts"
      operationId: "getWrongPwAttempts"
      responses:
        200:
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/NetworkResponse"
  /auth/pw/forgot/security-questions/1.0:
    post:
      tags:
        - Password
      summary: Verify SecQn Forgot Password Request
      description: API to verify SecQn of the forgot password request
      operationId: pwForgotSecQn
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SecQnForgotPwRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecQnForgotPwResponseData'
  /auth/pw/forgot/create/1.0:
    post:
      tags:
        - Password
      summary: Create Forgot Password Request
      description: API to initiate a forgot password request
      operationId: pwForgotCreate
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateForgotPwRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateForgotPwResponseData'
  /auth/pw/change/positive/create/1.0:
    post:
      tags:
        - "Password"
      summary: "Positive Change Password Request"
      description: "API to initiate a positive change password request"
      operationId: "pwPositiveChangeCreate"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreatePositiveChangePwRequest"
        required: "true"
      responses:
        200:
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreatePositiveChangePwResDto"
  /auth/otp/secured/verify/1.0:
    post:
      tags:
        - "OTP"
      summary: "Verify Secured Otp Request"
      description: "API to verify the secured otp request"
      operationId: "otpSecuredVerify"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OtpSecureVerifyRequest"
        required: "true"
      responses:
        200:
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/NetworkResponse"
  /auth/otp/secured/resend/1.0:
    post:
      tags:
        - "OTP"
      summary: "Resend Secured Otp Request"
      description: "API to Resend the secured otp request"
      operationId: "otpSecuredResend"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OtpSecuredResendRequest"
        required: "true"
      responses:
        200:
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateOtpResDto"
  /auth/pw/change/create:
    post:
      tags:
      - login
      summary: Create Change Password Request
      description: API to initiate a change password request
      operationId: pwChangeCreate
      parameters: null
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateChangePwRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateOtpResDto'
  /auth/otp/verify:
    post:
      tags:
      - login
      summary: Verify Otp Request
      description: API to verify the otp request
      operationId: otpVerify
      parameters: null
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OtpVerifyRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponseDto'
  /auth/otp/secured/verify/user-info/personal/1.0:
    post:
      tags:
        - OTP
      summary: Verify Secured Otp Personal Info Request
      description: API to verify the secured otp personal info request
      operationId: otpSecuredVerifyPersonalInfo
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OtpSecuredVerifyPersonalInfoRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPersonalInfoData'
  /auth/otp/resend:
    post:
      tags:
      - login
      summary: Resend Otp Request
      description: API to Resend the otp request
      operationId: otpResend
      parameters: null
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OtpResendRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateOtpResDto'
  /auth/mobile/login:
    post:
      tags:
      - login
      summary: Mobile Login Request
      description: API to Login on Mobile Device
      operationId: login
      parameters: null
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponseDto'
  /auth/user-info/1.0:
    post:
      tags:
      - login
      summary: Get User Info
      description: API to get user info
      operationId: getUserInfo
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUserInfoResponse'
      parameters: null
  /auth/security-question/get-all/1.0:
    post:
      tags:
      - login
      summary: Get All Secret Question
      description: API to get all secret question
      operationId: secQnGetAll
      parameters: null
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SecQnGetAllResponse'
  /auth/login/biometric/verify/1.0:
    post:
      tags:
        - "Login"
      summary: "Biometric login verify"
      description: "Biometric login verify"
      operationId: "biometricLoginVerify"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BiometricVerifyRequest"
        required: "true"
      responses:
        200:
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LoginResponseDto"
  /auth/login/biometric/challenge/1.0:
    post:
      tags:
        - "Login"
      summary: "Biometric login challenge"
      description: "Biometric login challenge"
      operationId: "biometricLoginChallenge"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BiometricChallengeRequest"
        required: "true"
      responses:
        200:
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BiometricChallengeResponseData"
  /auth/info/smart-otp/1.0:
    post:
      tags:
        - Info
      summary: Get Smart Otp Info
      description: API to get smart otp info
      operationId: getSmartOtpInfo
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSmartOtpInfoResponseData'
  /auth/biometric/on/1.0:
    post:
      tags:
        - "Biometric"
      summary: "Biometric turn on"
      description: "Biometric turn on"
      operationId: "biometricOn"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BiometricOnRequest"
        required: "true"
      responses:
        200:
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CompositeOtpResDto"
  /auth/biometric/off/1.0:
    post:
      tags:
        - "Biometric"
      summary: "Biometric turn off"
      description: "Biometric turn off"
      operationId: "biometricOff"
      responses:
        200:
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/NetworkResponse"
  /auth/logout/1.0:
    post:
      tags:
        - login
      summary: Mobile Logout Request
      description: API to Logout on Mobile Device
      operationId: logout
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Result'
components:
  schemas:
    SecQnSaveRequest:
      required:
      - securityQuestions
      type: object
      properties:
        securityQuestions:
          type: array
          items:
            $ref: '#/components/schemas/SecurityQuestionAnswerDto'
    SecurityQuestionAnswerDto:
      type: object
      properties:
        questionCode:
          type: string
          example: SEC_QN_01
        answer:
          type: string
          example: Hanoi
    ResponseError:
      type: object
      properties:
        errorCode:
          type: string
        errorDesc:
          type: string
        refVal:
          type: object
      description: Chi tiết lỗi
    Result:
      required:
      - code
      - message
      - status
      - traceId
      type: object
      properties:
        status:
          type: integer
          description: status code
          format: int32
          example: 0
        code:
          type: string
          description: Error code detail
          example: '0'
        message:
          type: string
          description: Error description
          example: Success
        errors:
          type: array
          description: Addition list error
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
          example: '123456'
        data:
          type: object
          description: data body
      description: Base Result
    CreateChangePwRequest:
      type: object
      properties:
        method:
          type: string
          example: FIRST_TIME_CHANGE_PW
        deviceId:
          type: string
          example: 2864272bc9f10865
        username:
          type: string
          example: 100682system
        currentPassword:
          type: string
          example: oldPassword123
        newPassword:
          type: string
          example: newPassword123
        confirmNewPassword:
          type: string
          example: newPassword123
    CreateChangePwResponse:
      required:
      - code
      - message
      - status
      - traceId
      type: object
      properties:
        status:
          type: integer
          description: status code
          format: int32
          example: 0
        code:
          type: string
          description: Error code detail
          example: '0'
        message:
          type: string
          description: Error description
          example: Success
        errors:
          type: array
          description: Addition list error
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
          example: '123456'
        data:
          $ref: '#/components/schemas/CreateOtpResDto'
    CreateOtpResDto:
      type: object
      properties:
        transId:
          type: string
          example: '123456789'
        resendOtpTime:
          type: string
          description: Time for resending OTP
          example: '30'
        otpActiveCount:
          type: string
          description: Active OTP count
          example: '120'
        maskedPhoneOrEmail:
          type: string
          description: Masked phone or email
          example: '*********114'
        content:
          type: string
          description: Content
          example: >-
            Vui lòng nhập mã OTP đã được gửi đến số điện thoại *********114 để
            xác thực kích hoạt tài khoản.
        trustedDevice:
          type: boolean
          description: Trusted Device
          example: true
    OtpVerifyRequest:
      type: object
      properties:
        method:
          type: string
          example: FIRST_TIME_CHANGE_PW
        username:
          type: string
          example: 100682system
        transId:
          type: string
          example: '123456789'
        otpNum:
          type: string
          example: '123456'
        deviceId:
          type: string
          example: 2864272bc9f10865
        fcmId:
          type: string
          example: fcmIdExample123
    AccessTokenResponseDto:
      type: object
      properties:
        access_token:
          type: string
          description: Access token for authentication
        expires_in:
          type: integer
          description: Token expiration time in seconds
          format: int32
        refresh_expires_in:
          type: integer
          description: Refresh token expiration time in seconds
          format: int32
        refresh_token:
          type: string
          description: Refresh token
        token_type:
          type: string
          description: Type of the token
        id_token:
          type: string
          description: ID token
        not-before-policy:
          type: integer
          description: Not-before policy
          format: int32
        session_state:
          type: string
          description: Session state
        scope:
          type: string
          description: Scope of the token
        error:
          type: string
          description: Error message, if any
        error_description:
          type: string
          description: Description of the error
        error_uri:
          type: string
          description: URI for error details
    Chronology:
      type: object
      properties:
        id:
          type: string
        calendarType:
          type: string
    Duration:
      type: object
      properties:
        seconds:
          type: integer
          format: int64
        nano:
          type: integer
          format: int32
        negative:
          type: boolean
        zero:
          type: boolean
        units:
          type: array
          items:
            $ref: '#/components/schemas/TemporalUnit'
    Instant:
      type: object
      properties:
        nano:
          type: integer
          format: int32
        epochSecond:
          type: integer
          format: int64
    LoginResponseDto:
      type: object
      properties:
        status:
          type: string
          description: Status of the login operation
        langCode:
          type: string
          description: Language code
        userId:
          type: string
          description: User ID
        username:
          type: string
          description: User name
        userFullName:
          type: string
          description: Full name of the user
        forceChangePw:
          type: boolean
          description: Indicates if the user must change their password
        cifResidencyStatus:
          type: string
          description: CIF residency status
        cifName:
          type: string
          description: Name associated with the CIF
        userRole:
          type: string
          description: Role of the user
        isSecurityQuestionSet:
          type: boolean
          description: Indicates if security questions are set
        smartOtp:
          $ref: '#/components/schemas/SmartOtpLoginResponseDto'
        otp:
          $ref: '#/components/schemas/CreateOtpResDto'
        token:
          $ref: '#/components/schemas/AccessTokenResponseDto'
      description: data body
    OtpVerifyResponse:
      required:
      - code
      - message
      - status
      - traceId
      type: object
      properties:
        status:
          type: integer
          description: status code
          format: int32
          example: 0
        code:
          type: string
          description: Error code detail
          example: '0'
        message:
          type: string
          description: Error description
          example: Success
        errors:
          type: array
          description: Addition list error
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
          example: '123456'
        data:
          $ref: '#/components/schemas/LoginResponseDto'
    SmartOtpLoginResponseDto:
      type: object
      properties:
        isSmartOtpSet:
          type: boolean
          description: Indicates if Smart OTP is set
          example: true
        differentIdNumExists:
          type: boolean
          description: Indicates if a different ID number exists
          example: false
        regStatus:
          type: string
          description: Registration status
          example: ACTIVE
        lockTime:
          type: string
          description: Time of lock
          example: '2020-01-01T00:00:00.000+00:00'
        deviceId:
          type: string
          description: Device ID
          example: 368de9117c7b4707a87d05d7ee487d6c
        activationLockTime:
          type: string
          description: Activation lock time
          example: '2020-01-01T00:00:00.000+00:00'
        kycRequired:
          type: string
          description: Indicates if KYC is required
          example: 'Y'
    OtpSecuredVerifyPersonalInfoRequest:
      required:
        - otpNum
        - transId
      type: object
      properties:
        transId:
          type: string
          example: '123456789'
        otpNum:
          type: string
          example: '123456'
    UserPersonalInfoData:
      type: object
      properties:
        mobile:
          type: string
          example: 0987654321
        emailOtpReg:
          type: boolean
          example: true
        otpEmail:
          type: string
          example: <EMAIL>
        email:
          type: string
          example: <EMAIL>
        idType:
          type: string
          example: CCCD
        idNum:
          type: string
          example: '123456789'
        nat:
          type: string
          example: VN
        idIssueDate:
          type: string
          example: 2020/01/01
        idExpType:
          type: boolean
          example: true
        idExpDate:
          type: string
          example: 2030/01/01
    TemporalUnit:
      type: object
      properties:
        dateBased:
          type: boolean
        timeBased:
          type: boolean
        duration:
          $ref: '#/components/schemas/Duration'
        durationEstimated:
          type: boolean
    ZoneOffset:
      type: object
      properties:
        totalSeconds:
          type: integer
          format: int32
        id:
          type: string
        rules:
          $ref: '#/components/schemas/ZoneRules'
    ZoneOffsetTransition:
      type: object
      properties:
        offsetBefore:
          $ref: '#/components/schemas/ZoneOffset'
        offsetAfter:
          $ref: '#/components/schemas/ZoneOffset'
        duration:
          $ref: '#/components/schemas/Duration'
        gap:
          type: boolean
        dateTimeBefore:
          $ref: '#/components/schemas/LocalDateTime'
        dateTimeAfter:
          $ref: '#/components/schemas/LocalDateTime'
        overlap:
          type: boolean
        instant:
          $ref: '#/components/schemas/Instant'
    ZoneOffsetTransitionRule:
      type: object
      properties:
        month:
          type: string
          enum:
          - JANUARY
          - FEBRUARY
          - MARCH
          - APRIL
          - MAY
          - JUNE
          - JULY
          - AUGUST
          - SEPTEMBER
          - OCTOBER
          - NOVEMBER
          - DECEMBER
        timeDefinition:
          type: string
          enum:
          - UTC
          - WALL
          - STANDARD
        standardOffset:
          $ref: '#/components/schemas/ZoneOffset'
        offsetBefore:
          $ref: '#/components/schemas/ZoneOffset'
        offsetAfter:
          $ref: '#/components/schemas/ZoneOffset'
        dayOfWeek:
          type: string
          enum:
          - MONDAY
          - TUESDAY
          - WEDNESDAY
          - THURSDAY
          - FRIDAY
          - SATURDAY
          - SUNDAY
        dayOfMonthIndicator:
          type: integer
          format: int32
        localTime:
          $ref: '#/components/schemas/LocalTime'
        midnightEndOfDay:
          type: boolean
    ZoneRules:
      type: object
      properties:
        fixedOffset:
          type: boolean
        transitions:
          type: array
          items:
            $ref: '#/components/schemas/ZoneOffsetTransition'
        transitionRules:
          type: array
          items:
            $ref: '#/components/schemas/ZoneOffsetTransitionRule'
    OtpResendRequest:
      type: object
      properties:
        method:
          type: string
          example: FIRST_TIME_CHANGE_PW
        username:
          type: string
          example: 100682system
        transId:
          type: string
          example: '123456789'
        deviceId:
          type: string
          example: 2864272bc9f10865
    LoginRequest:
      type: object
      properties:
        password:
          type: string
          description: Password
          example: '123123123'
        username:
          type: string
          description: Username of the user
          example: 100682system
        deviceId:
          type: string
          description: Device ID
          example: 2864272bc9f10865
        clientPublicKey:
          type: string
          example: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0L8aOXk/wL9InjIb2QQUHHDreXL3aJpQucsE1PMczj4RoKGuEmcxGIopTo6gHc/K9XN7NxuK9o7RKnxwqRts9ILaXr8JEzHcnVmuA2f/Ld3uoYO+Tn86u9mJVHhEQpqqoMKhsMUj9EBXe1eY64c0W8Ndpc5LsfFYng/mvi/hICKqbbFUu7TP48qO9rzFlZjmqHgcyhiOIZFAqGEHBMr++pmBmIdQa1z9u3sqV8nKB47GvCP7wfes7D7RLW8DxDGrZ1IXqbhW5QojoVw4c2gfHsna3hzeXwGaX/tF/CI8c8PRU5rieTz/Vf6jqiGbvDsFsWxOv2ZUbOeQlLao3sNikQIDAQAB
    LoginResponse:
      required:
        - code
        - message
        - status
        - traceId
      type: object
      properties:
        status:
          type: integer
          description: status code
          format: int32
          example: 0
        code:
          type: string
          description: Error code detail
          example: '0'
        message:
          type: string
          description: Error description
          example: Success
        errors:
          type: array
          description: Addition list error
          items:
            $ref: '#/components/schemas/ResponseError'
        traceId:
          type: string
          description: Trace ID
          example: '123456'
        data:
          $ref: '#/components/schemas/LoginResponseDto'
    DefaultAccResDto:
      type: object
      properties:
        accNo:
          type: string
          description: accNo
          example: '1200046752'
        grpType:
          type: string
          description: grpType
          example: ACC_INQ
        resrcType:
          type: string
          description: resrcType
          example: DD_ACC
    GetUserInfoResponse:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/UserResDto'
        permissions:
          type: array
          items:
            $ref: '#/components/schemas/PermissionResDto'
        defaultAcc:
          type: array
          items:
            $ref: '#/components/schemas/DefaultAccResDto'
        params:
          type: object
          additionalProperties:
            type: string
            description: Thông tin tham số cấu hình hệ thống
            example: >-
              {isAdmin=true, langCode=en, paramKey1=paramValue1,
              paramKey2=paramValue2}
          description: Thông tin tham số cấu hình hệ thống
          example: >-
            {isAdmin=true, langCode=en, paramKey1=paramValue1,
            paramKey2=paramValue2}
        quickLinks:
          type: array
          items:
            type: string
        userRole:
          type: string
          description: userRole
          example: Admin
        widgets:
          type: array
          items:
            type: string
      description: data body
    PermissionResDto:
      type: object
      properties:
        code:
          type: string
          description: code
          example: M_HOME
        type:
          type: string
          description: type
          example: MENU
        priority:
          type: string
          description: priority
        priorityLevel:
          type: string
          description: priorityLevel
        children:
          type: array
          description: children
          example: '[{code=M_SUBMENU, type=SUBMENU, children=[]}]'
          items:
            $ref: '#/components/schemas/PermissionResDto'
    UserResDto:
      type: object
      properties:
        userId:
          type: integer
          description: userId
          example: 240
        username:
          type: string
          description: username
          example: 297system1
        usid:
          type: string
          description: usid
          example: 297system1
        roleCode:
          type: string
          description: roleCode
          example: ADMIN
        fullname:
          type: string
          description: fullname
          example: System
        cusId:
          type: number
          description: cusId
          example: 601
        cifNo:
          type: string
          description: cifNo
          example: '704'
        cifName:
          type: string
          description: cifName
          example: DU TU VJYEP JOIQ
        langCode:
          type: string
          description: langCode
          example: vi-vn
        profileImage:
          type: string
          description: profileImage
          example: profile.png
        authMethod:
          type: string
          description: authMethod
          example: SMARTOTP
    CodeValueDto:
      type: object
      properties:
        code:
          type: string
          example: '001'
        value:
          type: string
          example: Active
      description: data body
    SecQnGetAllResponse:
      type: object
      properties:
        items:
          type: array
          description: List of code-value pairs
          items:
            $ref: '#/components/schemas/CodeValueDto'
        total:
          type: integer
          description: S? l??ng
          example: 10
    CreatePositiveChangePwRequest:
      type: "object"
      properties:
        currentPassword:
          type: "string"
          example: "oldPassword123"
        newPassword:
          type: "string"
          example: "newPassword123"
        confirmNewPassword:
          type: "string"
          example: "newPassword123"
    CreatePositiveChangePwResDto:
      type: object
      properties:
        authType:
          type: string
          description: "SMARTOTP|CA|SMSOTP|EMAILOTP"
          example: "SMARTOTP"
        transId:
          type: string
          description: "Transaction ID"
          example: "b088117585394b9db5919023c75b0742"
        basicOtp:
          $ref: "#/components/schemas/CreateOtpResDto"
        smartOtp:
          $ref: "#/components/schemas/CreateSmartOtpResDto"
      description: "data body"
    CreatePositiveChangePwResDtoBasicOtp:
      type: "object"
      properties:
        resendOtpTime:
          type: "string"
          description: "Time for resending OTP"
          example: "30"
        otpActiveCount:
          type: "string"
          description: "Active OTP count"
          example: "120"
        maskedPhoneOrEmail:
          type: "string"
          description: "Masked phone or email"
          example: "*********114"
        content:
          type: "string"
          description: "Content"
          example: "Vui lòng nhập mã OTP đã được gửi đến số điện thoại *********114 để xác thực kích hoạt tài khoản."
    WrongPwAttemptsResponse:
      required:
        - "code"
        - "message"
        - "status"
        - "traceId"
      type: "object"
      properties:
        status:
          type: "integer"
          description: "status code"
          format: "int32"
          example: "0"
        code:
          type: "string"
          description: "Error code detail"
          example: "0"
        message:
          type: "string"
          description: "Error description"
          example: "Success"
        errors:
          type: "array"
          description: "Addition list error"
          items:
            $ref: "#/components/schemas/ResponseError"
        traceId:
          type: "string"
          description: "Trace ID"
          example: "123456"
        data:
          type: "string"
          description: "data body"
    OtpSecureVerifyRequest:
      required:
        - "method"
        - "otpNum"
        - "transId"
      type: "object"
      properties:
        method:
          type: "string"
          example: "PW_CHANGE"
        transId:
          type: "string"
          example: "123456789"
        otpNum:
          type: "string"
          example: "123456"
    OtpSecuredResendRequest:
      required:
        - "method"
        - "transId"
      type: "object"
      properties:
        method:
          type: "string"
          example: "PW_CHANGE"
        transId:
          type: "string"
          example: "123456789"
    BiometricVerifyRequest:
      required:
        - "credentialId"
        - "deviceId"
        - "signature"
        - "transId"
        - "username"
      type: "object"
      properties:
        username:
          type: "string"
          description: "Username of the user"
          example: "100682system"
        deviceId:
          type: "string"
          description: "Device ID"
          example: "2864272bc9f10865"
        credentialId:
          type: "string"
          example: "Xc8VhBtEh-2vO0X7T9fJYw"
        transId:
          type: "string"
          example: "UL00102398120012999"
        signature:
          type: "string"
          example: "yNPglanpX4lG7rcUCE+/LBQgPKv42dkD22xXHkIHg7TnuN11wb8oa24/9xVFMKDovhwTN4DXMnkHzau/EYBQAw=="
    BiometricChallengeRequest:
      required:
        - "credentialId"
        - "deviceId"
        - "username"
      type: "object"
      properties:
        username:
          type: "string"
          description: "Username of the user"
          example: "100682system"
        deviceId:
          type: "string"
          description: "Device ID"
          example: "2864272bc9f10865"
        credentialId:
          type: "string"
          example: "Xc8VhBtEh-2vO0X7T9fJYw"
    BiometricChallengeResponseData:
      type: "object"
      properties:
        transId:
          type: "string"
          example: "UL00102398120012999"
        challengeCode:
          type: "string"
          example: "KbLAdHzEcnYAwblAM7_l-yJqQ-XslO40I_2CnDQ1fos"
      description: "data body"
    GetSmartOtpInfoResponseData:
      type: object
      properties:
        userRole:
          type: string
          description: Role of the user
          example: ADMIN
        smartOtp:
          $ref: '#/components/schemas/SmartOtpLoginResponseDto'
      description: data body
    BiometricOnRequest:
      required:
        - "credentialId"
        - "publicKey"
        - "type"
      type: "object"
      properties:
        credentialId:
          type: "string"
          example: "Xc8VhBtEh-2vO0X7T9fJYw"
        type:
          type: "string"
          example: "FACE_ID|TOUCH_ID"
        publicKey:
          type: "string"
          example: "MCowBQYDK2VwAyEAq4dgdBf0GivvPVs4B22hS1sHuSKbmQCz12F79u834rs="
    CompositeOtpResDto:
      type: "object"
      properties:
        authType:
          type: "string"
          description: "SMARTOTP|CA|SMSOTP|EMAILOTP"
          example: "SMARTOTP"
        transId:
          type: "string"
          description: "Transaction ID"
          example: "b088117585394b9db5919023c75b0742"
        basicOtp:
          $ref: "#/components/schemas/CompositeOtpResDtoBasicOtp"
        smartOtp:
          $ref: "#/components/schemas/CreateSmartOtpResDto"
      description: "data body"
    CompositeOtpResDtoBasicOtp:
      type: "object"
      properties:
        resendOtpTime:
          type: "string"
          description: "Time for resending OTP"
          example: "30"
        otpActiveCount:
          type: "string"
          description: "Active OTP count"
          example: "120"
        maskedPhoneOrEmail:
          type: "string"
          description: "Masked phone or email"
          example: "*********114"
        content:
          type: "string"
          description: "Content"
          example: "Vui lòng nhập mã OTP đã được gửi đến số điện thoại *********114 để xác thực kích hoạt tài khoản."
    CreateSmartOtpResDto:
      type: "object"
      properties:
        isSameDevice:
          type: "string"
          description: "Y|N"
          example: "Y"
        authId:
          type: "string"
          description: "auth Id"
          example: "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"
        qrCode:
          type: "string"
          description: "qr code"
          example: "67171768786494a79fcb37d8cb86741b67171768786494a79fcb37d8cb86741b"
        expTime:
          type: "string"
          description: "exp time seconds"
          example: "120"
        additionalInfo: {
          "$ref": "#/components/schemas/QrTransRes"
        }
    QrTransRes: {
      type: object,
      properties: {
        type: {
          type: string,
          description: SINGLE|MULTI,
          example: SINGLE
        },
        productCode: {
          type: string,
          description: product Code,
          example: CTT-TTCN
        },
        subProductCode: {
          type: string,
          description: sub product Code,
          example: CTT-TTCN-CTK
        },
        authId: {
          type: string,
          description: auth Id,
          example: f890a732-4c73-4d06-8567-61a098c6d787
        },
        userId: {
          type: string,
          description: userId,
          example: 123455
        },
        totalTrans: {
          type: integer,
          description: total trans,
          format: int32,
          example: 10
        },
        createdBy: {
          type: string,
          description: createdBy,
          example: createdBy
        },
        qrType: {
          type: string,
          description: type QR,
          example: BID_TRANS_OTP|VIETQR
        },
        productName: {
          type: string,
          description: productName,
          example: CTT-TTCN
        },
        subProductName: {
          type: string,
          description: subProductName,
          example: CTT-TTCN
        },
        trans: {
          type: array,
          items: {
            $ref: '#/components/schemas/QrTransGroupDto'
          }
        }
      }
    }
    QrTransGroupDto: {
      type: object,
      properties: {
        ccy: {
          type: string,
          description: CCY,
          example: VND
        },
        amount: {
          type: string,
          description: amount,
          example: 10000
        },
        amountText: {
          type: string,
          description: amountText,
          example: Mot nghin dong
        },
        total: {
          type: string,
          description: total,
          example: 10
        }
      }
    }
    UserInfoSettingsResponse:
      type: object
      properties:
        userInfo:
          $ref: '#/components/schemas/UserInfoSettingsResponseUserInfo'
        cusInfo:
          $ref: '#/components/schemas/UserInfoSettingsResponseCusInfo'
      description: data body
    UserInfoSettingsResponseCusInfo:
      type: object
      properties:
        cifNo:
          type: string
          example: '123456789'
        name:
          type: string
          example: Công ty TNHH Mặt trời đỏ
        idReg:
          type: string
          example: 0324567891
    UserInfoSettingsResponseUserInfo:
      type: object
      properties:
        name:
          type: string
          example: Nguyễn Văn Trung
        username:
          type: string
          example: 1235trungnv
        otpType:
          type: string
          example: Số điện thoại
        authType:
          type: string
          example: Chữ ký số
        role:
          type: string
          example: Duyệt lệnh
        wfRole:
          type: string
          example: 1
        limit:
          type: number
          example: 1000000000
    SecQnForgotPwRequest:
      required:
        - deviceId
        - securityQuestions
        - transId
        - username
      type: object
      properties:
        username:
          type: string
          example: 297vietqq
        deviceId:
          type: string
          example: 368de9117c7b4707a87d05d7ee487d6c
        transId:
          type: string
          example: UL092501201351584827
        securityQuestions:
          type: array
          items:
            $ref: '#/components/schemas/SecurityQuestionAnswerDto'
    SecQnForgotPwResponseData:
      type: object
      properties:
        userRole:
          type: string
          description: Role of the user
          example: ADMIN
        basicOtp:
          $ref: '#/components/schemas/CreateOtpResDto'
    CreateForgotPwRequest:
      required:
        - deviceId
        - idExpireDate
        - idNum
        - idReg
        - username
      type: object
      properties:
        username:
          type: string
          example: 297vietqq
        deviceId:
          type: string
          example: 368de9117c7b4707a87d05d7ee487d6c
        idNum:
          type: string
          example: 030091010005
        idExpireDate:
          type: string
          example: '2030-09-11'
        idReg:
          type: string
          example: 454RF/VDDC-MF
        mobile:
          type: string
          example: 0945224805
        otpEmail:
          type: string
          example: <EMAIL>
    CreateForgotPwResponseData:
      type: object
      properties:
        userRole:
          type: string
          description: Role of the user
          example: ADMIN
        basicOtp:
          $ref: '#/components/schemas/CreateOtpResDto'
        securityQuestions:
          type: array
          items:
            $ref: '#/components/schemas/CodeValueDto'
      description: data body
tags:
  - login
formated: 1
