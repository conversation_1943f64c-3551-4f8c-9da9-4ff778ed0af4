package vn.com.bidv.feature.login.data

import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.data.login.apis.OTPApi
import vn.com.bidv.feature.login.data.login.model.CreateOtpResDto
import vn.com.bidv.feature.login.data.login.model.OtpSecureVerifyRequest
import vn.com.bidv.feature.login.data.login.model.OtpSecuredResendRequest
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class VerifySecureOtpRepository @Inject constructor(
    private val service: OTPApi

) : BaseRepository() {

    suspend fun otpSecuredVerify(
        transId: String,
        otpNum: String,
        method: String
    ) = launch {
        service.otpSecuredVerify(
            OtpSecureVerifyRequest(
                method = method,
                transId = transId,
                otpNum = otpNum
            )
        )
    }

    suspend fun otpSecuredResend(
        transId: String,
        method: String,
    ): NetworkResult<CreateOtpResDto> = launch {
        service.otpSecuredResend(
            OtpSecuredResendRequest(
                method = method,
                transId = transId
            )
        )
    }

}