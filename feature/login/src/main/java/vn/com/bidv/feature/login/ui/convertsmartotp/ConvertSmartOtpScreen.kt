package vn.com.bidv.feature.login.ui.convertsmartotp

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.R.drawable
import vn.com.bidv.designsystem.component.datadisplay.tooltip.IBankTooltip
import vn.com.bidv.designsystem.component.datadisplay.tooltip.TooltipPosition
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessage
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessageStatus
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.feedback.otpview.IBankOtpItemInputList
import vn.com.bidv.designsystem.component.keyboard.KeyInput
import vn.com.bidv.designsystem.component.keyboard.NumpadKeyboard
import vn.com.bidv.designsystem.component.keyboard.NumpadType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.convertsmartotp.ConvertSmartOtpReducer.ConvertSmartOtpViewEffect
import vn.com.bidv.feature.login.ui.convertsmartotp.ConvertSmartOtpReducer.ConvertSmartOtpViewEvent
import vn.com.bidv.feature.login.ui.convertsmartotp.ConvertSmartOtpReducer.ConvertSmartOtpViewState
import vn.com.bidv.feature.login.ui.forgotpassword.textStyleToSpanStyle
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.feature.common.navigation.NavigationHelper as CommonNavigationHelper

@SuppressLint("StateFlowValueCalledInComposition")
@Composable
fun ConvertSmartOtpScreen(navController: NavHostController) {
    val vm: ConvertSmartOtpViewModel = hiltViewModel()

    var errorCommonMessage by remember { mutableStateOf("") }
    var errorLockedMessage by remember { mutableStateOf("") }
    var errorMessage by remember { mutableStateOf("") }
    BaseScreen(
        navController = navController,
        viewModel = vm,
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            isShowActionItem = false,
            titleTopAppBar = stringResource(R.string.chuyen_doi_smart_otp),
            showHomeIcon = false,
            isShowNavigationIcon = false
        ),
        renderContent = { uiState, onEvent ->
            if (!vm.uiState.value.isInit) {
                onEvent(ConvertSmartOtpViewEvent.OnInitEvent)
            } else {
                ConvertSmartOtpContent(
                    uiState = uiState,
                    onEvent = onEvent,
                    onBack = {
                        NavigationHelper.popConvertActiveSmartOtp(navController)
                    }
                )
            }

            BackHandler {
                NavigationHelper.popConvertActiveSmartOtp(navController)
            }

            if (errorCommonMessage.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = errorCommonMessage,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.close),
                        )
                    ),
                    onDismissRequest = {
                        errorCommonMessage = ""
                        NavigationHelper.popConvertActiveSmartOtp(navController)
                    }
                )
            }

            if (errorMessage.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = errorMessage,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.close),
                        )
                    ),
                    onDismissRequest = {
                        errorMessage = ""
                        NavigationHelper.popConvertActiveSmartOtp(navController)
                    }
                )
            }

            if (errorLockedMessage.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(R.string.loi),
                    supportingText = errorLockedMessage,
                    modalConfirmType = ModalConfirmType.Error,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.yes),
                            isDismissRequest = false,
                            onClick = {
                                errorLockedMessage = ""
                                CommonNavigationHelper.navigateToReActiveSmartOtpScreen(
                                    navController,
                                    true
                                )
                            }
                        ),
                        DialogButtonInfo(
                            label = stringResource(R.string.de_sau),
                        ),
                    ),
                    onDismissRequest = {
                        errorLockedMessage = ""
                        NavigationHelper.popConvertActiveSmartOtp(navController)
                    }
                )

            }
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is ConvertSmartOtpViewEffect.ConvertSuccessEffect -> {
                    navController.popBackStack(IBankMainRouting.AuthRoutes.ConvertActiveSmartOtpRoute.route, true)
                    CommonNavigationHelper.navigateToActiveSmartOtp(navController)
                }

                is ConvertSmartOtpViewEffect.ConvertFailCommonErrorEffect -> {
                    errorCommonMessage = sideEffect.errorMessage
                }

                is ConvertSmartOtpViewEffect.ConvertFailLockedErrorEffect -> {
                    errorLockedMessage = sideEffect.errorMessage
                }

                is ConvertSmartOtpViewEffect.ConvertFailDefaultErrorEffect -> {
                    errorMessage = sideEffect.errorMessage
                }

                else -> {
                    // Handle other side effects
                }
            }
        }
    )
}

@Composable
private fun ConvertSmartOtpContent(
    uiState: ConvertSmartOtpViewState,
    onEvent: (ConvertSmartOtpViewEvent) -> Unit,
    onBack: () -> Unit
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    val isInputFocus = remember { mutableStateOf(true) }
    val inputSize = 6
    val isEqualLength = uiState.inputPIN.length == inputSize
    val onKeyClickHandler = rememberUpdatedState { key: KeyInput ->
        when (key) {
            is KeyInput.Delete -> {
                if (isInputFocus.value) {
                    onEvent(ConvertSmartOtpViewEvent.DeleteEvent)
                }
            }

            is KeyInput.Number -> {
                if (isInputFocus.value && uiState.inputPIN.length < inputSize) {
                    onEvent(ConvertSmartOtpViewEvent.InputPINChangedEvent(key.value))
                }
            }

            else -> {
                // no action
            }
        }
    }
    val instruction = """
    ${stringResource(R.string.de_kich_hoat_chuyen_doi_smart_otp_tren_bidv_direct_quy_khach_vui_long_thuc_hien_cac_buoc_sau)}
      •  ${stringResource(R.string.mo_ung_dung_bidv_smart_otp)}
      •  ${stringResource(R.string.nhap_ma_pin)}.
      •  ${stringResource(R.string.lay_ma_otp_tuong_ung_voi_ten_dang_nhap_ibank_cua_quy_khach_va_nhap_vao_o_ben_duoi)}
""".trimIndent()

    val tooltipText = if (uiState.isAdminRole) {
        buildAnnotatedString {
            append(stringResource(R.string.truong_hop_quen_ma_pin_truy_cap_bidv_smart_otp_vui_long_toi_chi_nhanh_bidv_de_duoc_ho_tro))
        }
    } else {
        buildAnnotatedString {
            withStyle(
                style = textStyleToSpanStyle(
                    typography.titleTitle_s,
                    colorScheme.contentOn_specialPrimary
                )
            ) {
                append("${stringResource(R.string.truong_hop_quen_ma_pin_truy_cap_bidv_smart_otp_vui_long_thuc_hien_theo_mot_trong_hai_cach_sau)}\n")
            }
            withStyle(
                style = textStyleToSpanStyle(
                    typography.bodyBody_m,
                    colorScheme.contentOn_specialSecondary
                )
            ) {
                append("  •  ${stringResource(R.string.cach_1_toi_chi_nhanh_bidv_de_duoc_ho_tro)}\n")
            }
            withStyle(
                style = textStyleToSpanStyle(
                    typography.bodyBody_m,
                    colorScheme.contentOn_specialSecondary
                )
            ) {
                append("  •  ${stringResource(R.string.cach_2_su_dung_tinh_nang_kich_hoat_lai_smart_otp_tren_bidv_direct_chon_de_sau_trang_chu_cai_dat_cai_dat_thong_tin_bao_mat_kich_hoat_lai_smart_otp)}")
            }
        }
    }

    Box {
        if (!isInputFocus.value) {
            IBankActionBar(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .zIndex(1f),
                isVertical = false,
                buttonNegative = DialogButtonInfo(
                    label = stringResource(R.string.de_sau)
                ) {
                    onBack()
                },
                buttonPositive = DialogButtonInfo(
                    label = stringResource(R.string.xac_nhan)
                ) {
                    if (isEqualLength) {
                        onEvent(
                            ConvertSmartOtpViewEvent.SubmitEvent(uiState.inputPIN)
                        )
                    }
                }
            )
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .align(Alignment.BottomCenter),
        ) {
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = IBSpacing.spacingM)
                    .clickable(indication = null, interactionSource = remember {
                        MutableInteractionSource()
                    }) {
                        isInputFocus.value = false
                    }
                    .verticalScroll(rememberScrollState())
            ) {
                Spacer(modifier = Modifier.height(IBSpacing.spacingS))
                InlineMessage(
                    status = InlineMessageStatus.Brand(LocalColorScheme.current),
                    message = instruction,
                )
                Spacer(modifier = Modifier.height(IBSpacing.spacingM))
                InputPINView(
                    inputSize = inputSize,
                    title = stringResource(R.string.nhap_ma_otp_tren_ung_dung_bidv_smart_otp),
                    isFocus = isInputFocus.value,
                    text = uiState.inputPIN,
                    tooltipText = tooltipText,
                    onOtpFocus = { isFocus ->
                        isInputFocus.value = isFocus
                    },
                    onOtpInputDone = {
                        isInputFocus.value = false
                    }
                )

                if (!isInputFocus.value && uiState.inputPIN.length in 1..<inputSize) {
                    Spacer(modifier = Modifier.height(IBSpacing.spacingS))
                    Text(
                        stringResource(R.string.ma_smart_otp_gom_6_ky_tu),
                        style = typography.labelLabel_m,
                        color = colorScheme.contentNegativeSecondary,
                        textAlign = TextAlign.Start,
                        modifier = Modifier.align(Alignment.Start)
                    )
                }

                Spacer(modifier = Modifier.weight(1f))
            }

            AnimatedVisibility(
                visible = isInputFocus.value,
                enter = slideInVertically(
                    initialOffsetY = { fullHeight -> fullHeight }
                ) + fadeIn(),
                exit = slideOutVertically(
                    targetOffsetY = { fullHeight -> fullHeight }
                ) + fadeOut()
            ) {
                NumpadKeyboard(
                    type = NumpadType.SHUFFLED,
                    onKeyClick = { key -> onKeyClickHandler.value(key) }
                )
            }

        }
    }
}

@Composable
private fun InputPINView(
    inputSize: Int,
    title: String,
    isFocus: Boolean = false,
    text: String,
    tooltipText: AnnotatedString,
    onOtpFocus: (Boolean) -> Unit = {},
    onOtpInputDone: (String) -> Unit = {},
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current



    Column {
        Row(
            modifier = Modifier.wrapContentSize(),
            horizontalArrangement = Arrangement.Start,
            verticalAlignment = Alignment.Bottom
        ) {
            Text(
                title,
                style = typography.titleTitle_s,
                color = colorScheme.contentMainPrimary,
            )

            Spacer(modifier = Modifier.width(IBSpacing.spacingXs))
            IBankTooltip(
                isSupportingText = true,
                subContent = {
                    Text(
                        text = tooltipText,
                        lineHeight = typography.bodyBody_m.lineHeight,
                        color = colorScheme.contentOn_specialSecondary,
                        style = typography.bodyBody_m,
                    )

                },
                showCloseButton = false,
                tooltipPosition = TooltipPosition.BELOW,
                showIconView = { modifier ->
                    Icon(
                        painter = painterResource(id = drawable.information_circle_outline),
                        contentDescription = "Tooltip Icon",
                        tint = colorScheme.contentMainTertiary,
                        modifier = modifier
                            .size(20.dp)
                    )
                }
            )
        }

        Spacer(modifier = Modifier.height(IBSpacing.spacingM))

        IBankOtpItemInputList(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(indication = null, interactionSource = remember {
                    MutableInteractionSource()
                }) {
                    onOtpFocus(true)
                },
            otpItemNumber = inputSize,
            otpText = text,
            isFocus = isFocus,
            onOtpInputDone = {
                onOtpInputDone(text)
            }

        )
    }
}
