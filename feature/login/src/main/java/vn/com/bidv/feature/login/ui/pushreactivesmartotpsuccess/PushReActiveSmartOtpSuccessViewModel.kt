package vn.com.bidv.feature.login.ui.pushreactivesmartotpsuccess

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.common.patterns.mvi.BaseMviViewModel
import vn.com.bidv.feature.login.ui.pushreactivesmartotpsuccess.PushReActiveSmartOtpSuccessReducer.PushReActiveSmartOtpSuccessViewEffect
import vn.com.bidv.feature.login.ui.pushreactivesmartotpsuccess.PushReActiveSmartOtpSuccessReducer.PushReActiveSmartOtpSuccessViewEvent
import vn.com.bidv.feature.login.ui.pushreactivesmartotpsuccess.PushReActiveSmartOtpSuccessReducer.PushReActiveSmartOtpSuccessViewState
import javax.inject.Inject

@HiltViewModel
class PushReActiveSmartOtpSuccessViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
) : BaseMviViewModel<PushReActiveSmartOtpSuccessViewState, PushReActiveSmartOtpSuccessViewEvent, PushReActiveSmartOtpSuccessViewEffect>(
    initialState = PushReActiveSmartOtpSuccessViewState(),
    reducer = PushReActiveSmartOtpSuccessReducer()
) {
    override fun handleEffect(
        sideEffect: PushReActiveSmartOtpSuccessViewEffect,
        onResult: (PushReActiveSmartOtpSuccessViewEvent) -> Unit
    ) {
        // TODO: Controller logic, do not handle logic code here.
        /*
		 when (sideEffect) {
		    // Continue event
            is PushReActiveSmartOtpSuccessViewEffect.FetchListData -> {
                // Controller logic via Use Case
                fetchData(sideEffect.searchText) { nextEvent ->
                    onResult(nextEvent)
                }
            }

            else -> {}
        }
		*/
    }
}
