package vn.com.bidv.feature.login.ui.pushreactivesmartotpsuccess.model

import kotlinx.serialization.Serializable

@Serializable
sealed class ModelPushReActiveSmartOtpSuccessUI {

    @Serializable
    data class ActiveByPhone(
        val transId: String = "",
        val phoneNumber: String = ""
    ) : ModelPushReActiveSmartOtpSuccessUI()

    @Serializable
    data class ActiveByEmail(
        val time: String = "",
    ) : ModelPushReActiveSmartOtpSuccessUI()
}
