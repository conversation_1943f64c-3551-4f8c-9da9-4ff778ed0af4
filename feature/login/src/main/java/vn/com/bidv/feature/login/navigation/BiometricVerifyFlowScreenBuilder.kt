package vn.com.bidv.feature.login.navigation

import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.login.domain.BiometricVerifyUseCase
import javax.inject.Inject

class BiometricVerifyFlowScreenBuilder @Inject constructor(
    useCase: BiometricVerifyUseCase,
): VerifyTransactionFlowScreenBuilder(
    useCase = useCase,
    type = VerifyTransactionTypeConstant.TurnOnBiometric,
    isShareDataWhenFail = true
)