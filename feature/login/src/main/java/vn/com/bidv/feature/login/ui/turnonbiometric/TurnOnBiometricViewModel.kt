package vn.com.bidv.feature.login.ui.turnonbiometric

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.feature.common.constants.Constants as CommonConstants
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.common.domain.data.UserResDMO
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.BiometricUseCase
import vn.com.bidv.feature.login.domain.model.CompositeOtpResDMO
import vn.com.bidv.feature.login.ui.turnonbiometric.TurnOnBiometricReducer.TurnOnBiometricViewEffect
import vn.com.bidv.feature.login.ui.turnonbiometric.TurnOnBiometricReducer.TurnOnBiometricViewEvent
import vn.com.bidv.feature.login.ui.turnonbiometric.TurnOnBiometricReducer.TurnOnBiometricViewState
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class TurnOnBiometricViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val biometricUseCase: BiometricUseCase,
    private val userInfoUseCase: UserInfoUseCase,
) : ViewModelIBankBase<TurnOnBiometricViewState, TurnOnBiometricViewEvent, TurnOnBiometricViewEffect>(
    initialState = TurnOnBiometricViewState(),
    reducer = TurnOnBiometricReducer()
) {
    override fun handleEffect(
        sideEffect: TurnOnBiometricViewEffect,
        onResult: (TurnOnBiometricViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is TurnOnBiometricViewEffect.InitTurnOnBiometric -> {
                onResult(
                    TurnOnBiometricViewEvent.OnInitTurnOnBiometricSuccess(
                        userInfoUseCase.getUserInfoFromStorage().getSafeData()?.user ?: UserResDMO()
                    )
                )
                onResult(TurnOnBiometricViewEvent.OnCheckBiometricAvailable)
            }

            is TurnOnBiometricViewEffect.CheckBiometricAvailable -> {
                callDomain(
                    showLoadingIndicator = false,
                    isListenAllError = true,
                    onSuccess = {
                        onResult(TurnOnBiometricViewEvent.OnGetAuthenticationResult)
                    },
                    onFail = {
                        onResult(TurnOnBiometricViewEvent.OnBiometricNotAvailable(it?.errorCode))
                    }
                ) {
                   biometricUseCase.checkBiometricAvailable()
                }
            }

            is TurnOnBiometricViewEffect.StartTurnOnBiometricWithVerifySMS -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = {
                        onResult(TurnOnBiometricViewEvent.OnVerifyOtpBiometric(it.data ?: CompositeOtpResDMO()))
                    },
                    onFail = {
                        onResult(TurnOnBiometricViewEvent.OnStartTurnOnBiometricError(it?.errorMessage))
                    }
                ) {
                    biometricUseCase.turnOnBiometric()
                }
            }

            is TurnOnBiometricViewEffect.VerifyOtpBiometricSuccess -> {
                callDomain(
                    showLoadingIndicator = false,
                    onSuccess = {
                        viewModelScope.launch(dispatcher) {
                            localRepository.showSnackBar(
                                IBankSnackBarInfo(
                                    message = resourceProvider.getString(R.string.kich_hoat_dang_nhap_bang_touch_id_thanh_cong),
                                    primaryButtonText = resourceProvider.getString(R.string.dong)
                                )
                            )
                        }
                        localRepository.unsubscribeShareData(Constants.VerifyBiometricOtp.VERIFY_BIOMETRIC_SMS_OTP)
                        shareBiometricAction {
                            biometricUseCase.shareBiometricAction(CommonConstants.Biometric.BIOMETRIC_TURNON_SUCCESS)
                        }
                        onResult(TurnOnBiometricViewEvent.OnTurnOnBiometricSuccess)
                    }
                ) {
                    biometricUseCase.verifyOtpBiometricSuccess(sideEffect.authenticationResult)
                }
            }

            is TurnOnBiometricViewEffect.TurnOffBiometric -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = {
                        viewModelScope.launch(dispatcher) {
                            localRepository.showSnackBar(
                                IBankSnackBarInfo(
                                    message = resourceProvider.getString(R.string.huy_dang_nhap_bang_touch_id_thanh_cong),
                                    primaryButtonText = resourceProvider.getString(R.string.dong)
                                )
                            )
                        }
                        shareBiometricAction {
                            biometricUseCase.shareBiometricAction(CommonConstants.Biometric.BIOMETRIC_TURNOFF_SUCCESS)
                        }
                        onResult(TurnOnBiometricViewEvent.OnTurnOffBiometricSuccess)
                    },
                    onFail = {
                        onResult(TurnOnBiometricViewEvent.OnTurnOffBiometricError(it?.errorMessage))
                    }
                ) {
                    biometricUseCase.turnOffBiometric()
                }
            }

            is TurnOnBiometricViewEffect.CreateBiometricOnRequest -> {
                val result = biometricUseCase.createBiometricOnRequest()
                onResult(TurnOnBiometricViewEvent.OnCreateBiometricOnRequestSuccess(result))
            }

            else -> {
                // nothing
            }
        }
    }

    private fun shareBiometricAction (onShare: suspend () -> Unit) {
        viewModelScope.launch(Dispatchers.Default) {
            onShare()
        }
    }
}
