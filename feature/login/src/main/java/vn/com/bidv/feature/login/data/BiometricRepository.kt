package vn.com.bidv.feature.login.data

import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.data.login.apis.BiometricApi
import vn.com.bidv.feature.login.data.login.apis.OTPApi
import vn.com.bidv.feature.login.data.login.model.BiometricOnRequest
import vn.com.bidv.feature.login.data.login.model.OtpSecureVerifyRequest
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class BiometricRepository @Inject constructor(
    private val biometricApi: BiometricApi,
    private val otpApi: OTPApi
) : BaseRepository() {
    suspend fun turnOnBiometric(credentialId: String, type: String, publicKey: String) = launch {
        biometricApi.biometricOn(
            BiometricOnRequest(
                credentialId = credentialId,
                type = type,
                publicKey = publicKey
            )
        )
    }

    suspend fun turnOffBiometric() = launch {
        biometricApi.biometricOff()
    }

    suspend fun verifySmartOtpBiometric(transId: String, otp: String) = launch {
        otpApi.otpSecuredVerify(
            OtpSecureVerifyRequest(
                transId = transId,
                otpNum = otp,
                method = Constants.Biometric.SETTING_BIOMETRIC
            )
        )
    }
}