package vn.com.bidv.feature.login.ui.setupsmartotp

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.common.domain.data.UserActiveSmartOtpDMO
import vn.com.bidv.feature.common.domain.smartotp.SmartOTPUseCase
import vn.com.bidv.feature.login.domain.ActiveSmartOTPUseCase
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEffect
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewEvent
import vn.com.bidv.feature.login.ui.setupsmartotp.SetupSmartOtpReducer.SetupSmartOtpViewState
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class SetupSmartOtpViewModel @Inject constructor(
    private val activeSmartOTPUseCase: ActiveSmartOTPUseCase,
    private val smartOTPUseCase: SmartOTPUseCase,
) : ViewModelIBankBase<SetupSmartOtpViewState, SetupSmartOtpViewEvent, SetupSmartOtpViewEffect>(
    initialState = SetupSmartOtpViewState(),
    reducer = SetupSmartOtpReducer()
) {

    override fun handleEffect(
        sideEffect: SetupSmartOtpViewEffect,
        onResult: (SetupSmartOtpViewEvent) -> Unit
    ) {
        BLogUtil.d("handleSideEffect: $sideEffect")
        when (sideEffect) {
            is SetupSmartOtpViewEffect.RequestActiveSmartOTPEffect -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = { result ->
                        result.data?.let { nonNullData ->
                            onResult(SetupSmartOtpViewEvent.RequestActiveSmartOtpSuccessEvent(data = nonNullData))
                        }
                    },
                    onFail = { error ->
                        onResult(
                            SetupSmartOtpViewEvent.RequestActiveSmartOtpFail(
                                errorCode = error?.errorCode ?: "",
                                errorMessage = error?.errorMessage ?: "",
                            )
                        )
                    }
                ) {
                    activeSmartOTPUseCase.invoke()
                }
            }

            is SetupSmartOtpViewEffect.DeleteSmartOtpEffect -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = {
                        smartOTPUseCase.deleteAllUserSmartOtp()
                        onResult(SetupSmartOtpViewEvent.DeleteSmartOtpSuccessEvent)
                    },
                    onFail = { error ->
                        onResult(
                            SetupSmartOtpViewEvent.RequestActiveSmartOtpFail(
                                errorCode = error?.errorCode ?: "",
                                errorMessage = error?.errorMessage ?: "",
                            )
                        )
                    }
                ) {
                    activeSmartOTPUseCase.deleteAllSmartOtpInDevice()
                }
            }

            is SetupSmartOtpViewEffect.RequestChangePinEffect -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = { result ->
                        result.data?.let { nonNullData ->
                            onResult(SetupSmartOtpViewEvent.RequestChangePinSuccessEvent(data = nonNullData))
                        }
                    },
                    onFail = { error ->
                        onResult(
                            SetupSmartOtpViewEvent.RequestActiveSmartOtpFail(
                                errorCode = error?.errorCode ?: "",
                                errorMessage = error?.errorMessage ?: "",
                            )
                        )
                    }
                ) {
                    activeSmartOTPUseCase.changePin()
                }
            }

            is SetupSmartOtpViewEffect.UpdateUserActiveEffect -> {
                saveUserActiveOtp(sideEffect.userActiveSmartOtpDMO)
                onResult(SetupSmartOtpViewEvent.UpdateUserActiveSuccessEvent)
            }

            is SetupSmartOtpViewEffect.GetSmartOtpInfoEffect -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = {
                        onResult(SetupSmartOtpViewEvent.OnGetSmartOtpInfoSuccess(it.data))
                    },
                    onFail = {
                        onResult(SetupSmartOtpViewEvent.OnGetSmartOtpInfoError(it?.errorCode, it?.errorMessage))
                    }
                ) {
                    activeSmartOTPUseCase.getSmartOtpInfo(isGetSmartOtpInfo = sideEffect.isGetSmartOtpInfo)
                }
            }

            else -> {
                //nothing
            }
        }

    }

    fun saveUserActiveOtp(userActiveSmartOtpDMO: UserActiveSmartOtpDMO) {
        smartOTPUseCase.saveUserActiveSmartOtpDMO(userActiveSmartOtpDMO).also {
            localRepository.updateNeedToSyncTime(true)
        }
    }

}
