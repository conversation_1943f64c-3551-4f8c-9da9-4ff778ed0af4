package vn.com.bidv.feature.login.ui.manageusersmartotp

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.UserSmartOtpDMO

class ManageUserSmartOtpReducer :
    Reducer<ManageUserSmartOtpReducer.ManageUserSmartOtpViewState, ManageUserSmartOtpReducer.ManageUserSmartOtpViewEvent, ManageUserSmartOtpReducer.ManageUserSmartOtpViewEffect> {

    @Immutable
    data class ManageUserSmartOtpViewState(
        val isInitSuccess: Boolean = false,
        val listUserSmartOtpDMO: List<UserSmartOtpDMO> = emptyList(),
    ) : ViewState

    @Immutable
    sealed class ManageUserSmartOtpViewEvent : ViewEvent {
        data object OnInitScreen : ManageUserSmartOtpViewEvent()
        data class OnGetListUserSmartOtpSuccess(val listUserSmartOtpDMO: List<UserSmartOtpDMO>) :
            ManageUserSmartOtpViewEvent()

        data class OnDeleteUserSmartOtp(val model: UserSmartOtpDMO) : ManageUserSmartOtpViewEvent()
        data class OnDeleteUserSmartOtpSuccess(val model: UserSmartOtpDMO) :
            ManageUserSmartOtpViewEvent()
    }

    @Immutable
    sealed class ManageUserSmartOtpViewEffect : SideEffect {
        data object GetListUserSmartOtp : ManageUserSmartOtpViewEffect()
        data class DeleteUserSmartOtp(val model: UserSmartOtpDMO) : ManageUserSmartOtpViewEffect()
    }

    override fun reduce(
        previousState: ManageUserSmartOtpViewState,
        event: ManageUserSmartOtpViewEvent,
    ): Pair<ManageUserSmartOtpViewState, ManageUserSmartOtpViewEffect?> {
        return handleManageUserSmartOtp(previousState, event)
    }

    private fun handleManageUserSmartOtp(
        previousState: ManageUserSmartOtpViewState,
        event: ManageUserSmartOtpViewEvent
    ): Pair<ManageUserSmartOtpViewState, ManageUserSmartOtpViewEffect?> {
        return when (event) {
            is ManageUserSmartOtpViewEvent.OnInitScreen -> {
                previousState.copy(isInitSuccess = true) to ManageUserSmartOtpViewEffect.GetListUserSmartOtp
            }

            is ManageUserSmartOtpViewEvent.OnGetListUserSmartOtpSuccess -> {
                previousState.copy(listUserSmartOtpDMO = event.listUserSmartOtpDMO) to null
            }

            is ManageUserSmartOtpViewEvent.OnDeleteUserSmartOtp -> {
                previousState to ManageUserSmartOtpViewEffect.DeleteUserSmartOtp(event.model)
            }

            is ManageUserSmartOtpViewEvent.OnDeleteUserSmartOtpSuccess -> {
                previousState.copy(listUserSmartOtpDMO = previousState.listUserSmartOtpDMO.filter {
                    it.userId != event.model.userId
                }) to null
            }
        }
    }
}
