package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName

data class SmartOtpRqActiveRequestDMO(
    /* Unique identifier of the device */
    @SerializedName("deviceId")
    val deviceId: kotlin.String,

    /* OS */
    @SerializedName("os")
    val os: kotlin.String? = null,

    /* OS Version */
    @SerializedName("osVersion")
    val osVersion: kotlin.String? = null,

    /* Device Model */
    @SerializedName("deviceModel")
    val deviceModel: kotlin.String? = null
)