package vn.com.bidv.feature.login.ui.verifyquestion

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.designsystem.component.dataentry.AllowSpecificCharactersFilter
import vn.com.bidv.designsystem.component.dataentry.IBFrameExtendState
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankDropDown
import vn.com.bidv.designsystem.component.dataentry.IBankInputPassword
import vn.com.bidv.designsystem.component.dataentry.RemoveDoubleSpaceFilter
import vn.com.bidv.designsystem.component.feedback.bottomsheet.IBankBottomSheet
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessage
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessageStatus
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBCornerRadius
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.model.CodeValueDMO
import vn.com.bidv.feature.login.domain.model.DetailError
import vn.com.bidv.feature.login.domain.model.ModelForgotPwOTPConfigDMO
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.model.QaModel
import vn.com.bidv.feature.login.ui.verifyquestion.VerifySecureQuestionsReducer.VerifySecureQuestionsViewEffect
import vn.com.bidv.feature.login.ui.verifyquestion.VerifySecureQuestionsReducer.VerifySecureQuestionsViewEvent
import vn.com.bidv.feature.login.ui.verifyquestion.VerifySecureQuestionsReducer.VerifySecureQuestionsViewState
import vn.com.bidv.feature.login.ui.verifyquestion.model.VerifyQuestionModel
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.utils.VietnameseAccentRemoverFilter
import java.util.Locale
import vn.com.bidv.sdkbase.navigation.NavigationHelper as SdkBaseNavigationHelper

@Composable
fun VerifySecureQuestionsScreen(
    navController: NavHostController, verifyQuestionModel: VerifyQuestionModel?
) {
    val vm: VerifySecureQuestionsViewModel = hiltViewModel()

    val isBottomSheetVisible = remember { mutableStateOf(false) }
    val selectedIndex: MutableState<Int> = remember { mutableIntStateOf(-1) }

    BaseScreen(navController = navController, viewModel = vm, topAppBarConfig = TopAppBarConfig(
        isShowNavigationIcon = true,
        titleTopAppBar = stringResource(R.string.quen_mat_khau),
        showHomeIcon = false,
    ), renderContent = { uiState, onEvent ->
        LaunchedEffect(true) {
            onEvent(
                VerifySecureQuestionsViewEvent.InitEvent(
                    userName = verifyQuestionModel?.userName ?: "",
                    transId = verifyQuestionModel?.transId ?: "",
                    initData = verifyQuestionModel?.listQuestions ?: emptyList()
                )
            )
        }
        VerifySecureQuestionsContent(
            navController,
            uiState,
            onEvent,
            onAnswerChange = { index ->
                isBottomSheetVisible.value = true
                selectedIndex.value = index
            },
        )

        if (isBottomSheetVisible.value) {
            ShowBottomSheet(
                selectedItem = uiState.data[selectedIndex.value],
                listQuestion = uiState.dataListQuestionCanSelect(selectedIndex.value),
                onDismiss = {
                    isBottomSheetVisible.value = false
                    if (it != null) {
                        onEvent(
                            VerifySecureQuestionsViewEvent.QuestionChangeEvent(
                                selectedIndex.value, it
                            )
                        )
                    }
                })
        }

    }, handleSideEffect = { sideEffect ->

        if (sideEffect is VerifySecureQuestionsViewEffect.SubmitDataSuccessEffect) {
            val data = sideEffect.secQnForgotPwResponseDMO
            NavigationHelper.navigateToForgotPWSmsOTP(
                navController, modelForgotPwOTPConfigDMO = ModelForgotPwOTPConfigDMO(
                    userName = verifyQuestionModel?.userName,
                    userRole = data?.userRole,
                    modelCreateOtpResDMO = sideEffect.secQnForgotPwResponseDMO?.basicOtp
                )
            )
        }

    }

    )
}

@Composable
private fun VerifySecureQuestionsContent(
    navController: NavHostController,
    uiState: VerifySecureQuestionsViewState,
    onEvent: (VerifySecureQuestionsViewEvent) -> Unit,
    onAnswerChange: (Int) -> Unit = {},
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.Start
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.height(IBSpacing.spacingS))
            InlineMessage(
                modifier = Modifier.padding(horizontal = IBSpacing.spacingM),
                status = InlineMessageStatus.Brand(LocalColorScheme.current),
                message = stringResource(R.string.quy_khach_vui_long_chon_va_tra_loi_23_cau_hoi_bao_mat_da_khai_bao_voi_ngan_hang_de_xac_thuc),
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacingL))
            repeat(uiState.data.size) { index ->
                ItemQuestion(title = "${stringResource(R.string.cau_hoi)} ${index + 1}",
                    qaModel = uiState.data[index],
                    questionViewId = Constants.ViewID.QUESTION_VIEW_ID + index,
                    answerViewId = Constants.ViewID.ANSWER_VIEW_ID + index,
                    onChoiceRequest = {
                        onAnswerChange(index)
                    },
                    onAnswerChange = { newValue ->
                        onEvent(
                            VerifySecureQuestionsViewEvent.AnswerChangeEvent(
                                index, newValue
                            )
                        )
                    })
                Spacer(modifier = Modifier.height(IBSpacing.spacingL))
            }
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(color = LocalColorScheme.current.bgMainTertiary),
        ) {

            IBankActionBar(modifier = Modifier,
                isVertical = false,
                buttonNegative = DialogButtonInfo(stringResource(R.string.thoat), onClick = {
                    SdkBaseNavigationHelper.navigationToLogin(navController)
                }),
                buttonPositive = DialogButtonInfo(stringResource(R.string.tiep_tuc), onClick = {
                    onEvent(VerifySecureQuestionsViewEvent.SubmitDataEvent)
                }

                ))

        }
    }
}

@Composable
private fun getErrorDetails(
    listClientErrorCode: List<Pair<String, String>>?,
    viewIdError: String
): DetailError {
    val hasError = listClientErrorCode?.any { it.first == viewIdError } == true
    val state =
        if (hasError) IBFrameState.ERROR(LocalColorScheme.current) else IBFrameExtendState.FILLED(
            LocalColorScheme.current
        )
    val message = if (hasError) stringResource(R.string.vui_long_nhap_thong_tin) else ""
    return DetailError(state, message)
}

@Composable
private fun ItemQuestion(
    title: String,
    qaModel: QaModel,
    questionViewId: String,
    answerViewId: String,
    onChoiceRequest: () -> Unit,
    onAnswerChange: (String) -> Unit,
) {
    val colorScheme = LocalColorScheme.current

    val questionError = getErrorDetails(qaModel.listErrorCode, questionViewId)
    val answerError = getErrorDetails(qaModel.listErrorCode, answerViewId)


    Column(
        modifier = Modifier
            .wrapContentHeight()
            .padding(horizontal = IBSpacing.spacingM),
        horizontalAlignment = Alignment.Start
    ) {
        Text(text = title, style = LocalTypography.current.titleTitle_s)
        Spacer(modifier = Modifier.height(IBSpacing.spacingXs))
        IBankDropDown(state = if (qaModel.editAble) {
            if (qaModel.question.value.isNullOrEmpty() && qaModel.listErrorCode.isNullOrEmpty()) IBFrameState.DEFAULT(
                colorScheme
            )
            else questionError.state
        } else IBFrameState.DISABLE(
            colorScheme
        ),
            hintTextStart = questionError.message,
            labelText = if (qaModel.question.value.isNotNullOrEmpty()) "${qaModel.question.value}" else stringResource(
                R.string.lua_chon_cau_hoi
            ),
            modifier = Modifier.align(Alignment.Start),
            onClickEnd = {
                if (qaModel.editAble) {
                    onChoiceRequest()
                }
            })

        Spacer(modifier = Modifier.height(IBSpacing.spacingS))
        IBankInputPassword(
            state = if (qaModel.editAble) answerError.state else IBFrameState.DISABLE(
                colorScheme
            ),
            helpTextLeft = answerError.message,
            text = (qaModel.answerTextFieldValue?.uppercase(Locale.getDefault()) ?: ""),
            placeholderText = stringResource(R.string.nhap_cau_tra_loi),
            maxLengthText = 30,
            filters = listOf(
                VietnameseAccentRemoverFilter(),
                RemoveDoubleSpaceFilter(),
                AllowSpecificCharactersFilter()
            ),
            onClickClear = {
                onAnswerChange("")
            },
            onValueChange = { newValue ->
                onAnswerChange(newValue.text.uppercase(Locale.getDefault()).trim())
            },
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ShowBottomSheet(
    selectedItem: QaModel?,
    listQuestion: List<CodeValueDMO>,
    onDismiss: ((itemSelected: CodeValueDMO?) -> Unit) = {},
) {

    val selectedValue = listQuestion.indexOfFirst { it.code == selectedItem?.question?.code }
    IBankBottomSheet(title = stringResource(R.string.chon_cau_hoi), onDismiss = {
        onDismiss(null)
    },

        bottomSheetContent = {
            if (listQuestion.isEmpty()) IBankEmptyState(
                modifier = Modifier.fillMaxWidth(1 / 3f),
                supportingText = stringResource(id = R.string.khong_co_du_lieu),
                textButton = stringResource(id = R.string.dong),
            )
            else LazyColumn {
                items(listQuestion.size) { index ->
                    val item = listQuestion[index]

                    val value = item.value
                    if (value != null) {
                        val baseModifier = Modifier
                            .clickable {
                                onDismiss(item)
                            }
                            .padding(start = IBSpacing.spacingM, end = IBSpacing.spacingM)

                        Box(
                            modifier = if (index == selectedValue) {
                                baseModifier
                                    .fillMaxWidth()
                                    .background(
                                        color = LocalColorScheme.current.bgBrand_01Tertiary,
                                        shape = RoundedCornerShape(IBCornerRadius.cornerRadiusL)
                                    )
                                    .wrapContentHeight()
                            } else {
                                baseModifier
                            }
                        ) {
                            Row(
                                horizontalArrangement = Arrangement.SpaceBetween,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(end = IBSpacing.spacingXs)
                            ) {
                                Text(
                                    text = value,
                                    modifier = Modifier
                                        .testTagIBank("login_text_question")
                                        .wrapContentWidth()
                                        .padding(IBSpacing.spacingM)
                                )

                                if (index == selectedValue) {
                                    Icon(
                                        imageVector = ImageVector.vectorResource(id = vn.com.bidv.designsystem.R.drawable.check),
                                        contentDescription = null,
                                        tint = LocalColorScheme.current.contentBrand_01Primary,
                                        modifier = Modifier
                                            .size(24.dp)
                                            .align(Alignment.CenterVertically)
                                    )
                                }
                            }
                        }
                    }
                }
            }

        })

}
