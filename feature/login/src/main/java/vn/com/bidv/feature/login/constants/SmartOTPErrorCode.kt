package vn.com.bidv.feature.login.constants

object SmartOTPErrorCode {
    const val TIMEOUT_FO_01  = "TIMEOUT_FO_01"
    const val ERROR_DURING_PROCESSING  = "UL0001"
    const val INPUT_INVALID  = "UL0002"
    const val USER_NOT_REGISTER_YET  = "UL0102"
    const val DEVICE_THAT_IS_ACTIVATING_THE_USER_SMART_OTP_HAS_A_DIFFERENT_ID  = "UL0103"
    const val SMART_OTP_STATUS_NOT_VALID  = "UL0104"
    const val SMART_OTP_HAS_BEEN_LOCKED  = "UL0105"
    const val OTP_VERIFICATION_IS_TEMPORARILY_LOCKED = "UL0017"
    const val INVALID_TRANSACTION_STATUS = "UL0003"

    // Chuyển đổi Smart OTP 1.5
    const val VERIFICATION_FAILED = "UL1504" // Không thể xác thực Smart OTP 1.5. Quý khách xin vui lòng thử lại.
    const val STATUS_INVALID_FOR_VERIFICATION = "UL1501" // Trạng thái Smart OTP 1.5 không hợp lệ để xác thực.
    const val SMART_OTP_LOCKED_TOO_MANY_ATTEMPTS = "UL1509" // Smart OTP đã bị khoá do nhập sai mã Smart OTP 1.5 quá số lần cho phép. Quý khách vui lòng đến chi nhánh BIDV để được hỗ trợ.
    const val SMART_OTP_REACTIVATE_OR_VISIT_BRANCH = "UL1510" // Smart OTP đã bị khoá do nhập sai mã Smart OTP 1.5 quá số lần cho phép. Vui lòng kích hoạt lại hoặc đến chi nhánh BIDV để được hỗ trợ.
}
