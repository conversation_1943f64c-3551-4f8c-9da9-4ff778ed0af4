package vn.com.bidv.feature.login.domain

import com.google.gson.Gson
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.common.domain.verifyFlowUseCase.VerifyByTypeTransactionUseCase
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyCreateTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.feature.login.data.UserInfoSettingRepository
import vn.com.bidv.feature.login.domain.model.CompositeOtpResDMO
import vn.com.bidv.feature.login.domain.model.UserPersonalInfoDMO
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class UserInfoVerifyUseCase @Inject constructor(
    private val userInfoSettingRepository: UserInfoSettingRepository,
    private val userInfoUseCase: UserInfoUseCase
) : VerifyByTypeTransactionUseCase {

    override suspend fun initTransaction(input: InputVerifyTransaction): DomainResult<InitVerifyTransactionResponse> {
        return DomainResult.Success(InitVerifyTransactionResponse())
    }

    override suspend fun initCreateTransaction(input: InputVerifyCreateTransaction): DomainResult<InitVerifyTransactionResponse> {
        val result = userInfoSettingRepository.getUserInfoSettingSecure()
        val domain = result.convert(CompositeOtpResDMO::class.java)
        return if (domain is DomainResult.Success) {
            val compositeOtpResDMO = domain.data
            val smartOtp = compositeOtpResDMO?.smartOtp
            DomainResult.Success(
                InitVerifyTransactionResponse(
                    transAuth = smartOtp?.copy(
                        transKey = compositeOtpResDMO.transId
                    )
                )
            )
        } else {
            val domainError = domain as? DomainResult.Error
            DomainResult.Error(domainError?.errorCode ?: "", domainError?.errorMessage)
        }
    }

    override suspend fun verifyTransaction(
        initResponse: InitVerifyTransactionResponse,
        reqValue: String?
    ): DomainResult<String> {
        val result = userInfoSettingRepository.verifySmartOtpUserInfo(initResponse.transKey ?: initResponse.transAuth?.transKey ?: "", reqValue ?: "")
        val domain = result.convert(UserPersonalInfoDMO::class.java)
        return if (domain is DomainResult.Success) {
            DomainResult.Success(Gson().toJson(domain.data))
        } else {
            val domainError = domain as? DomainResult.Error
            DomainResult.Error(domainError?.errorCode ?: "", domainError?.errorMessage)
        }
    }
}