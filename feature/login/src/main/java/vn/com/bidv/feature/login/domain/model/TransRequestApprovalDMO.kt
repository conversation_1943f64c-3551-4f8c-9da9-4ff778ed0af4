package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

@Serializable
data class TransRequestApprovalDMO(
    @SerializedName("id")
    val id: String? = null,

    @SerializedName("userId")
    val userId: Long? = null,

    @SerializedName("fullName")
    val fullName: String? = null,

    @SerializedName("roleGroup")
    val roleGroup: String? = null,

    @SerializedName("phoneNumber")
    val phoneNumber: String? = null,

    @SerializedName("otpEmail")
    val otpEmail: String? = null,

    @SerializedName("requestType")
    val requestType: String? = null,

    @SerializedName("createdDate")
    val createdDate: String? = null,

    @SerializedName("createdBy")
    val createdBy: String? = null,

    @SerializedName("errorDesc")
    val errorDesc: String? = null,

    @SerializedName("errorCode")
    val errorCode: String? = null,

    @SerializedName("userName")
    val userName: String? = null
)
