package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName

data class UserInfoSettingsDMO(
    @SerializedName("userInfo")
    val userInfo: UserInfoDMO? = null,

    @SerializedName("cusInfo")
    val cusInfo: CustomerInfoDMO? = null
)

data class UserInfoDMO(
    @SerializedName("name")
    val name: kotlin.String? = null,

    @SerializedName("username")
    val username: kotlin.String? = null,

    @SerializedName("otpType")
    val otpType: kotlin.String? = null,

    @SerializedName("authType")
    val authType: kotlin.String? = null,

    @SerializedName("role")
    val role: kotlin.String? = null,

    @SerializedName("wfRole")
    val wfRole: kotlin.String? = null,

    @SerializedName("limit")
    val limit: java.math.BigDecimal? = null
)

class CustomerInfoDMO(
    @SerializedName("cifNo")
    val cifNo: kotlin.String? = null,

    @SerializedName("name")
    val name: kotlin.String? = null,

    @SerializedName("idReg")
    val idReg: kotlin.String? = null
)
