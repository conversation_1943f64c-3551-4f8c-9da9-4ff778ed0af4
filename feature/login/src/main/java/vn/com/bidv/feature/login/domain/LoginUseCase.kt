package vn.com.bidv.feature.login.domain

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import vn.com.bidv.common.sharePreference.Storage
import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.common.domain.data.GetUserInfoResponseDMO
import vn.com.bidv.feature.common.domain.data.UserResDMO
import vn.com.bidv.feature.common.domain.smartotp.SmartOTPKey
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.data.LoginRepository
import vn.com.bidv.feature.login.domain.model.BiometricChallengeResponseDMO
import vn.com.bidv.feature.login.domain.model.BiometricData
import vn.com.bidv.feature.login.domain.model.ModelCreateOtpResDMO
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.sdkbase.domain.DomainErrorCode
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import vn.com.bidv.secure.secure.smartotp.ISmartOTPSecure
import vn.com.bidv.secure.utils.BiometricUtils
import javax.inject.Inject

class LoginUseCase @Inject constructor(
    private val loginRepository: LoginRepository,
    private val userInfoUseCase: UserInfoUseCase,
    private val smartOTPSecure: ISmartOTPSecure,
    @ApplicationContext private val context: Context,
)  {
    suspend operator fun invoke(
        userName: String,
        password: String
    ): DomainResult<ModelLoginDMO> {
        val listClientError = mutableListOf<Pair<String, String>>()
        if (userName.isBlank()) {
            listClientError.add(Constants.ViewID.USER_NAME_VIEW_ID to LoginErrorCode.USER_NAME_EMPTY)
        }
        if (password.isBlank()) {
            listClientError.add(Constants.ViewID.PASSWORD_VIEW_ID to LoginErrorCode.PASSWORD_EMPTY)
        } else {
            if (password.length < 8) {
                listClientError.add(Constants.ViewID.PASSWORD_VIEW_ID to LoginErrorCode.LESS_THAN_8_CHARACTERS)
            }
        }
        return if (listClientError.isNotEmpty()) {
            DomainResult.Error(
                errorCode = DomainErrorCode.CLIENT_ERROR_CODE,
                listClientErrorCode = listClientError
            )
        } else {
            val result = loginRepository.login(userName, password)
            val modelLoginDMO = result.convert(ModelLoginDMO::class.java)
            if (modelLoginDMO is DomainResult.Success) {
                if (modelLoginDMO.data?.status == Constants.LOGIN_ACCEPTED) {
                    val userInfo = GetUserInfoResponseDMO(
                        user = UserResDMO(
                            username = modelLoginDMO.data?.username,
                            langCode = modelLoginDMO.data?.langCode,
                            fullname = modelLoginDMO.data?.userFullName,
                            userId = modelLoginDMO.data?.userId?.toInt(),
                            cifName = modelLoginDMO.data?.cifName,
                            roleCode = modelLoginDMO.data?.userRole
                        ),
                    )
                    userInfoUseCase.saveUserInfoToStorage(userInfo)
                }
                modelLoginDMO.data?.apply {
                    isShowSecurityQuestionScreen = isNeedToSetupSecurityQuestion(
                        this.userRole ?: "",
                        this.isSecurityQuestionSet ?: false
                    )
                    isShowSmartOtpScreen = isNeedToSetupSmartOtp(
                        this.smartOtpDMO?.isSmartOtpSet ?: false,
                        this.smartOtpDMO?.regStatus ?: ""
                    )
                }
            }
            modelLoginDMO
        }
    }

    fun getDataFromStorage(): DomainResult<GetUserInfoResponseDMO> {
        val userProfile = userInfoUseCase.getUserInfoFromStorage()
        return userProfile
    }

    suspend fun reSendSmsOtp(
        method: String,
        userName: String,
        txnId: String
    ): DomainResult<ModelCreateOtpResDMO> {
        val result = loginRepository.reSendSmsOtp(
            method,
            userName,
            txnId,
        )
        val modelCreateOtpResDMO = result.convert(ModelCreateOtpResDMO::class.java)
        return modelCreateOtpResDMO
    }

    suspend fun verifyOtp(
        method: String,
        userName: String,
        txnId: String,
        otpNum: String,
        fcmId: String
    ): DomainResult<ModelLoginDMO> {
        val result = loginRepository.verifyOtp(
            method,
            userName,
            txnId,
            otpNum,
            fcmId,
        )
        val modelLoginDMO = result.convert(ModelLoginDMO::class.java)
        if (modelLoginDMO is DomainResult.Success) {
            modelLoginDMO.data?.apply {
                isShowSecurityQuestionScreen = isNeedToSetupSecurityQuestion(
                    this.userRole ?: "",
                    this.isSecurityQuestionSet ?: false
                )
                isShowSmartOtpScreen = isNeedToSetupSmartOtp(
                    this.smartOtpDMO?.isSmartOtpSet ?: false,
                    this.smartOtpDMO?.regStatus ?: ""
                )
            }
            if (modelLoginDMO.data?.status == Constants.LOGIN_ACCEPTED) {
                val userInfo = GetUserInfoResponseDMO(
                    user = UserResDMO(
                        username = modelLoginDMO.data?.username,
                        langCode = modelLoginDMO.data?.langCode,
                        fullname = modelLoginDMO.data?.userFullName,
                        userId = modelLoginDMO.data?.userId?.toInt(),
                        cifName = modelLoginDMO.data?.cifName,
                        roleCode = modelLoginDMO.data?.userRole
                    ),
                )
                userInfoUseCase.saveUserInfoToStorage(userInfo)
            }
        }
        return modelLoginDMO
    }

    fun changeUser(): DomainResult<Boolean> {
        userInfoUseCase.clearUserInfoCache()
        Storage.clear(
            keepKeys = arrayOf(
                SmartOTPKey.USER_ACTIVE_SMART_OTP,
                SmartOTPKey.SYNC_TIME,
                smartOTPSecure.getSmartOTPSecureKey()
            )
        )
        context.deleteDatabase(vn.com.bidv.feature.common.constants.Constants.IBankDatabase.UTILITY_DATABASE)
        return DomainResult.Success(true)
    }

    suspend fun getUserInfo(): DomainResult<GetUserInfoResponseDMO> {
        val result = loginRepository.getUserInfo()
        val model = result.convert(GetUserInfoResponseDMO::class.java)
        return model
    }

    fun checkBiometricEnable(): DomainResult<Boolean> {
        val biometricEnable = BiometricUtils.isBiometricEnable(context)
        return DomainResult.Success(biometricEnable)
    }

    suspend fun biometricLoginVerify(
        userName: String,
        biometricData: BiometricData?
    ): DomainResult<ModelLoginDMO> {
        val result = loginRepository.getChallengeCode(userName, biometricData?.cr ?: "")
        val model = result.convert(BiometricChallengeResponseDMO::class.java)
        return if (model is DomainResult.Success) {
            val signed = BiometricUtils.signData(
                model.data?.challengeCode ?: "",
                biometricData?.pr
            )
            val biometricVerify = loginRepository.biometricLoginVerify(
                userName,
                biometricData?.cr ?: "",
                model.data?.transId ?: "",
                signed ?: ""
            )
            val biometricVerifyDMO = biometricVerify.convert(ModelLoginDMO::class.java)
            biometricVerifyDMO
        } else {
            model as DomainResult.Error
        }
    }

    private fun isNeedToSetupSecurityQuestion(
        userRole: String,
        isSecurityQuestionSet: Boolean
    ): Boolean {
        return !isSecurityQuestionSet && userRole.contains(Constants.ADMIN_ROLE, true)
    }

    private fun isNeedToSetupSmartOtp(isSmartOtpSet: Boolean, regStatus: String): Boolean {
        return isSmartOtpSet && regStatus.contains(Constants.WAITING_ACTIVATION, true)
    }
}
