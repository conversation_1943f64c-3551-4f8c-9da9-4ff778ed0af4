package vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen

import dagger.hilt.android.lifecycle.HiltViewModel
import vn.com.bidv.feature.login.domain.GetAllQuestionUseCase
import vn.com.bidv.feature.login.domain.ManageQuestionByUserUseCase
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.SetupSecurityQuestionsReducer.SetupSecurityQuestionsSideEffect
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.SetupSecurityQuestionsReducer.SetupSecurityQuestionsViewEvent
import vn.com.bidv.feature.login.ui.setupsecurityquestionsscreen.SetupSecurityQuestionsReducer.SetupSecurityQuestionsViewState
import vn.com.bidv.sdkbase.domain.DomainErrorCode
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class SetupSecurityQuestionsContentViewModel @Inject constructor(
    private val manageQuestionByUserUseCase: ManageQuestionByUserUseCase,
    private val getAllQuestionUseCase: GetAllQuestionUseCase,
) : ViewModelIBankBase<SetupSecurityQuestionsViewState, SetupSecurityQuestionsViewEvent, SetupSecurityQuestionsSideEffect>(
    initialState = SetupSecurityQuestionsViewState(),
    reducer = SetupSecurityQuestionsReducer()
) {

    override fun handleEffect(
        sideEffect: SetupSecurityQuestionsSideEffect,
        onResult: (SetupSecurityQuestionsViewEvent) -> Unit
    ) {
        if (sideEffect is SetupSecurityQuestionsSideEffect.GetAllQuestionEffect) {
            callDomain(
                onSuccess = { result ->
                    onResult(
                        SetupSecurityQuestionsViewEvent.GetQuestionSuccessEvent(
                            initData = result.data
                        )
                    )

                }
            ) {
                getAllQuestionUseCase.invoke()
            }
        }

        if (sideEffect is SetupSecurityQuestionsSideEffect.SubmitDataEffect) {
            callDomain(
                showLoadingIndicator = sideEffect.dataSubmit.any { it.questionCode?.isNotEmpty() == true && it.answer?.isNotEmpty() == true },
                listErrorCodeListen = listOf(DomainErrorCode.CLIENT_ERROR_CODE),
                onFail = {
                    onResult(
                        SetupSecurityQuestionsViewEvent.SubmitDataErrorEvent(
                            listClientErrorCode = it?.listClientErrorCode ?: listOf(),
                        )
                    )
                },
                onSuccess = {
                    onResult(SetupSecurityQuestionsViewEvent.SubmitDataSuccessEvent)
                }
            ) {
                getAllQuestionUseCase.saveQuestions(sideEffect.dataSubmit)
            }
        }

        if (sideEffect is SetupSecurityQuestionsSideEffect.SubmitDataWithAuthenticationEffect) {
            callDomain(
                showLoadingIndicator = sideEffect.dataSubmit.any { it.questionCode?.isNotEmpty() == true && it.answer?.isNotEmpty() == true },
                listErrorCodeListen = listOf(DomainErrorCode.CLIENT_ERROR_CODE),
                onSuccess = { result ->
                    onResult(
                        SetupSecurityQuestionsViewEvent.SubmitDataWithAuthenticationSuccessEvent(
                            listSubmit = result.data ?: emptyList()
                        )
                    )
                },
                onFail = {
                    onResult(
                        SetupSecurityQuestionsViewEvent.SubmitDataErrorEvent(
                            listClientErrorCode = it?.listClientErrorCode ?: listOf(),
                        )
                    )
                },
            ) {
                manageQuestionByUserUseCase.validateQuestionWithAuthentication(sideEffect.dataSubmit)
            }
        }

    }
}
