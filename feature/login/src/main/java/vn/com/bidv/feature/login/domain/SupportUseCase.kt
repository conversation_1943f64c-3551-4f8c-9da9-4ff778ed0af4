package vn.com.bidv.feature.login.domain

import vn.com.bidv.feature.common.constants.Constants
import vn.com.bidv.network.NetworkConfig
import vn.com.bidv.sdkbase.utils.ItemLanguage
import javax.inject.Inject

class SupportUseCase @Inject constructor(
    private val networkConfig: NetworkConfig,
) {
    fun getLinkSupport(itemLanguage: ItemLanguage): Map<Constants.UrlType, String> {
        val locale = itemLanguage.apiDefLang
        val paramsUrl = "?locale=${locale}"

        val supportUrls: Map<Constants.UrlType, String> =
            networkConfig.supportUrls.mapKeys { entry -> Constants.UrlType.valueOf(entry.key.uppercase()) }
        val urlMapping = mapOf(
            Constants.UrlType.GUIDELINE to supportUrls[Constants.UrlType.GUIDELINE] + paramsUrl,
            Constants.UrlType.FAQ to supportUrls[Constants.UrlType.FAQ] + paramsUrl,
            Constants.UrlType.TERMS to supportUrls[Constants.UrlType.TERMS] + paramsUrl
        )

        return urlMapping
    }
}