package vn.com.bidv.feature.login.ui.approvalrequestsresult

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.login.domain.model.AdminTransVerifyDMO
import vn.com.bidv.feature.login.domain.model.TransRequestApprovalDMO

class ApprovalRequestsResultReducer :
    Reducer<ApprovalRequestsResultReducer.ApprovalRequestsResultViewState, ApprovalRequestsResultReducer.ApprovalRequestsResultViewEvent, ApprovalRequestsResultReducer.ApprovalRequestsResultViewEffect> {

    @Immutable
    data class ApprovalRequestsResultViewState(
        val initSuccess: Boolean = false,
        val dataVerify: AdminTransVerifyDMO = AdminTransVerifyDMO(),
    ) : ViewState

    @Immutable
    sealed class ApprovalRequestsResultViewEvent : ViewEvent {
        data class OnInitScreen(
            val dataVerify: AdminTransVerifyDMO,
        ) : ApprovalRequestsResultViewEvent()
    }

    @Immutable
    sealed class ApprovalRequestsResultViewEffect : SideEffect {

    }

    override fun reduce(
        previousState: ApprovalRequestsResultViewState,
        event: ApprovalRequestsResultViewEvent,
    ): Pair<ApprovalRequestsResultViewState, ApprovalRequestsResultViewEffect?> {
        return handleApprovalRequestsResultViewState(previousState, event)
    }

    private fun handleApprovalRequestsResultViewState(
        previousState: ApprovalRequestsResultViewState,
        event: ApprovalRequestsResultViewEvent
    ): Pair<ApprovalRequestsResultViewState, ApprovalRequestsResultViewEffect?> {
        return when (event) {
            is ApprovalRequestsResultViewEvent.OnInitScreen -> {
                previousState.copy(
                    initSuccess = true,
                    dataVerify = event.dataVerify,
                ) to null
            }
        }
    }
}
