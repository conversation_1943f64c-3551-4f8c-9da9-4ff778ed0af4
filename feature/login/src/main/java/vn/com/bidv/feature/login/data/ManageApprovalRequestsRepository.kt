package vn.com.bidv.feature.login.data

import vn.com.bidv.feature.common.data.utilities.apis.UtilitiesApi
import vn.com.bidv.feature.common.data.utilities.model.AdminSearchApprovalRequest
import vn.com.bidv.feature.common.data.utilities.model.AdminTransActionRequest
import vn.com.bidv.feature.common.data.utilities.model.Page
import vn.com.bidv.feature.common.data.utilities.model.TransApproveRequest
import vn.com.bidv.feature.login.ui.manageapprovalrequest.model.Action
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class ManageApprovalRequestsRepository @Inject constructor(
    private val utilitiesApi: UtilitiesApi,
) : BaseRepository() {

    suspend fun getPendingApprovalRequests(
        pageSize: Int,
        pageNumber: Int,
        getTotal: Boolean = true,
        searchText: String = ""
    ) = launch {
        utilitiesApi.search(
            AdminSearchApprovalRequest(
                page = Page(
                    pageSize = pageSize,
                    pageNum = pageNumber,
                    getTotal = getTotal
                ),
                searchTerm = searchText
            )
        )
    }

    suspend fun performGetApprovalRequestsData(ids: List<String>) = launch {
        utilitiesApi.action(
            AdminTransActionRequest(
                ids = ids,
                actionCode = Action.APPROVE.name
            )
        )
    }

    suspend fun performGetRejectRequestsData(ids: List<String>) = launch {
        utilitiesApi.action(
            AdminTransActionRequest(
                ids = ids,
                actionCode = Action.REJECT.name
            )
        )
    }

    suspend fun performVerifyApprovalRequest(transKey: String, authValue: String) = launch {
        utilitiesApi.verify(
            TransApproveRequest(
                transKey = transKey,
                authValue = authValue
            )
        )
    }

    suspend fun performRejectRequest(ids: List<String>, actionType: String, reason: String) = launch {
        utilitiesApi.reject(
            AdminTransActionRequest(
                ids = ids,
                actionCode = actionType,
                reason = reason
            )
        )
    }
}