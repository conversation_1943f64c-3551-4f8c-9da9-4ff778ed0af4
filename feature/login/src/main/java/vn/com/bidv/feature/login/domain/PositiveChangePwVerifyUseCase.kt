package vn.com.bidv.feature.login.domain

import com.google.gson.Gson
import vn.com.bidv.feature.common.domain.verifyFlowUseCase.VerifyByTypeTransactionUseCase
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InitVerifyTransactionResponse
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyCreateTransaction
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.InputVerifyTransaction
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.data.PasswordRepository
import vn.com.bidv.feature.login.data.VerifySecureOtpRepository
import vn.com.bidv.feature.login.domain.model.CompositeOtpResDMO
import vn.com.bidv.feature.login.ui.changepassword.model.ModelChangPasswordUI
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class PositiveChangePwVerifyUseCase @Inject constructor(
    private val verifySecureOtpRepository: VerifySecureOtpRepository,
    private val passwordRepository: PasswordRepository,
) : VerifyByTypeTransactionUseCase {

    override suspend fun initTransaction(input: InputVerifyTransaction): DomainResult<InitVerifyTransactionResponse> {
        return DomainResult.Success(InitVerifyTransactionResponse())
    }

    override suspend fun initCreateTransaction(input: InputVerifyCreateTransaction): DomainResult<InitVerifyTransactionResponse> {
        val modelChangPasswordUI = Gson().fromJson(input.dataString, ModelChangPasswordUI::class.java)

        val result = passwordRepository.positiveChangePw(
            newPassword = modelChangPasswordUI.newPassword,
            oldPassword = modelChangPasswordUI.oldPassword,
            confirmPassword = modelChangPasswordUI.confirmPassword,
        )
        val domain = result.convert(CompositeOtpResDMO::class.java)
        return if (domain is DomainResult.Success) {
            val compositeOtpResDMO = domain.data
            val smartOtp = compositeOtpResDMO?.smartOtp
            DomainResult.Success(
                InitVerifyTransactionResponse(
                    transAuth = smartOtp?.copy(
                        transKey = compositeOtpResDMO.transId
                    )
                )
            )
        } else {
            val domainError = domain as? DomainResult.Error
            DomainResult.Error(domainError?.errorCode ?: "", domainError?.errorMessage)
        }
    }

    override suspend fun verifyTransaction(
        initResponse: InitVerifyTransactionResponse, reqValue: String?
    ): DomainResult<String> {
        val result = verifySecureOtpRepository.otpSecuredVerify(
            transId = initResponse.transKey ?: initResponse.transAuth?.transKey ?: "",
            otpNum = reqValue ?: "",
            method = Constants.PW_CHANGE
        )
        if (result is NetworkResult.Error) {
           return DomainResult.Success(
                Gson().toJson(
                    DomainResult.Error(
                        errorCode = handleError(result.errorCode),
                        errorMessage = result.errorMessage
                    )
                )
            )

        }
        return DomainResult.Success(true.toString())
    }

    private fun handleError(errorCode: String): String {
        return when (errorCode) {
            LoginErrorCode.IM9004 -> VerifyPwOtpErrorType.SMS_OTP_BLOCK
            else -> errorCode
        }
    }
}