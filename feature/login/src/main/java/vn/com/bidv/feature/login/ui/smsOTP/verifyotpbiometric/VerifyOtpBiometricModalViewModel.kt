package vn.com.bidv.feature.login.ui.smsOTP.verifyotpbiometric

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.feature.login.domain.VerifySecureOtpUseCase
import vn.com.bidv.feature.login.domain.model.ModelCreateOtpResDMO
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPReducer
import vn.com.bidv.feature.login.ui.smsOTP.smsotpbase.BaseIBankModalOTPViewModel
import javax.inject.Inject

@HiltViewModel
class VerifyOtpBiometricModalViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val verifySecureOtpUseCase: VerifySecureOtpUseCase,
) : BaseIBankModalOTPViewModel<ModelCreateOtpResDMO, ModelCreateOtpResDMO, Boolean>(
    initialState = BaseIBankModalOTPReducer.BaseIBankModalOTPViewState(),
    reducer = BaseIBankModalOTPReducer()
) {

    private val SETTING_BIOMETRIC = "SETTING_BIOMETRIC"
    override fun handleEffect(
        sideEffect: BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect<ModelCreateOtpResDMO, ModelCreateOtpResDMO, Boolean>,
        onResult: (BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent<ModelCreateOtpResDMO, ModelCreateOtpResDMO, Boolean>) -> Unit
    ) {
        when (sideEffect) {
            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.RetrySendOTP -> {
                callDomain(onSuccess = {
                    onResult(
                        BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnRetrySendOTPSuccess(
                            it.data
                        )
                    )
                }) {
                    verifySecureOtpUseCase.otpSecuredResend(
                        transId = sideEffect.txnId,
                        method = SETTING_BIOMETRIC
                    )
                }
            }

            is BaseIBankModalOTPReducer.BaseIBankModalOTPViewEffect.VerifyOTP -> {
                callDomain(
                    isListenAllError = true,
                    onSuccess = {
                        shareVerifyData {
                            verifySecureOtpUseCase.shareVerifyBiometricData()
                        }
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPSuccess(
                                true
                            )
                        )
                    },
                    onFail = { error ->
                        onResult(
                            BaseIBankModalOTPReducer.BaseIBankModalOTPViewEvent.OnVerifyOTPError(
                                error?.errorCode,
                                error?.errorMessage
                            )
                        )
                    }
                ) {
                    verifySecureOtpUseCase.otpSecuredVerify(
                        transId = sideEffect.txnId,
                        otpNum = sideEffect.otpNum,
                        method = SETTING_BIOMETRIC
                    )
                }
            }
        }
    }

    private fun shareVerifyData(onShareData: suspend () -> Unit) {
        viewModelScope.launch(Dispatchers.Default) {
            onShareData()
        }
    }

}