package vn.com.bidv.feature.login.domain

import vn.com.bidv.feature.common.domain.UserInfoUseCase
import vn.com.bidv.feature.common.domain.data.GetUserInfoResponseDMO
import vn.com.bidv.feature.common.domain.data.UserResDMO
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.data.LoginRepository
import vn.com.bidv.feature.login.data.PasswordRepository
import vn.com.bidv.feature.login.domain.model.CreatePositiveChangePwResDMO
import vn.com.bidv.feature.login.domain.model.ModelCreateOtpResDMO
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.sdkbase.domain.DomainErrorCode
import vn.com.bidv.sdkbase.domain.DomainResult
import vn.com.bidv.sdkbase.utils.convert
import javax.inject.Inject

class ChangePwUseCase @Inject constructor(
    private val passwordRepository: PasswordRepository,
    private val loginRepository: LoginRepository,
    private val userInfoUseCase: UserInfoUseCase,
) {

    suspend fun getWrongPwAttempts(): DomainResult<Boolean> {
        val result = passwordRepository.getWrongPwAttempts()
        val domain = result.convert {
            this.isSuccess()
        }
        return domain
    }

    suspend fun performPositiveChangePassword(
        oldPassword: String,
        newPassword: String,
        confirmPassword: String,
        userName: String
    ): DomainResult<CreatePositiveChangePwResDMO> {
        val result = passwordRepository.positiveChangePw(
            oldPassword,
            newPassword,
            confirmPassword
        )
        return if (result is NetworkResult.Error) {
            DomainResult.Error(
                errorCode = handleError(result.errorCode),
                errorMessage = result.errorMessage
            )
        } else {
            val modelCreateOtpResDMO = result.convert(CreatePositiveChangePwResDMO::class.java)
            modelCreateOtpResDMO
        }
    }

    fun validClientInputData(
        oldPassword: String,
        newPassword: String,
        confirmPassword: String,
        userName: String
    ): DomainResult<Boolean> {
        val listClientError = mutableListOf<Pair<String, String>>()
        val validOldPassword = validOldPassword(oldPassword)
        if (validOldPassword is DomainResult.Error) {
            listClientError.addAll(validOldPassword.listClientErrorCode ?: listOf())
        }
        val validNewPassword = validNewPassword(newPassword, userName)
        if (validNewPassword is DomainResult.Error) {
            listClientError.addAll(validNewPassword.listClientErrorCode ?: listOf())
        }
        val validConfirmPassword = validConfirmPassword(newPassword, confirmPassword)
        if (validConfirmPassword is DomainResult.Error) {
            listClientError.addAll(validConfirmPassword.listClientErrorCode ?: listOf())
        }
        return if (listClientError.isNotEmpty()) {
            DomainResult.Error(
                errorCode = DomainErrorCode.CLIENT_ERROR_CODE,
                listClientErrorCode = listClientError
            )
        } else {
            DomainResult.Success(true)
        }
    }

    suspend fun performChangePassword(
        method: String,
        oldPassword: String,
        newPassword: String,
        confirmPassword: String,
        userName: String
    ): DomainResult<ModelCreateOtpResDMO> {
        val result = loginRepository.changePWCreate(
            method, userName, oldPassword, newPassword, confirmPassword
        )
        return if (result is NetworkResult.Error) {
            return DomainResult.Error(
                errorCode = handleError(result.errorCode), errorMessage = result.errorMessage
            )
        } else {
            val modelCreateOtpResDMO = result.convert(ModelCreateOtpResDMO::class.java)
            modelCreateOtpResDMO
        }
    }

    suspend fun reSendSmsOtp(
        method: String,
        userName: String,
        txnId: String
    ): DomainResult<ModelCreateOtpResDMO> {
        val result = loginRepository.reSendSmsOtp(
            method,
            userName,
            txnId,
        )
        val modelCreateOtpResDMO = result.convert(ModelCreateOtpResDMO::class.java)
        return modelCreateOtpResDMO
    }

    suspend fun verifyOtp(
        method: String,
        userName: String,
        txnId: String,
        otpNum: String,
        fcmId: String
    ): DomainResult<ModelLoginDMO> {
        val result = loginRepository.verifyOtp(
            method,
            userName,
            txnId,
            otpNum,
            fcmId,
        )
        val modelLoginDMO = result.convert(ModelLoginDMO::class.java)
        if (modelLoginDMO is DomainResult.Success) {
            modelLoginDMO.data?.apply {
                isShowSecurityQuestionScreen = isNeedToSetupSecurityQuestion(
                    this.userRole ?: "",
                    this.isSecurityQuestionSet ?: false
                )
                isShowSmartOtpScreen = isNeedToSetupSmartOtp(
                    this.smartOtpDMO?.isSmartOtpSet ?: false,
                    this.smartOtpDMO?.regStatus ?: ""
                )
            }
            if (modelLoginDMO.data?.status == Constants.LOGIN_ACCEPTED) {
                var userInfo = GetUserInfoResponseDMO(
                    user = UserResDMO(
                        username = modelLoginDMO.data?.username,
                        langCode = modelLoginDMO.data?.langCode,
                        fullname = modelLoginDMO.data?.userFullName,
                        userId = modelLoginDMO.data?.userId?.toInt(),
                        cifName = modelLoginDMO.data?.cifName,
                    ),
                )
                val userInfoResponseDMO = getUserInfo()
                if (userInfoResponseDMO is DomainResult.Success) {
                    userInfo = userInfoResponseDMO.data ?: userInfo
                }
                userInfoUseCase.saveUserInfoToStorage(userInfo)
            }
        }
        return modelLoginDMO
    }

    fun validOldPassword(oldPassword: String): DomainResult<Boolean> {
        val listClientError = mutableListOf<Pair<String, String>>()
        if (oldPassword.isBlank()) {
            listClientError.add(Constants.ViewID.OLD_PASSWORD_VIEW_ID to LoginErrorCode.PASSWORD_EMPTY)
        } else {
            if (oldPassword.length < 8) {
                listClientError.add(Constants.ViewID.OLD_PASSWORD_VIEW_ID to LoginErrorCode.LESS_THAN_8_CHARACTERS)
            }
        }
        return if (listClientError.isNotEmpty()) {
            DomainResult.Error(
                errorCode = DomainErrorCode.CLIENT_ERROR_CODE,
                listClientErrorCode = listClientError
            )
        } else {
            DomainResult.Success(true)
        }
    }

    fun validNewPassword(newPassword: String, userName: String): DomainResult<Boolean> {
        val listClientError = mutableListOf<Pair<String, String>>()
        if (newPassword.isBlank()) {
            listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.PASSWORD_EMPTY)
            listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.LESS_THAN_8_CHARACTERS)
            listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.NOT_CONTAIN_CHARACTER_LOWER)
            listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.NOT_CONTAIN_CHARACTER_UPPER)
            listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.NOT_CONTAIN_NUMBER)
            listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.NOT_CONTAIN_SPECIAL_CHARACTER)
            listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.CONTAIN_REPEATED_CHARACTER)
            listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.OVERLAP_3_CONSECUTIVE_CHARACTERS_USER_NAME)
        } else {
            if (newPassword.length < 8) {
                listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.LESS_THAN_8_CHARACTERS)
            }
            if (!newPassword.matches(Regex(Constants.NORMAL_CHARACTERS))) {
                listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.NOT_CONTAIN_CHARACTER_LOWER)
            }
            if (!newPassword.matches(Regex(Constants.UPPER_CHARACTERS))) {
                listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.NOT_CONTAIN_CHARACTER_UPPER)
            }
            if (!newPassword.matches(Regex(Constants.NUMBER_CHARACTERS))) {
                listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.NOT_CONTAIN_NUMBER)
            }
            if (!newPassword.matches(Regex(Constants.SPECIAL_CHARACTERS))) {
                listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.NOT_CONTAIN_SPECIAL_CHARACTER)
            }
            if (containsRepeatedOrConsecutiveSequences(newPassword)) {
                listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.CONTAIN_REPEATED_CHARACTER)
            }
            if (isPasswordContainUsernameSequence(newPassword, userName)) {
                listClientError.add(Constants.ViewID.NEW_PASSWORD_VIEW_ID to LoginErrorCode.OVERLAP_3_CONSECUTIVE_CHARACTERS_USER_NAME)
            }
        }
        return if (listClientError.isNotEmpty()) {
            DomainResult.Error(
                errorCode = DomainErrorCode.CLIENT_ERROR_CODE,
                listClientErrorCode = listClientError
            )
        } else {
            DomainResult.Success(true)
        }
    }

    fun validConfirmPassword(
        newPassword: String,
        confirmPassword: String
    ): DomainResult<Boolean> {
        val listClientError = mutableListOf<Pair<String, String>>()
        if (confirmPassword.isBlank()) {
            listClientError.add(Constants.ViewID.CONFIRM_PASSWORD_VIEW_ID to LoginErrorCode.PASSWORD_EMPTY)
        }
        if (newPassword != confirmPassword) {
            listClientError.add(Constants.ViewID.CONFIRM_PASSWORD_VIEW_ID to LoginErrorCode.CONFIRM_PASS_NOT_MATCH_NEW_PASS)
        }
        return if (listClientError.isNotEmpty()) {
            DomainResult.Error(
                errorCode = DomainErrorCode.CLIENT_ERROR_CODE,
                listClientErrorCode = listClientError
            )
        } else {
            DomainResult.Success(true)
        }
    }

    private fun containsRepeatedOrConsecutiveSequences(password: String): Boolean {
        val passwordLower = password.lowercase()
        val repeatedPattern = Regex("(.)\\1{2,}")
        val consecutivePattern =
            Regex("(${Constants.CONSECUTIVE_CHARACTERS})")
        return repeatedPattern.containsMatchIn(passwordLower) || consecutivePattern.containsMatchIn(
            passwordLower
        )
    }

    private fun isPasswordContainUsernameSequence(password: String, username: String): Boolean {
        val passwordLower = password.lowercase()
        val userNameLower = username.lowercase()
        val regexPattern = (0..username.length - 3).joinToString("|") {
            Regex.escape(userNameLower.substring(it, it + 3))
        }
        val regex = Regex(regexPattern)
        return regex.containsMatchIn(passwordLower)
    }

    private fun isNeedToSetupSecurityQuestion(
        userRole: String,
        isSecurityQuestionSet: Boolean
    ): Boolean {
        return !isSecurityQuestionSet && userRole.contains(Constants.ADMIN_ROLE, true)
    }

    private fun isNeedToSetupSmartOtp(isSmartOtpSet: Boolean, regStatus: String): Boolean {
        return isSmartOtpSet && regStatus.contains(Constants.WAITING_ACTIVATION, true)
    }

    suspend fun getUserInfo(): DomainResult<GetUserInfoResponseDMO> {
        val result = loginRepository.getUserInfo()
        val model = result.convert(GetUserInfoResponseDMO::class.java)
        return model
    }

    private fun handleError(errorCode: String): String {
        return when (errorCode) {
            LoginErrorCode.IM9004 -> VerifyPwOtpErrorType.SMS_OTP_BLOCK
            LoginErrorCode.IM9006 -> VerifyPwOtpErrorType.SMART_OTP_HAS_EXPIRED

            else -> VerifyPwOtpErrorType.DEFAULT_ERROR
        }
    }
}