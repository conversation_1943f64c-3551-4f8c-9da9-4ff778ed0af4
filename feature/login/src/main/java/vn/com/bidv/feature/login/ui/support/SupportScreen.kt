package vn.com.bidv.feature.login.ui.support

import OpenBrowser
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessage
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessageStatus
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.login.ui.support.SupportReducer.SupportEvent
import vn.com.bidv.feature.login.ui.support.SupportReducer.SupportViewState
import vn.com.bidv.localization.R
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.feature.common.constants.Constants as CommonConstants
import vn.com.bidv.localization.R as RLocalization

@Composable
fun SupportScreen(navController: NavHostController) {
    val vm: SupportViewModel = hiltViewModel()
    val context = LocalContext.current

    BaseScreen(
        navController = navController,
        viewModel = vm,
        renderContent = { uiState, onEvent ->
            SupportContent(uiState, onEvent, navController)
        },
        handleSideEffect = { _ ->
        },
        topAppBarType = TopAppBarType.Title,
        backgroundColor = LocalColorScheme.current.bgMainPrimary,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(RLocalization.string.ho_tro),
            showHomeIcon = false
        )
    )
}

@Composable
private fun SupportContent(
    uiState: SupportViewState,
    onEvent: (SupportEvent) -> Unit,
    navController: NavHostController,
) {

    if (!uiState.isInitSuccess) {
        onEvent(SupportEvent.InitData)
    }

    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current
    val context = LocalContext.current
    var urlWebView by remember { mutableStateOf("") }

    val emailTextTitle = buildAnnotatedString {
        val baseStyle = typography.bodyBody_l
        appendStyledText(
            "${context.getString(R.string.email)} ",
            baseStyle,
            colorScheme.contentMainPrimary
        )
        appendStyledText(CommonConstants.EMAIL_OPERATOR, baseStyle, colorScheme.contentBrand_01Secondary)
    }

    val operatorTextTitle = buildAnnotatedString {
        val baseStyle = typography.bodyBody_l
        appendStyledText(
            context.getString(R.string.tong_dai_s, ""),
            baseStyle,
            colorScheme.contentMainPrimary
        )
        appendStyledText(CommonConstants.TEL_OPERATOR, baseStyle, colorScheme.contentBrand_01Secondary)
    }

    // open browser when click link
    if (urlWebView.isNotBlank()) {
        OpenBrowser(urlWebView)
        urlWebView = ""
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .padding(
                    horizontal = IBSpacing.spacingM, vertical = IBSpacing.spacingXs
                )
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingM, Alignment.Top),
            horizontalAlignment = Alignment.Start,
        ) {

            uiState.urls[CommonConstants.UrlType.GUIDELINE]?.let { url ->
                SupportNavigationCard(
                    iconId = RDesignSystem.drawable.huong_dan_su_dung,
                    title = stringResource(RLocalization.string.huong_dan_su_dung),
                    onClick = {
                        urlWebView = url
                    })
            }

            uiState.urls[CommonConstants.UrlType.FAQ]?.let { url ->
                SupportNavigationCard(
                    iconId = RDesignSystem.drawable.cau_hoi_thuong_gap,
                    title = stringResource(RLocalization.string.cau_hoi_thuong_gap),
                    onClick = {
                        urlWebView = url
                    })
            }

            uiState.urls[CommonConstants.UrlType.TERMS]?.let { url ->
                SupportNavigationCard(
                    iconId = RDesignSystem.drawable.dieu_khoan_su_dung,
                    title = stringResource(RLocalization.string.dieu_khoan_dieu_kien),
                    onClick = {
                        urlWebView = url
                    })
            }

            SupportNavigationCard(
                iconId = RDesignSystem.drawable.call,
                title = operatorTextTitle,
                isShowRightIcon = false,
                onClick = {
                    callToOperator(context)
                }
            )

            SupportNavigationCard(
                iconId = RDesignSystem.drawable.lien_he,
                title = emailTextTitle,
                isShowRightIcon = false,
                onClick = {
                    sendToEmailOperator(context)
                }
            )

            InlineMessage(
                title = stringResource(RLocalization.string.thong_bao),
                message = "",
                showLeading = true,
                status = InlineMessageStatus.Info(colorScheme)
            )
        }
    }
}

@Composable
private fun SupportNavigationCard(
    iconId: Int,
    title: Any,
    isShowRightIcon: Boolean = true,
    onClick: () -> Unit
) {
    val colorScheme = LocalColorScheme.current
    val typography = LocalTypography.current

    val displayText = when (title) {
        is AnnotatedString -> title
        is String -> AnnotatedString(title)
        else -> AnnotatedString("Invalid Title")
    }

    Card(
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = colorScheme.bgMainTertiary),
        modifier = Modifier.testTagIBank("login_card_$title"),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = ripple(
                        bounded = true,
                        color = colorScheme.bgSolidPrimary_press,
                    ),
                    onClick = onClick
                )
                .heightIn(min = 56.dp)
                .padding(
                    horizontal = IBSpacing.spacingM,
                    vertical = IBSpacing.spacingXs
                ),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(IBSpacing.spacing4xl)
                    .background(
                        color = colorScheme.bgMainPrimary,
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(iconId),
                    contentDescription = null,
                    modifier = Modifier.size(IBSpacing.spacing2xl),
                    tint = Color.Unspecified,
                )
            }

            Spacer(modifier = Modifier.width(IBSpacing.spacingM))

            Box(
                modifier = Modifier.weight(1f)
            ) {

                Text(
                    text = displayText,
                    style = typography.bodyBody_l,
                    color = colorScheme.contentMainPrimary,
                )
            }

            if (isShowRightIcon) {
                Icon(
                    imageVector = ImageVector.vectorResource(RDesignSystem.drawable.mo_tab_moi),
                    contentDescription = null,
                    tint = colorScheme.contentMainPrimary,
                    modifier = Modifier
                        .testTagIBank("login_icon_mo_tab_moi")
                        .size(IBSpacing.spacingM)
                )
            }

        }
    }
}

private fun AnnotatedString.Builder.appendStyledText(
    text: String,
    textStyle: TextStyle,
    color: Color
) {
    withStyle(textStyleToSpanStyle(textStyle, color)) {
        append(text)
    }
}

private fun textStyleToSpanStyle(textStyle: TextStyle, color: Color): SpanStyle {
    return SpanStyle(
        fontSize = textStyle.fontSize,
        textDecoration = textStyle.textDecoration,
        fontFamily = textStyle.fontFamily,
        fontWeight = textStyle.fontWeight,
        fontStyle = textStyle.fontStyle,
        letterSpacing = textStyle.letterSpacing,
        color = color
    )
}

private fun callToOperator(context: Context?) {
    if (context == null) return
    try {
        val phoneNumber = CommonConstants.TEL_OPERATOR.takeIf { it.isNotBlank() } ?: return
        val intent = Intent(Intent.ACTION_DIAL).apply {
            data = Uri.parse("tel:$phoneNumber")
        }
        context.startActivity(intent)
    } catch (_: Exception) {
    }
}

private fun sendToEmailOperator(context: Context?) {
    if (context == null) return
    try {
        val email = CommonConstants.EMAIL_OPERATOR.takeIf { it.isNotBlank() } ?: return
        val intent = Intent(Intent.ACTION_SENDTO).apply {
            data = Uri.parse("mailto:$email")
        }
        context.startActivity(intent)
    } catch (_: Exception) {
    }
}


