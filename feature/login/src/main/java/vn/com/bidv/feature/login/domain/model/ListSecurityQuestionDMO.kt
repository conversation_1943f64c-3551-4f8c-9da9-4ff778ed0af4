package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

data class SecQnGetAllResponseDMO(
    /* <PERSON>h sách câu hỏi */
    @SerializedName("items")
    val items: kotlin.collections.List<CodeValueDMO>? = null,

    /* Số lượng */
    @SerializedName("total")
    val total: kotlin.Int? = null
)

@Serializable
data class CodeValueDMO(

    @SerializedName("code")
    val code: kotlin.String? = null,

    @SerializedName("value")
    val `value`: kotlin.String? = null,

    val selected: kotlin.Boolean? = false

)





