package vn.com.bidv.feature.login.ui.changepassword.model

import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.domain.model.CreateSmartOtpResDMO
import vn.com.bidv.feature.login.domain.model.ModelCreateOtpResDMO
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO

data class ModelChangPasswordUI(
    val modelLoginDMO: ModelLoginDMO? = null,
    var oldPassword: String = "",
    var newPassword: String = "",
    var confirmPassword: String = "",
    var listOldPWClientErrorCode: List<Pair<String, String>>? = null,
    var listNewPWClientErrorCode: List<Pair<String, String>>? = null,
    var modelRuleValidPassword: ModelRuleValidPassword = ModelRuleValidPassword(),
    var listConfirmPWClientErrorCode: List<Pair<String, String>>? = null,
    val modelCreateOtpResDMO: ModelCreateOtpResDMO? = null,
) {
    companion object {
        fun getDefault(): ModelChangPasswordUI {
            return ModelChangPasswordUI()
        }
    }

    fun getModelRuleValidPassword(listNewPWClientErrorCode: List<Pair<String, String>>?): ModelRuleValidPassword {
        val listErrorCode = listNewPWClientErrorCode?.map { it.second } ?: listOf()
        val modelRuleValidPassword = ModelRuleValidPassword()
        if (!listErrorCode.contains(LoginErrorCode.LESS_THAN_8_CHARACTERS)) {
            modelRuleValidPassword.isBiggerThan8 = true
        }
        if (!listErrorCode.contains(LoginErrorCode.NOT_CONTAIN_CHARACTER_LOWER)) {
            modelRuleValidPassword.isContainLowerCase = true
        }
        if (!listErrorCode.contains(LoginErrorCode.NOT_CONTAIN_CHARACTER_UPPER)) {
            modelRuleValidPassword.isContainUpperCase = true
        }
        if (!listErrorCode.contains(LoginErrorCode.NOT_CONTAIN_NUMBER)) {
            modelRuleValidPassword.isContainNumberNumber = true
        }
        if (!listErrorCode.contains(LoginErrorCode.NOT_CONTAIN_SPECIAL_CHARACTER)) {
            modelRuleValidPassword.isContainSpecialCharacter = true
        }
        if (!listErrorCode.contains(LoginErrorCode.CONTAIN_REPEATED_CHARACTER)) {
            modelRuleValidPassword.isNotContainRepeatedCharacter = true
        }
        if (!listErrorCode.contains(LoginErrorCode.OVERLAP_3_CONSECUTIVE_CHARACTERS_USER_NAME)) {
            modelRuleValidPassword.isNotOverlap3ConsecutiveUserName = true
        }
        return modelRuleValidPassword
    }
}

data class ModelRuleValidPassword(
    var isBiggerThan8: Boolean = false,
    var isContainLowerCase: Boolean = false,
    var isContainUpperCase: Boolean = false,
    var isContainNumberNumber: Boolean = false,
    var isContainSpecialCharacter: Boolean = false,
    var isNotContainRepeatedCharacter: Boolean = false,
    var isNotOverlap3ConsecutiveUserName: Boolean = false,
)
