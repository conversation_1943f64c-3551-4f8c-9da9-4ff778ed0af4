package vn.com.bidv.feature.login.ui.setupsmartotp

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.extenstion.isNotNull
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.extenstion.isNull
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.ModelRequestSmartOtp
import vn.com.bidv.feature.common.domain.data.SmartOtpDMO
import vn.com.bidv.feature.common.domain.data.UserActiveSmartOtpDMO
import vn.com.bidv.feature.common.domain.data.UserResDMO
import vn.com.bidv.feature.login.domain.SmartOtpErrorType
import vn.com.bidv.feature.login.domain.model.SmartOtpInfoDMO
import vn.com.bidv.feature.login.domain.model.SmartOtpReqActiveResDMO
import vn.com.bidv.feature.login.domain.model.TransUpdateBasicOtpDMO
import vn.com.bidv.log.BLogUtil

enum class InputType {
    INPUT,
    CONFIRM
}

class SetupSmartOtpReducer :
    Reducer<SetupSmartOtpReducer.SetupSmartOtpViewState, SetupSmartOtpReducer.SetupSmartOtpViewEvent, SetupSmartOtpReducer.SetupSmartOtpViewEffect> {

    data class SetupSmartOtpViewState(
        val inputPIN: String = "",
        val confirmPIN: String = "",
        val isInit: Boolean = false,
        val modelRequestSmartOtp: ModelRequestSmartOtp? = null,
        val userInfo: UserResDMO? = null
    ) : ViewState

    @Immutable
    sealed class SetupSmartOtpViewEvent : ViewEvent {

        data class OnInitEvent(
            val modelRequestSmartOtp: ModelRequestSmartOtp?
        ) : SetupSmartOtpViewEvent()

        data class InputPINChangedEvent(val textInput: String, val isInput: InputType) :
            SetupSmartOtpViewEvent()

        data class DeleteEvent(val isInput: InputType) : SetupSmartOtpViewEvent()

        data object ValidateInputEvent : SetupSmartOtpViewEvent()

        data object DeleteSmartOtpEvent : SetupSmartOtpViewEvent()

        data object DeleteSmartOtpSuccessEvent : SetupSmartOtpViewEvent()

        data class RequestActiveSmartOtpSuccessEvent(val data: SmartOtpReqActiveResDMO) :
            SetupSmartOtpViewEvent()

        data class RequestChangePinSuccessEvent(val data: TransUpdateBasicOtpDMO) :
            SetupSmartOtpViewEvent()

        data class RequestActiveSmartOtpFail(
            val errorCode: String = "",
            val errorMessage: String = "",
        ) : SetupSmartOtpViewEvent()

        data class UpdateUserActiveEvent(
            val userActiveSmartOtpDMO: UserActiveSmartOtpDMO
        ) : SetupSmartOtpViewEvent()

        data object UpdateUserActiveSuccessEvent : SetupSmartOtpViewEvent()

        data object ClearDataInputPinEvent : SetupSmartOtpViewEvent()

        data class OnGetSmartOtpInfoSuccess(val data: SmartOtpInfoDMO?): SetupSmartOtpViewEvent()

        data class OnGetSmartOtpInfoError(val errorCode: String?, val errorMessage: String?): SetupSmartOtpViewEvent()

        data class OnUpdateSmartOtpInfo(val smartOtpInfoDMO: SmartOtpDMO?): SetupSmartOtpViewEvent()
    }

    @Immutable
    sealed interface SetupSmartOtpViewEffect : SideEffect {

        data object ShowPopupSmartOPTWarningEffect : SetupSmartOtpViewEffect,
            UIEffect

        data object RequestActiveSmartOTPEffect : SetupSmartOtpViewEffect

        data object RequestChangePinEffect : SetupSmartOtpViewEffect

        data class GoToSmsPopupActiveSmartOtpEffect(val data: SmartOtpReqActiveResDMO) :
            SetupSmartOtpViewEffect, UIEffect

        data class ActiveSmartOtpSuccessEffect(val data: SmartOtpReqActiveResDMO) :
            SetupSmartOtpViewEffect, UIEffect

        data class RequestChangePinSuccessEffect(val data: TransUpdateBasicOtpDMO) :
            SetupSmartOtpViewEffect, UIEffect

        data class ShowPopupCommonErrorEffect(
            val errorMessage: String = "",
        ) : SetupSmartOtpViewEffect, UIEffect

        data class ShowPopupDefaultErrorEffect(
            val errorMessage: String = "",
        ) : SetupSmartOtpViewEffect, UIEffect

        data class ShowPopupActivationDifferentUserSmartOtpErrorEffect(
            val errorMessage: String = "",
        ) : SetupSmartOtpViewEffect, UIEffect

        data object DeleteSmartOtpEffect : SetupSmartOtpViewEffect

        data object DeleteSmartOtpSuccessEffect :
            SetupSmartOtpViewEffect, UIEffect

        data class UpdateUserActiveEffect(
            val userActiveSmartOtpDMO: UserActiveSmartOtpDMO,
        ) : SetupSmartOtpViewEffect

        data object UpdateUserActiveSuccessEffect : SetupSmartOtpViewEffect, UIEffect

        data class GetSmartOtpInfoEffect(val isGetSmartOtpInfo: Boolean): SetupSmartOtpViewEffect

        data object InitSuccessEffect : SetupSmartOtpViewEffect, UIEffect

        data class GetSmartOtpInfoError(val errorCode: String?, val errorMessage: String?): SetupSmartOtpViewEffect, UIEffect

    }

    override fun reduce(
        previousState: SetupSmartOtpViewState,
        event: SetupSmartOtpViewEvent,
    ): Pair<SetupSmartOtpViewState, SetupSmartOtpViewEffect?> {
        BLogUtil.d( "event: $event")
        return when (event) {
            is SetupSmartOtpViewEvent.OnInitEvent -> {
                 previousState.copy(
                    isInit = true,
                    modelRequestSmartOtp = event.modelRequestSmartOtp
                ) to SetupSmartOtpViewEffect.GetSmartOtpInfoEffect(isGetSmartOtpInfo = event.modelRequestSmartOtp?.smartOtp.isNull())
            }

            is SetupSmartOtpViewEvent.InputPINChangedEvent -> {
                val newInput =
                    if (event.isInput == InputType.INPUT)
                        previousState.inputPIN + event.textInput
                    else previousState.confirmPIN + event.textInput

                val newState = if (event.isInput == InputType.INPUT) previousState.copy(
                    inputPIN = newInput
                ) else previousState.copy(
                    confirmPIN = newInput
                )
                 newState to null
            }

            is SetupSmartOtpViewEvent.DeleteEvent -> {

                val newState = if (event.isInput == InputType.INPUT) previousState.copy(
                    inputPIN = previousState.inputPIN.dropLast(1)
                ) else previousState.copy(
                    confirmPIN = previousState.confirmPIN.dropLast(1)
                )
                 newState to null
            }

            is SetupSmartOtpViewEvent.ValidateInputEvent -> {
                val newEffect =
                    if (previousState.modelRequestSmartOtp?.userActiveSmartOtpDMO.isNotNull()) SetupSmartOtpViewEffect.RequestChangePinEffect else SetupSmartOtpViewEffect.RequestActiveSmartOTPEffect
                 previousState to newEffect
            }

            is SetupSmartOtpViewEvent.RequestActiveSmartOtpSuccessEvent -> {
                val data = event.data
                val newEffect =
                    if (data.smToken.isNotNullOrEmpty() && data.secretKey.isNotNullOrEmpty()) {
                        SetupSmartOtpViewEffect.ActiveSmartOtpSuccessEffect(
                            data.copy(
                                pinCode = previousState.inputPIN,
                            )
                        )
                    } else SetupSmartOtpViewEffect.GoToSmsPopupActiveSmartOtpEffect(
                        data.copy(
                            pinCode = previousState.inputPIN,
                        )
                    )
                 previousState to newEffect
            }

            is SetupSmartOtpViewEvent.RequestActiveSmartOtpFail -> {
                when (event.errorCode) {
                    SmartOtpErrorType.COMMON_ERROR -> {
                        previousState.copy(
                            inputPIN = "",
                            confirmPIN = ""
                        ) to SetupSmartOtpViewEffect.ShowPopupCommonErrorEffect(
                            event.errorMessage
                        )
                    }

                    SmartOtpErrorType.ACTIVATION_DIFFERENT_USER_SMART_OTP -> {
                        previousState to SetupSmartOtpViewEffect.ShowPopupActivationDifferentUserSmartOtpErrorEffect(
                            event.errorMessage
                        )
                    }

                    else -> {
                        previousState.copy(
                            inputPIN = "",
                            confirmPIN = ""
                        ) to SetupSmartOtpViewEffect.ShowPopupDefaultErrorEffect(
                            event.errorMessage
                        )
                    }

                }
            }

            is SetupSmartOtpViewEvent.DeleteSmartOtpEvent -> {
                 previousState to SetupSmartOtpViewEffect.DeleteSmartOtpEffect
            }

            is SetupSmartOtpViewEvent.DeleteSmartOtpSuccessEvent -> {
                 previousState to SetupSmartOtpViewEffect.DeleteSmartOtpSuccessEffect
            }

            is SetupSmartOtpViewEvent.RequestChangePinSuccessEvent -> {
                 previousState to SetupSmartOtpViewEffect.RequestChangePinSuccessEffect(
                    event.data
                )
            }

            is SetupSmartOtpViewEvent.UpdateUserActiveEvent -> {
                 previousState to SetupSmartOtpViewEffect.UpdateUserActiveEffect(
                    event.userActiveSmartOtpDMO
                )
            }

            is SetupSmartOtpViewEvent.UpdateUserActiveSuccessEvent -> {
                 previousState to SetupSmartOtpViewEffect.UpdateUserActiveSuccessEffect
            }

            is SetupSmartOtpViewEvent.ClearDataInputPinEvent -> {
                 previousState.copy(
                    inputPIN = "",
                    confirmPIN = ""
                ) to null
            }

            is SetupSmartOtpViewEvent.OnGetSmartOtpInfoSuccess -> {
                previousState.copy(
                    modelRequestSmartOtp = previousState.modelRequestSmartOtp?.copy(
                        smartOtp = event.data?.smartOtp ?: previousState.modelRequestSmartOtp.smartOtp
                    ),
                    userInfo = event.data?.user,
                ) to SetupSmartOtpViewEffect.InitSuccessEffect
            }

            is SetupSmartOtpViewEvent.OnGetSmartOtpInfoError -> {
                previousState to SetupSmartOtpViewEffect.GetSmartOtpInfoError(
                    event.errorCode, event.errorMessage ?: ""
                )
            }

            is SetupSmartOtpViewEvent.OnUpdateSmartOtpInfo -> {
                previousState.copy(
                    modelRequestSmartOtp = previousState.modelRequestSmartOtp?.copy(
                        smartOtp = event.smartOtpInfoDMO
                    )
                ) to null
            }
        }
    }
}
