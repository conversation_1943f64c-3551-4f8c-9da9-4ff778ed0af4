package vn.com.bidv.feature.login.ui.changepassword

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import com.google.gson.Gson
import vn.com.bidv.designsystem.component.dataentry.IBFrameState
import vn.com.bidv.designsystem.component.dataentry.IBankInputPassword
import vn.com.bidv.designsystem.component.dataentry.RemoveSpaceFilter
import vn.com.bidv.designsystem.component.dataentry.RemoveSpecialCharacterPasswordFilter
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessage
import vn.com.bidv.designsystem.component.feedback.inlinemessage.InlineMessageStatus
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.navigation.topappbar.IBankActionBar
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.IBTypography.NormalTypography.bodyBody_m
import vn.com.bidv.designsystem.theme.IBTypography.NormalTypography.titleTitle_s
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionTypeConstant
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.constants.LoginErrorCode
import vn.com.bidv.feature.login.domain.model.ModelCreateOtpResDMO
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.localization.R
import vn.com.bidv.sdkbase.data.ReloadFunctionKey
import vn.com.bidv.sdkbase.data.ReloadKey
import vn.com.bidv.sdkbase.data.ReloadModuleKey
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.utils.VietnameseAccentRemoverFilter
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.feature.common.navigation.NavigationHelper as CommonNavigationHelper
import vn.com.bidv.localization.R as RLocalization
import vn.com.bidv.sdkbase.navigation.NavigationHelper as SdkBaseNavigationHelper

@Composable
fun ChangePasswordScreen(navController: NavHostController, modelLoginDMO: ModelLoginDMO?) {
    val changePasswordViewModel: ChangePasswordViewModel = hiltViewModel()

    val messageSuccess = remember { mutableStateOf("") }
    val messageDefaultError = remember { mutableStateOf("") }
    val messageChangePwError = remember { mutableStateOf("") }
    val messageVerifyError = remember { mutableStateOf("") }
    val isPositiveChangePw = modelLoginDMO?.status == Constants.PW_CHANGE

    BaseScreen(
        navController = navController,
        backgroundColor = LocalColorScheme.current.bgMainTertiary,
        viewModel = changePasswordViewModel,
        renderContent = { uiState, onEvent ->
            LaunchedEffect(true) {
                onEvent(ChangePasswordReducer.ChangePasswordViewEvent.InitData(modelLoginDMO))
            }
            ChangePasswordContent(
                navController = navController,
                uiState = uiState,
                onEvent = onEvent
            )

            LaunchedEffect(true) {
                changePasswordViewModel.subscribeReloadData(ReloadKey(ReloadModuleKey.LOGIN, ReloadFunctionKey.POSITIVE_CHANGE_PW)).collect {
                    if (changePasswordViewModel.checkReloadData(it)) {
                        onEvent(ChangePasswordReducer.ChangePasswordViewEvent.OnTurnOffBiometric)
                    }
                }
            }

            if (messageVerifyError.value.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = messageVerifyError.value,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.thu_lai),
                            onClick = {
                                onEvent(ChangePasswordReducer.ChangePasswordViewEvent.Continue)
                            }
                        )
                    ),
                    onDismissRequest = {
                        messageVerifyError.value = ""
                    }
                )
            }

            if (messageSuccess.value.isNotEmpty()) {
                IBankModalConfirm(
                    title = stringResource(R.string.doi_mat_khau_thanh_cong),
                    isShowIconClose = modelLoginDMO?.status?.contains(Constants.PW_CHANGE, true) == true,
                    modalConfirmType = ModalConfirmType.Success,
                    supportingText = messageSuccess.value,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.dang_nhap_lai),
                        )
                    ),
                    onDismissRequest = {
                        onEvent(ChangePasswordReducer.ChangePasswordViewEvent.OnTurnOffBiometric)
                        messageSuccess.value = ""
                        SdkBaseNavigationHelper.navigationToLogin(navController)
                    }
                )
            }

            if (messageDefaultError.value.isNotEmpty()) {
                IBankModalConfirm(
                    title = if (isPositiveChangePw) stringResource(R.string.doi_mat_khau_khong_thanh_cong)
                    else stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = messageDefaultError.value,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.close),
                        )
                    ),
                    onDismissRequest = {
                        messageDefaultError.value = ""
                    }
                )
            }

            if (messageChangePwError.value.isNotEmpty()) {
                IBankModalConfirm(
                    title = if (isPositiveChangePw) stringResource(R.string.doi_mat_khau_khong_thanh_cong)
                    else stringResource(R.string.loi),
                    modalConfirmType = ModalConfirmType.Error,
                    supportingText = messageChangePwError.value,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.close),
                        )
                    ),
                    onDismissRequest = {
                        messageChangePwError.value = ""
                        if (isPositiveChangePw) {
                            navController.popBackStack(
                                IBankMainRouting.AuthRoutes.PositiveChangePwRoute.route,
                                inclusive = true
                            )
                        } else {
                            SdkBaseNavigationHelper.navigationToLogin(navController)
                        }
                    }
                )
            }
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is ChangePasswordReducer.ChangePasswordViewEffect.CreateSmsOtp -> {
                    navigateToSmsOTP(
                        navController,
                        sideEffect.modelLoginDMO,
                        sideEffect.modelCreateOtpResDMO
                    )
                }

                is ChangePasswordReducer.ChangePasswordViewEffect.PositiveCreateSmsOtp -> {
                    navigateToPositiveChangePwSmsOTP(
                        navController,
                        sideEffect.modelLoginDMO,
                        sideEffect.modelCreateOtpResDMO
                    )
                }

                is ChangePasswordReducer.ChangePasswordViewEffect.PositiveCreateSmartOtp -> {
                    CommonNavigationHelper.navigateToVerifyByTypeCreateTransaction(
                        navController = navController,
                        dataString = Gson().toJson(sideEffect.modelChangPasswordUI),
                        type = VerifyTransactionTypeConstant.ChangePassword,
                    )
                }

                is ChangePasswordReducer.ChangePasswordViewEffect.VerifyChangePwSuccess -> {
                    messageSuccess.value =
                        navController.context.getString(R.string.quy_khach_vui_long_dang_nhap_lai_de_tiep_tuc_su_dung_dich_vu)
                }

                is ChangePasswordReducer.ChangePasswordViewEffect.ShowDefaultErrorVerifyChangePwFail -> {
                    messageDefaultError.value = sideEffect.message
                }

                is ChangePasswordReducer.ChangePasswordViewEffect.ShowSmartOtpErrorVerifyChangePwFail -> {
                    messageChangePwError.value = sideEffect.message
                }

                is ChangePasswordReducer.ChangePasswordViewEffect.PerformChangePassword,
                is ChangePasswordReducer.ChangePasswordViewEffect.ValidConfirmPassword,
                is ChangePasswordReducer.ChangePasswordViewEffect.ValidNewPassword,
                is ChangePasswordReducer.ChangePasswordViewEffect.ValidNewPasswordRealTime,
                is ChangePasswordReducer.ChangePasswordViewEffect.ValidOldPassword,
                is ChangePasswordReducer.ChangePasswordViewEffect.ValidClientInputData,
                is ChangePasswordReducer.ChangePasswordViewEffect.TurnOffBiometric -> TODO()
            }
        },
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(
            titleTopAppBar = stringResource(id = RLocalization.string.doi_mat_khau),
            showHomeIcon = isPositiveChangePw,
            actionItems = {}
        )
    )
}

private fun navigateToSmsOTP(
    navController: NavHostController,
    modelLoginDMO: ModelLoginDMO?,
    modelCreateOtpResDMO: ModelCreateOtpResDMO?
) {
    NavigationHelper.navigateToChangePWSmsOTP(navController, modelLoginDMO, modelCreateOtpResDMO)
}

private fun navigateToPositiveChangePwSmsOTP(
    navController: NavHostController,
    modelLoginDMO: ModelLoginDMO?,
    modelCreateOtpResDMO: ModelCreateOtpResDMO?
) {
    NavigationHelper.navigateToPositiveChangePWSmsOTP(
        navController,
        modelLoginDMO,
        modelCreateOtpResDMO
    )
}

@Composable
private fun ChangePasswordContent(
    navController: NavHostController,
    uiState: ChangePasswordReducer.ResultState,
    onEvent: (ChangePasswordReducer.ChangePasswordViewEvent) -> Unit,
) {
    val focusManager: FocusManager = LocalFocusManager.current

    val lstFilterPassword = listOf(
        VietnameseAccentRemoverFilter(),
        RemoveSpaceFilter(),
        RemoveSpecialCharacterPasswordFilter()
    )

    val oldPasswordState = getPasswordState(
        uiState.modelChangPasswordUI.listOldPWClientErrorCode,
        Constants.ViewID.OLD_PASSWORD_VIEW_ID
    )
    val oldPasswordMessage = getMessageError(
        uiState.modelChangPasswordUI.listOldPWClientErrorCode,
        Constants.ViewID.OLD_PASSWORD_VIEW_ID
    )

    val newPasswordState = getPasswordState(
        uiState.modelChangPasswordUI.listNewPWClientErrorCode,
        Constants.ViewID.NEW_PASSWORD_VIEW_ID
    )
    val newPasswordMessage = getMessageError(
        uiState.modelChangPasswordUI.listNewPWClientErrorCode,
        Constants.ViewID.NEW_PASSWORD_VIEW_ID
    )

    val confirmPasswordState = getPasswordState(
        uiState.modelChangPasswordUI.listConfirmPWClientErrorCode,
        Constants.ViewID.CONFIRM_PASSWORD_VIEW_ID
    )
    val confirmPasswordMessage = getMessageError(
        uiState.modelChangPasswordUI.listConfirmPWClientErrorCode,
        Constants.ViewID.CONFIRM_PASSWORD_VIEW_ID
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .padding(start = IBSpacing.spacingM, end = IBSpacing.spacingM)
                .fillMaxWidth()
                .weight(1f)
                .verticalScroll(rememberScrollState())
        ) {
            InlineMessage(
                message = stringResource(RLocalization.string.vi_ly_do_bao_mat_quy_khach_vui_long_dat_mat_khau_moi_khong_trung_voi_3_mat_khau_gan_nhat),
                showLeading = true,
                status = InlineMessageStatus.Warning(LocalColorScheme.current)
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacingS))
            IBankInputPassword(
                maxLengthText = 20,
                placeholderText = stringResource(RLocalization.string.mat_khau_hien_tai),
                filters = lstFilterPassword,
                text = uiState.modelChangPasswordUI.oldPassword,
                state = oldPasswordState,
                helpTextLeft = oldPasswordMessage,
                onFocusChange = {
                    if (!it) {
                        onEvent(ChangePasswordReducer.ChangePasswordViewEvent.ValidOldPassword)
                    }
                },
                onSubmitText = {
                    focusManager.clearFocus()
                },
                onClickClear = {
                    onEvent(ChangePasswordReducer.ChangePasswordViewEvent.OldPasswordChanged(""))
                },
                onValueChange = { textFieldValue ->
                    onEvent(
                        ChangePasswordReducer.ChangePasswordViewEvent.OldPasswordChanged(
                            textFieldValue.text
                        )
                    )
                },
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacingM))
            IBankInputPassword(
                maxLengthText = 20,
                placeholderText = stringResource(RLocalization.string.mat_khau_moi),
                filters = lstFilterPassword,
                text = uiState.modelChangPasswordUI.newPassword,
                state = newPasswordState,
                helpTextLeft = newPasswordMessage,
                onFocusChange = {
                    if (!it) {
                        onEvent(ChangePasswordReducer.ChangePasswordViewEvent.ValidNewPassword)
                    }
                },
                onSubmitText = {
                    focusManager.clearFocus()
                },
                onClickClear = {
                    onEvent(ChangePasswordReducer.ChangePasswordViewEvent.NewPasswordChanged(""))
                },
                onValueChange = {
                    onEvent(ChangePasswordReducer.ChangePasswordViewEvent.NewPasswordChanged(it.text))
                },
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacingM))
            IBankInputPassword(
                maxLengthText = 20,
                placeholderText = stringResource(RLocalization.string.xac_minh_mat_khau_moi),
                filters = lstFilterPassword,
                state = confirmPasswordState,
                helpTextLeft = confirmPasswordMessage,
                text = uiState.modelChangPasswordUI.confirmPassword,
                onFocusChange = {
                    if (!it) {
                        onEvent(ChangePasswordReducer.ChangePasswordViewEvent.ValidConfirmPassword)
                    }
                }, onSubmitText = {
                    focusManager.clearFocus()
                },
                onClickClear = {
                    onEvent(ChangePasswordReducer.ChangePasswordViewEvent.ConfirmPasswordChanged(""))
                },
                onValueChange = {
                    onEvent(ChangePasswordReducer.ChangePasswordViewEvent.ConfirmPasswordChanged(it.text))
                })
            Spacer(modifier = Modifier.height(IBSpacing.spacingXs))
            Text(
                modifier = Modifier.fillMaxWidth(),
                text = stringResource(RLocalization.string.yeu_cau_dat_mat_khau_moi),
                style = titleTitle_s,
                color = LocalColorScheme.current.contentMainTertiary
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
            PasswordLineCheck(
                content = stringResource(RLocalization.string.toi_thieu_08_ky_tu),
                isCheckSuccess = uiState.modelChangPasswordUI.modelRuleValidPassword.isBiggerThan8
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
            PasswordLineCheck(
                content = stringResource(RLocalization.string.it_nhat_1_chu_viet_thuong_az),
                isCheckSuccess = uiState.modelChangPasswordUI.modelRuleValidPassword.isContainLowerCase
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
            PasswordLineCheck(
                content = stringResource(RLocalization.string.it_nhat_1_chu_viet_hoa_az),
                isCheckSuccess = uiState.modelChangPasswordUI.modelRuleValidPassword.isContainUpperCase
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
            PasswordLineCheck(
                content = stringResource(RLocalization.string.it_nhat_1_ky_tu_so_mot_so_09),
                isCheckSuccess = uiState.modelChangPasswordUI.modelRuleValidPassword.isContainNumberNumber
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
            PasswordLineCheck(
                content = stringResource(RLocalization.string.it_nhat_1_ky_tu_dac_biet),
                isCheckSuccess = uiState.modelChangPasswordUI.modelRuleValidPassword.isContainSpecialCharacter
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
            PasswordLineCheck(
                content = stringResource(RLocalization.string.khong_chua_ky_tu_lap_hoac_lien_tiep_123111aaa),
                isCheckSuccess = uiState.modelChangPasswordUI.modelRuleValidPassword.isNotContainRepeatedCharacter
            )
            Spacer(modifier = Modifier.height(IBSpacing.spacing2xs))
            PasswordLineCheck(
                content = stringResource(RLocalization.string.khong_trung_3_ky_tu_lien_tiep_o_ten_dang_nhap),
                isCheckSuccess = uiState.modelChangPasswordUI.modelRuleValidPassword.isNotOverlap3ConsecutiveUserName
            )
        }
        val isPositiveChangePw =
            uiState.modelChangPasswordUI.modelLoginDMO?.status == Constants.PW_CHANGE
        IBankActionBar(modifier = Modifier
            .fillMaxWidth()
            .background(Color.White),
            isVertical = false,
            buttonNegative = if (!isPositiveChangePw
            ) DialogButtonInfo(
                label = stringResource(RLocalization.string.thoat)
            ) {
                SdkBaseNavigationHelper.navigationToLogin(navController)
            } else null,
            buttonPositive = DialogButtonInfo(
                label = stringResource(RLocalization.string.tiep_tuc)
            ) {
                onEvent(ChangePasswordReducer.ChangePasswordViewEvent.Continue)
            }
        )
    }
}

@Composable
private fun getMessageError(
    listPWClientErrorCode: List<Pair<String, String>>?,
    viewIdError: String
): String {
    return when (viewIdError) {
        Constants.ViewID.OLD_PASSWORD_VIEW_ID -> {
            when (listPWClientErrorCode?.firstOrNull { it.first == viewIdError }?.second) {
                LoginErrorCode.PASSWORD_EMPTY -> stringResource(RLocalization.string.vui_long_nhap_thong_tin)
                LoginErrorCode.LESS_THAN_8_CHARACTERS -> stringResource(RLocalization.string.mat_khau_toi_thieu_gom_8_ky_tu)
                else -> ""
            }
        }

        Constants.ViewID.NEW_PASSWORD_VIEW_ID -> {
            if ((listPWClientErrorCode?.map { it.second == viewIdError }?.size ?: 0) > 0) {
                stringResource(RLocalization.string.mat_khau_chua_dap_ung_yeu_cau)
            } else {
                ""
            }
        }

        Constants.ViewID.CONFIRM_PASSWORD_VIEW_ID -> {
            when (listPWClientErrorCode?.firstOrNull { it.first == viewIdError }?.second) {
                LoginErrorCode.PASSWORD_EMPTY -> stringResource(RLocalization.string.vui_long_nhap_thong_tin)
                LoginErrorCode.CONFIRM_PASS_NOT_MATCH_NEW_PASS -> stringResource(RLocalization.string.khong_trung_mat_khau_moi)
                else -> ""
            }
        }

        else -> ""
    }
}

@Composable
private fun getPasswordState(
    listPWClientErrorCode: List<Pair<String, String>>?,
    viewIdError: String
): IBFrameState {
    return if (listPWClientErrorCode?.any { it.first == viewIdError } == true) {
        IBFrameState.ERROR(LocalColorScheme.current)
    } else {
        IBFrameState.DEFAULT(LocalColorScheme.current)
    }
}

@Composable
private fun PasswordLineCheck(
    content: String,
    isCheckSuccess: Boolean
) {
    val iconId = if (isCheckSuccess) {
        RDesignSystem.drawable.check
    } else {
        RDesignSystem.drawable.bullet_point
    }
    val contentColor = if (isCheckSuccess) {
        LocalColorScheme.current.bgPositivePrimary
    } else {
        LocalColorScheme.current.contentMainTertiary
    }
    Row(modifier = Modifier.fillMaxWidth()) {
        Icon(
            painter = painterResource(iconId),
            contentDescription = null,
            tint = if (isCheckSuccess) LocalColorScheme.current.bgPositivePrimary else Color.Unspecified,
            modifier = Modifier.size(IBSpacing.spacingL)
        )
        Spacer(modifier = Modifier.width(IBSpacing.spacingXs))
        Text(
            modifier = Modifier.testTagIBank("login_passwordLineCheck_$content").weight(1f),
            text = content,
            color = contentColor,
            style = bodyBody_m
        )
    }
}

@Preview
@Composable
fun test() {
    ChangePasswordContent(
        navController = NavHostController(LocalContext.current),
        uiState = ChangePasswordReducer.ResultState(),
        onEvent = {}
    )
}
