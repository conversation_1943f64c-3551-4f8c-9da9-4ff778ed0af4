package vn.com.bidv.feature.login.ui.userinfoverifyresultscreen

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import vn.com.bidv.common.patterns.mvi.BaseMviViewModel
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.UserInfoSettingUseCase
import javax.inject.Inject

@HiltViewModel
class UserInfoVerifyResultViewModel @Inject constructor(
    private val userInfoSettingUseCase: UserInfoSettingUseCase,
) : BaseMviViewModel<UserInfoVerifyResultReducer.UserInfoVerifyResultViewState, UserInfoVerifyResultReducer.UserInfoVerifyResultViewEvent, UserInfoVerifyResultReducer.UserInfoVerifyResultViewEffect>(
    initialState = UserInfoVerifyResultReducer.UserInfoVerifyResultViewState(),
    reducer = UserInfoVerifyResultReducer()
) {
    override fun handleEffect(
        sideEffect: UserInfoVerifyResultReducer.UserInfoVerifyResultViewEffect,
        onResult: (UserInfoVerifyResultReducer.UserInfoVerifyResultViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is UserInfoVerifyResultReducer.UserInfoVerifyResultViewEffect.ShareDataVerify -> {
                viewModelScope.launch {
                    userInfoSettingUseCase.shareUserInfoSuccessAction(
                        action = Constants.USER_INFO_SUCCESS,
                        data = sideEffect.dataVerify
                    )
                }
                onResult(
                    UserInfoVerifyResultReducer.UserInfoVerifyResultViewEvent.OnShareDataVerified
                )
            }

            else -> {
                // do nothing
            }
        }
    }
}

