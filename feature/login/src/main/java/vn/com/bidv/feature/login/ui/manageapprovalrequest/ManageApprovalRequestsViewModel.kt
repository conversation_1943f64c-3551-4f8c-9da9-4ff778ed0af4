package vn.com.bidv.feature.login.ui.manageapprovalrequest

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.feature.common.domain.data.TransAuthDMO
import vn.com.bidv.feature.login.domain.ManageApprovalRequestsUseCase
import vn.com.bidv.feature.login.domain.model.AdminTransVerifyDMO
import vn.com.bidv.feature.login.ui.manageapprovalrequest.ManageApprovalRequestsReducer.ManageApprovalRequestsViewEffect
import vn.com.bidv.feature.login.ui.manageapprovalrequest.ManageApprovalRequestsReducer.ManageApprovalRequestsViewEvent
import vn.com.bidv.feature.login.ui.manageapprovalrequest.ManageApprovalRequestsReducer.ManageApprovalRequestsViewState
import vn.com.bidv.sdkbase.ui.ViewModelIBankBase
import javax.inject.Inject

@HiltViewModel
class ManageApprovalRequestsViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val manageApprovalRequestsUseCase: ManageApprovalRequestsUseCase,
) : ViewModelIBankBase<ManageApprovalRequestsViewState, ManageApprovalRequestsViewEvent, ManageApprovalRequestsViewEffect>(
    initialState = ManageApprovalRequestsViewState(),
    reducer = ManageApprovalRequestsReducer()
) {
    override fun handleEffect(
        sideEffect: ManageApprovalRequestsViewEffect,
        onResult: (ManageApprovalRequestsViewEvent) -> Unit
    ) {
        when (sideEffect) {
            is ManageApprovalRequestsViewEffect.RejectTransaction -> {
                callDomain (
                    onSuccess = {
                        onResult(
                            ManageApprovalRequestsViewEvent.OnRejectTransactionSuccess(
                                it.data ?: AdminTransVerifyDMO()
                            )
                        )
                    }
                ){
                    manageApprovalRequestsUseCase.performRejectRequest(
                        sideEffect.ids,
                        sideEffect.actionType,
                        sideEffect.reason
                    )
                }
            }
            is ManageApprovalRequestsViewEffect.RejectTransactionSuccess,
            is ManageApprovalRequestsViewEffect.ShowConfirmPopup,
            is ManageApprovalRequestsViewEffect.ShowDetailBottomSheet -> {
                // Nothing
            }
        }
    }
}
