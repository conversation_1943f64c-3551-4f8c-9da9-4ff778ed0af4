package vn.com.bidv.feature.login.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoSet
import retrofit2.Retrofit
import vn.com.bidv.feature.common.data.utilities.apis.UtilitiesApi
import vn.com.bidv.feature.common.di.AuthProvider
import vn.com.bidv.feature.common.domain.verifyFlowUseCase.VerifyByTypeTransactionUseCaseDefault
import vn.com.bidv.feature.common.ui.screen.verifyByTypeTransactionFlow.model.VerifyTransactionFlowScreenBuilder
import vn.com.bidv.feature.login.data.BiometricRepository
import vn.com.bidv.feature.login.data.LoginRepository
import vn.com.bidv.feature.login.data.PasswordRepository
import vn.com.bidv.feature.login.data.SmartOTPRepository
import vn.com.bidv.feature.login.data.UserInfoSettingRepository
import vn.com.bidv.feature.login.data.VerifySecureOtpRepository
import vn.com.bidv.feature.login.data.login.apis.BiometricApi
import vn.com.bidv.feature.login.data.login.apis.InfoApi
import vn.com.bidv.feature.login.data.login.apis.LoginApi
import vn.com.bidv.feature.login.data.login.apis.OTPApi
import vn.com.bidv.feature.login.data.login.apis.OthersApi
import vn.com.bidv.feature.login.data.login.apis.PasswordApi
import vn.com.bidv.feature.login.domain.BiometricVerifyUseCase
import vn.com.bidv.feature.login.domain.EditQuestionsVerifyUseCase
import vn.com.bidv.feature.login.domain.LoginManager
import vn.com.bidv.feature.login.domain.ManageApprovalRequestsVerifyUseCase
import vn.com.bidv.feature.login.domain.ManageQuestionsVerifyUseCase
import vn.com.bidv.feature.login.domain.PositiveChangePwVerifyUseCase
import vn.com.bidv.feature.login.domain.UserInfoVerifyUseCase
import vn.com.bidv.feature.login.navigation.BiometricVerifyFlowScreenBuilder
import vn.com.bidv.feature.login.navigation.ChangePinVerifyFlowScreenBuilder
import vn.com.bidv.feature.login.navigation.EditQuestionsVerifyFlowScreenBuilder
import vn.com.bidv.feature.login.navigation.LoginNavigation
import vn.com.bidv.feature.login.navigation.LoginNotificationRouteBuilder
import vn.com.bidv.feature.login.navigation.ManageApprovalFlowScreenBuilder
import vn.com.bidv.feature.login.navigation.ManageQuestionsVerifyFlowScreenBuilder
import vn.com.bidv.feature.login.navigation.PositiveChangePwVerifyFlowScreenBuilder
import vn.com.bidv.feature.login.navigation.UserInfoVerifyFlowScreenBuilder
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder
import vn.com.bidv.sdkbase.navigation.notifyroute.NotificationRouteBuilder
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class LoginModule {

    @Singleton
    @Provides
    fun provideLoginService(retrofit: Retrofit): LoginApi {
        return retrofit.create(LoginApi::class.java)
    }

    @Provides
    @Singleton
    fun provideLoginRepository(
        service: LoginApi,
    ): LoginRepository = LoginRepository(service)

    @Singleton
    @Provides
    @IntoSet
    fun provideLoginFeatureGraphBuilder(): FeatureGraphBuilder {
        return LoginNavigation()
    }

    @Singleton
    @Provides
    fun providePasswordService(retrofit: Retrofit): PasswordApi {
        return retrofit.create(PasswordApi::class.java)
    }

    @Singleton
    @Provides
    fun provideVerifyOtpService(retrofit: Retrofit): OTPApi {
        return retrofit.create(OTPApi::class.java)
    }

    @Provides
    @Singleton
    fun provideSmartOTPRepository(
        service: UtilitiesApi,
        infoApi: InfoApi
    ): SmartOTPRepository = SmartOTPRepository(service, infoApi)

    @Provides
    @Singleton
    fun providePasswordRepository(
        service: PasswordApi,
    ): PasswordRepository = PasswordRepository(service)

    @Provides
    @Singleton
    fun provideVerifyOtpRepository(
        service: OTPApi,
    ): VerifySecureOtpRepository = VerifySecureOtpRepository(service)

    @Singleton
    @Provides
    fun provideBiometricApi(retrofit: Retrofit): BiometricApi {
        return retrofit.create(BiometricApi::class.java)
    }

    @Provides
    @Singleton
    fun provideBiometricRepository(
        biometricApi: BiometricApi,
        otpApi: OTPApi
    ): BiometricRepository = BiometricRepository(biometricApi, otpApi)

    @Provides
    @Singleton
    fun provideAuthProvider(
        loginRepository: LoginRepository,
    ): AuthProvider = LoginManager(loginRepository)

    @Singleton
    @Provides
    fun provideUserInfoSettingService(retrofit: Retrofit): OthersApi {
        return retrofit.create(OthersApi::class.java)
    }

    @Provides
    @Singleton
    fun provideUserInfoSettingRepository(
        othersApi: OthersApi,
        otpApi: OTPApi
    ): UserInfoSettingRepository = UserInfoSettingRepository(othersApi, otpApi)

    @Provides
    @Singleton
    fun provideInfoApi(retrofit: Retrofit): InfoApi {
        return retrofit.create(InfoApi::class.java)
    }

    @Provides
    @Singleton
    @IntoSet
    fun provideLoginNotificationRouteBuilder(): NotificationRouteBuilder {
        return LoginNotificationRouteBuilder()
    }

    @Provides
    @Singleton
    @IntoSet
    fun provideManageApprovalFlowScreenBuilder(
        useCase: ManageApprovalRequestsVerifyUseCase,
    ): VerifyTransactionFlowScreenBuilder {
        return ManageApprovalFlowScreenBuilder(useCase)
    }

    @Provides
    @Singleton
    @IntoSet
    fun provideBiometricVerifyFlowScreenBuilder(
        biometricVerifyUseCase: BiometricVerifyUseCase,
    ): VerifyTransactionFlowScreenBuilder {
        return BiometricVerifyFlowScreenBuilder(biometricVerifyUseCase)
    }

    @Provides
    @Singleton
    @IntoSet
    fun provideUserInfoVerifyFlowScreenBuilder(
        userInfoVerifyUseCase: UserInfoVerifyUseCase,
    ): VerifyTransactionFlowScreenBuilder {
        return UserInfoVerifyFlowScreenBuilder(userInfoVerifyUseCase)
    }

    @Provides
    @Singleton
    @IntoSet
    fun provideManageQuestionsVerifyFlowScreenBuilder(
        manageQuestionsVerifyUseCase: ManageQuestionsVerifyUseCase,
    ): VerifyTransactionFlowScreenBuilder {
        return ManageQuestionsVerifyFlowScreenBuilder(manageQuestionsVerifyUseCase)
    }

    @Provides
    @Singleton
    @IntoSet
    fun provideEditQuestionsVerifyFlowScreenBuilder(
        editQuestionsVerifyUseCase: EditQuestionsVerifyUseCase,
        @ApplicationContext context: Context,
    ): VerifyTransactionFlowScreenBuilder {
        return EditQuestionsVerifyFlowScreenBuilder(editQuestionsVerifyUseCase,context)
    }

    @Provides
    @Singleton
    @IntoSet
    fun providePositiveChangePwVerifyFlowScreenBuilder(
        positiveChangePwVerifyUseCase: PositiveChangePwVerifyUseCase,
    ): VerifyTransactionFlowScreenBuilder {
        return PositiveChangePwVerifyFlowScreenBuilder(positiveChangePwVerifyUseCase)
    }

    @Provides
    @Singleton
    @IntoSet
    fun provideChangePinVerifyFlowScreenBuilder(
        changePinVerifyUseCase: VerifyByTypeTransactionUseCaseDefault,
    ): VerifyTransactionFlowScreenBuilder {
        return ChangePinVerifyFlowScreenBuilder(changePinVerifyUseCase)
    }
}