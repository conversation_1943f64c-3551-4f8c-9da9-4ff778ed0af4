package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable
import vn.com.bidv.feature.common.domain.data.UserResDMO

@Serializable
data class SmartOtpReqActiveResDMO(
    /* Resend Otp time */
    @SerializedName("resendOtp")
    val resendOtp: Long? = null,

    /* count time */
    @SerializedName("countTime")
    val countTime: Long? = null,

    /* text view onscreen user */
    @SerializedName("content")
    val contentText: String? = null,

    /* Secret key */
    @SerializedName("secretKey")
    val secretKey: String? = null,

    /* smartotp token */
    @SerializedName("smToken")
    val smToken: String? = null,

    @SerializedName("pinCode")
    val pinCode: String? = null,

    @SerializedName("userId")
    val userId: String? = null,

    @SerializedName("cifName")
    val cifName: String? = null,

    @SerializedName("username")
    val username: String? = null,
) : IIBankModelOTPDMO {

    override fun getRetryTimeSendOTP(): String? {
        return "${resendOtp}"
    }

    override fun getActiveTimeOTP(): String? {
        return "${countTime}"
    }

    override fun getContent(): String? {
        return contentText
    }

}
