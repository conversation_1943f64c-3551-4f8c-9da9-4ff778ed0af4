package vn.com.bidv.feature.login.ui.requestpositivechangepw

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.login.constants.Constants
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO

class RequestChangePwReducer :
    Reducer<RequestChangePwReducer.RequestChangePwViewState, RequestChangePwReducer.RequestChangePwViewEvent, RequestChangePwReducer.RequestChangePwViewEffect> {

    data object RequestChangePwViewState : ViewState

    @Immutable
    sealed class RequestChangePwViewEvent : ViewEvent {
        data object InitData : RequestChangePwViewEvent()
        data class GetWrongPwAttemptsSuccessEvent(val userName: String?, val userId: String?) :
            RequestChangePwViewEvent()

        data class GetWrongPwAttemptsFailEvent(val message: String) : RequestChangePwViewEvent()
    }

    @Immutable
    sealed class RequestChangePwViewEffect : SideEffect {
        data object InitEffect : RequestChangePwViewEffect()
        data class GetWrongPwAttemptsSuccessEffect(val data: ModelLoginDMO?) :
            RequestChangePwViewEffect(), UIEffect

        data class GetWrongPwAttemptsFailEffect(val message: String) : RequestChangePwViewEffect(),
            UIEffect
    }

    override fun reduce(
        previousState: RequestChangePwViewState,
        event: RequestChangePwViewEvent,
    ): Pair<RequestChangePwViewState, RequestChangePwViewEffect?> {
        return when (event) {
            is RequestChangePwViewEvent.InitData -> {
                previousState to RequestChangePwViewEffect.InitEffect
            }

            is RequestChangePwViewEvent.GetWrongPwAttemptsSuccessEvent -> {
                previousState to RequestChangePwViewEffect.GetWrongPwAttemptsSuccessEffect(
                    data = ModelLoginDMO(
                        status = Constants.PW_CHANGE,
                        username = event.userName,
                        userId = event.userId
                    )
                )
            }

            is RequestChangePwViewEvent.GetWrongPwAttemptsFailEvent -> {
                previousState to RequestChangePwViewEffect.GetWrongPwAttemptsFailEffect(message = event.message)
            }
        }
    }
}
