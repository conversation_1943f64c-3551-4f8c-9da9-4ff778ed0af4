package vn.com.bidv.feature.login.ui.userinfo

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.UserResDMO
import vn.com.bidv.feature.login.domain.model.CompositeOtpResDMO
import vn.com.bidv.feature.login.domain.model.ModelCreateOtpResDMO
import vn.com.bidv.feature.login.domain.model.ModelLoginDMO
import vn.com.bidv.feature.login.domain.model.UserPersonalInfoDMO
import vn.com.bidv.feature.login.domain.model.UserInfoSettingsDMO
import vn.com.bidv.feature.login.ui.userinfo.modelui.UserInfoModelUI

class UserInfoReducer :
    Reducer<UserInfoReducer.UserInfoViewState, UserInfoReducer.UserInfoViewEvent, UserInfoReducer.UserInfoViewEffect> {

    @Immutable
    data class UserInfoViewState(
        val isInitSuccess: Boolean = false,
        val userInfoModelUI: UserInfoModelUI = UserInfoModelUI.getDefault()
    ) : ViewState

    @Immutable
    sealed class UserInfoViewEvent : ViewEvent {
        data object OnInitData : UserInfoViewEvent()
        data class OnGetUserInfoSettingSuccess(val userInfoSettingsDMO: UserInfoSettingsDMO?) :
            UserInfoViewEvent()

        data class OnGetUserInfoSettingFail(val error: String?) : UserInfoViewEvent()
        data object OnGetUserInfoSecure : UserInfoViewEvent()
        data class OnGetUserInfoSecureSuccess(
            val compositeOtpResDMO: CompositeOtpResDMO?,
            val userInfo: UserResDMO?
        ) :
            UserInfoViewEvent()

        data class OnApprovalTransaction(val transId: String, val otp: String) :
            UserInfoViewEvent()

        data class OnApprovalTransactionSuccess(val userPersonalInfoDMO: UserPersonalInfoDMO?) :
            UserInfoViewEvent()

        data object OnPositivePerformStrongAuth : UserInfoViewEvent()

    }

    @Immutable
    sealed class UserInfoViewEffect : SideEffect {
        data object GetUserInfoSettings : UserInfoViewEffect()
        data class GetUserInfoSettingFail(val error: String?) : UserInfoViewEffect(), UIEffect
        data object GetUserInfoSecure : UserInfoViewEffect()
        data class PositivePerformOtpAuth(
            val modelLoginDMO: ModelLoginDMO?,
            val modelCreateOtpResDMO: ModelCreateOtpResDMO?
        ) :
            UserInfoViewEffect(),
            UIEffect

        data object PositivePerformStrongOTPAuth : UserInfoViewEffect(), UIEffect

        data class ApprovalTransaction(val transId: String, val otp: String) : UserInfoViewEffect()
    }

    override fun reduce(
        previousState: UserInfoViewState,
        event: UserInfoViewEvent,
    ): Pair<UserInfoViewState, UserInfoViewEffect?> {
        return when (event) {
            is UserInfoViewEvent.OnInitData -> {
                previousState to UserInfoViewEffect.GetUserInfoSettings
            }

            is UserInfoViewEvent.OnGetUserInfoSettingSuccess -> {
                previousState.copy(
                    isInitSuccess = true,
                    userInfoModelUI = previousState.userInfoModelUI.copy(
                        userInfoSettingsDMO = event.userInfoSettingsDMO
                    )
                ) to null
            }

            is UserInfoViewEvent.OnGetUserInfoSettingFail -> {
                previousState.copy(
                    isInitSuccess = true,
                ) to UserInfoViewEffect.GetUserInfoSettingFail(event.error)
            }

            is UserInfoViewEvent.OnGetUserInfoSecure -> {
                previousState to UserInfoViewEffect.GetUserInfoSecure
            }

            is UserInfoViewEvent.OnGetUserInfoSecureSuccess -> {
                val compositeOtpResDMO = event.compositeOtpResDMO
                val userInfo = event.userInfo
                val modelCreateOtpResDMO = ModelCreateOtpResDMO(
                    resendOtpTime = compositeOtpResDMO?.basicOtp?.resendOtpTime,
                    otpActiveCount = compositeOtpResDMO?.basicOtp?.otpActiveCount,
                    maskedPhoneOrEmail = compositeOtpResDMO?.basicOtp?.maskedPhoneOrEmail,
                    contentText = compositeOtpResDMO?.basicOtp?.contentText,
                    trustedDevice = compositeOtpResDMO?.basicOtp?.trustedDevice,
                    txnId = compositeOtpResDMO?.transId
                )
                val modelLoginDMO = ModelLoginDMO(
                    userId = userInfo?.userId.toString(),
                    username = userInfo?.username,
                )
                previousState to UserInfoViewEffect.PositivePerformOtpAuth(
                    modelLoginDMO,
                    modelCreateOtpResDMO
                )
            }

            is UserInfoViewEvent.OnPositivePerformStrongAuth -> {
                previousState to UserInfoViewEffect.PositivePerformStrongOTPAuth
            }

            is UserInfoViewEvent.OnApprovalTransaction -> {
                previousState to UserInfoViewEffect.ApprovalTransaction(
                    event.transId,
                    event.otp
                )
            }

            is UserInfoViewEvent.OnApprovalTransactionSuccess -> {
                previousState.copy(
                    userInfoModelUI = previousState.userInfoModelUI.copy(
                        userInfoSecureDMO = event.userPersonalInfoDMO
                    )
                ) to null
            }
        }
    }
}
