package vn.com.bidv.feature.login.ui.convertsmartotp

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.login.domain.SmartOtpErrorType

class ConvertSmartOtpReducer :
    Reducer<ConvertSmartOtpReducer.ConvertSmartOtpViewState, ConvertSmartOtpReducer.ConvertSmartOtpViewEvent, ConvertSmartOtpReducer.ConvertSmartOtpViewEffect> {

    data class ConvertSmartOtpViewState(
        val inputPIN: String = "",
        val isInit: Boolean = false,
        val isAdminRole: Boolean = false,
    ) : ViewState

    @Immutable
    sealed class ConvertSmartOtpViewEvent : ViewEvent {
        data object OnInitEvent : ConvertSmartOtpViewEvent()

        data class InputPINChangedEvent(val textInput: String) :
            ConvertSmartOtpViewEvent()

        data object DeleteEvent : ConvertSmartOtpViewEvent()

        data object ClearDataInputPinEvent : ConvertSmartOtpViewEvent()

        data class CheckUserAdminRoleEvent(val isAdminRole: Boolean) : ConvertSmartOtpViewEvent()

        data class SubmitEvent(val otp: String) : ConvertSmartOtpViewEvent()

        data object ConvertSuccessEvent : ConvertSmartOtpViewEvent()

        data class ConvertFailEvent(
            val errorCode: String = "",
            val errorMessage: String = "",
        ) : ConvertSmartOtpViewEvent()

    }

    @Immutable
    sealed interface ConvertSmartOtpViewEffect : SideEffect {
        data object InitEffect : ConvertSmartOtpViewEffect

        data class SubmitEffect(val otp: String) : ConvertSmartOtpViewEffect

        data object ConvertSuccessEffect : ConvertSmartOtpViewEffect, UIEffect

        data class ConvertFailCommonErrorEffect(val errorMessage: String) :
            ConvertSmartOtpViewEffect, UIEffect

        data class ConvertFailLockedErrorEffect(val errorMessage: String) :
            ConvertSmartOtpViewEffect, UIEffect

        data class ConvertFailDefaultErrorEffect(val errorMessage: String) :
            ConvertSmartOtpViewEffect, UIEffect
    }

    override fun reduce(
        previousState: ConvertSmartOtpViewState,
        event: ConvertSmartOtpViewEvent,
    ): Pair<ConvertSmartOtpViewState, ConvertSmartOtpViewEffect?> {
        return when (event) {
            is ConvertSmartOtpViewEvent.OnInitEvent -> {
                previousState.copy(
                    isInit = true
                ) to ConvertSmartOtpViewEffect.InitEffect
            }

            is ConvertSmartOtpViewEvent.InputPINChangedEvent -> {
                val newInput = previousState.inputPIN + event.textInput
                previousState.copy(
                    inputPIN = newInput
                ) to null
            }

            is ConvertSmartOtpViewEvent.DeleteEvent -> {
                previousState.copy(
                    inputPIN = previousState.inputPIN.dropLast(1)
                ) to null
            }

            is ConvertSmartOtpViewEvent.ClearDataInputPinEvent -> {
                previousState.copy(
                    inputPIN = ""
                ) to null
            }

            is ConvertSmartOtpViewEvent.CheckUserAdminRoleEvent -> {
                previousState.copy(
                    isAdminRole = event.isAdminRole
                ) to null
            }

            is ConvertSmartOtpViewEvent.SubmitEvent -> {
                previousState to ConvertSmartOtpViewEffect.SubmitEffect(otp = event.otp)
            }

            is ConvertSmartOtpViewEvent.ConvertFailEvent -> {
                when (event.errorCode) {
                    SmartOtpErrorType.COMMON_ERROR -> {
                        previousState to ConvertSmartOtpViewEffect.ConvertFailCommonErrorEffect(
                            errorMessage = event.errorMessage
                        )
                    }

                    SmartOtpErrorType.SMART_OTP_HAS_BEEN_LOCKED -> {
                        previousState to ConvertSmartOtpViewEffect.ConvertFailLockedErrorEffect(
                            errorMessage = event.errorMessage
                        )
                    }

                    else -> {
                        previousState to ConvertSmartOtpViewEffect.ConvertFailDefaultErrorEffect(
                            errorMessage = event.errorMessage
                        )
                    }
                }
            }

            is ConvertSmartOtpViewEvent.ConvertSuccessEvent -> {
                previousState to ConvertSmartOtpViewEffect.ConvertSuccessEffect
            }
        }
    }
}
