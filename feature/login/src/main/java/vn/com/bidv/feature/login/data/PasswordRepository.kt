package vn.com.bidv.feature.login.data

import vn.com.bidv.feature.login.data.login.apis.PasswordApi
import vn.com.bidv.feature.login.data.login.model.CreateForgotPwRequest
import vn.com.bidv.feature.login.data.login.model.CreateForgotPwResponseData
import vn.com.bidv.feature.login.data.login.model.CreatePositiveChangePwRequest
import vn.com.bidv.feature.login.data.login.model.CreatePositiveChangePwResDto
import vn.com.bidv.feature.login.data.login.model.SecQnForgotPwRequest
import vn.com.bidv.feature.login.data.login.model.SecQnForgotPwResponseData
import vn.com.bidv.feature.login.data.login.model.SecurityQuestionAnswerDto
import vn.com.bidv.network.NetworkResult
import vn.com.bidv.network.domain.BaseRepository
import javax.inject.Inject

class PasswordRepository @Inject constructor(
    private val service: PasswordApi

) : BaseRepository() {

    suspend fun getWrongPwAttempts() = launch {
        service.getWrongPwAttempts()
    }

    suspend fun positiveChangePw(
        oldPassword: String,
        newPassword: String,
        confirmPassword: String
    ): NetworkResult<CreatePositiveChangePwResDto> = launch {
        service.pwPositiveChangeCreate(
            CreatePositiveChangePwRequest(
                currentPassword = oldPassword,
                newPassword = newPassword,
                confirmNewPassword = confirmPassword,
            )
        )
    }

    suspend fun forgotPwCreate(
        username: String,
        deviceId: String,
        idExpireDate: String,
        idNum: String,
        idReg: String,
        mobile: String?,
        otpEmail: String?,
    ): NetworkResult<CreateForgotPwResponseData> = launch {
        service.pwForgotCreate(
            CreateForgotPwRequest(
                username = username,
                deviceId = deviceId,
                idExpireDate = idExpireDate,
                idNum = idNum,
                idReg = idReg,
                mobile = mobile,
                otpEmail = otpEmail,
            )
        )
    }

    suspend fun forgotPwQuestion(
        username: String,
        deviceId: String,
        transId: String,
        securityQuestions: List<SecurityQuestionAnswerDto>,
    ): NetworkResult<SecQnForgotPwResponseData> = launch {
        service.pwForgotSecQn(
            SecQnForgotPwRequest(
                username = username,
                deviceId = deviceId,
                transId = transId,
                securityQuestions = securityQuestions,
            )
        )
    }

}