package vn.com.bidv.feature.login.ui.approvalrequestsresult

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.common.patterns.mvi.BaseMviViewModel
import vn.com.bidv.feature.login.ui.approvalrequestsresult.ApprovalRequestsResultReducer.ApprovalRequestsResultViewEffect
import vn.com.bidv.feature.login.ui.approvalrequestsresult.ApprovalRequestsResultReducer.ApprovalRequestsResultViewEvent
import vn.com.bidv.feature.login.ui.approvalrequestsresult.ApprovalRequestsResultReducer.ApprovalRequestsResultViewState
import javax.inject.Inject

@HiltViewModel
class ApprovalRequestsResultViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
) : BaseMviViewModel<ApprovalRequestsResultViewState, ApprovalRequestsResultViewEvent, ApprovalRequestsResultViewEffect>(
    initialState = ApprovalRequestsResultViewState(),
    reducer = ApprovalRequestsResultReducer()
) {
    override fun handleEffect(
        sideEffect: ApprovalRequestsResultViewEffect,
        onResult: (ApprovalRequestsResultViewEvent) -> Unit
    ) {
        // TODO: Controller logic, do not handle logic code here.
        /*
		 when (sideEffect) {
		    // Continue event
            is ApprovalRequestsResultViewEffect.FetchListData -> {
                // Controller logic via Use Case
                fetchData(sideEffect.searchText) { nextEvent ->
                    onResult(nextEvent)
                }
            }

            else -> {}
        }
		*/
    }
}
