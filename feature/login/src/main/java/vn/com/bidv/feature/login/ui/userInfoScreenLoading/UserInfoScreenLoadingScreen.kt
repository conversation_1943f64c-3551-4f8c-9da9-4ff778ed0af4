package vn.com.bidv.feature.login.ui.userInfoScreenLoading

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.common.ui.BaseMVIScreen
import vn.com.bidv.feature.login.ui.userInfoScreenLoading.UserInfoScreenLoadingReducer.UserInfoScreenLoadingViewEffect
import vn.com.bidv.feature.login.ui.userInfoScreenLoading.UserInfoScreenLoadingReducer.UserInfoScreenLoadingViewEvent
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.localization.R

@Composable
fun UserInfoScreenLoadingScreen(navController: NavHostController) {
    val viewModel: UserInfoScreenLoadingViewModel = hiltViewModel()
    var isShowPopUpError by remember { mutableStateOf(false) }
    var messageError by remember { mutableStateOf("") }
    BaseMVIScreen(
        viewModel = viewModel,
        renderContent = { uiState, onEvent ->
            if (!uiState.isInitializer) {
                onEvent(UserInfoScreenLoadingViewEvent.OnGetUserInfo)
            }

            if (isShowPopUpError) {
                IBankModalConfirm(modalConfirmType = ModalConfirmType.Error,
                    title = stringResource(R.string.loi),
                    supportingText = messageError,
                    isShowIconClose = false,
                    listDialogButtonInfo = listOf(
                        DialogButtonInfo(
                            label = stringResource(R.string.dang_nhap_lai),
                            onClick = {
                                onEvent(UserInfoScreenLoadingViewEvent.OnLogout)
                            },
                            isDismissRequest = false
                        ),
                    ),
                    onDismissRequest = {
                        isShowPopUpError = false
                    })
            }
        },
        handleSideEffect = { sideEffect ->
            when (sideEffect) {
                is UserInfoScreenLoadingViewEffect.OnGetUserInfoFailSideEffect -> {
                    isShowPopUpError = true
                    messageError = sideEffect.errorMessage
                }
                is UserInfoScreenLoadingViewEffect.OnGetUserInfoSuccessSideEffect -> {
                    if (sideEffect.getUserInfoResponseDMO != null) {
                        NavigationHelper.navigateToHomeScreen(navController)
                    } else {
                        isShowPopUpError = true
                        messageError = navController.context.getString(R.string.co_loi_xay_ra_quy_khach_vui_long_thu_lai)
                    }
                }
                is UserInfoScreenLoadingViewEffect.OnLogoutSuccessSideEffect -> {
                    vn.com.bidv.sdkbase.navigation.NavigationHelper.navigationToLogin(navController)
                }
                else -> {
                    //Do nothing
                }
            }
        }
    )
}
