package vn.com.bidv.feature.login.domain.model

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

@Serializable
data class TransUpdateResDMO(
    /* SMARTOTP|CA */
    @SerializedName("authType")
    val authType: kotlin.String? = null,

    /* Y|N */
    @SerializedName("isSameDevice")
    val isSameDevice: kotlin.String? = null,

    /* auth Id */
    @SerializedName("authId")
    val authId: kotlin.String? = null,

    /* transKey */
    @SerializedName("transKey")
    val transKey: kotlin.String? = null,

    /* qr code */
    @SerializedName("qrCode")
    val qrCode: kotlin.String? = null,

    /* exp time seconds */
    @SerializedName("expTime")
    val expTime: kotlin.String? = null
) {
    fun isSameDevice(): Boolean {
        return isSameDevice == "Y"
    }
}
