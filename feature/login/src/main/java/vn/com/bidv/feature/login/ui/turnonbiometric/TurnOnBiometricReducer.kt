package vn.com.bidv.feature.login.ui.turnonbiometric

import androidx.biometric.BiometricPrompt.AuthenticationResult
import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState
import vn.com.bidv.feature.common.domain.data.UserResDMO
import vn.com.bidv.feature.login.domain.model.BiometricOnRequestDMO
import vn.com.bidv.feature.login.domain.model.CompositeOtpResDMO

class TurnOnBiometricReducer :
    Reducer<TurnOnBiometricReducer.TurnOnBiometricViewState, TurnOnBiometricReducer.TurnOnBiometricViewEvent, TurnOnBiometricReducer.TurnOnBiometricViewEffect> {

    @Immutable
    data class TurnOnBiometricViewState(
        val isInitSuccess: Boolean = false,
        val isTurnOnBiometric: Boolean = false,
        val userInfo: UserResDMO = UserResDMO(),
        val compositeOtpResDMO: CompositeOtpResDMO = CompositeOtpResDMO()
    ) : ViewState

    @Immutable
    sealed class TurnOnBiometricViewEvent : ViewEvent {
        data class OnInitTurnOnBiometric(val isTurnOnBiometric: Boolean) : TurnOnBiometricViewEvent()
        data class OnInitTurnOnBiometricSuccess(val userInfo: UserResDMO) :
            TurnOnBiometricViewEvent()

        data object OnCheckBiometricAvailable : TurnOnBiometricViewEvent()
        data class OnBiometricNotAvailable(val errorCode: String?) : TurnOnBiometricViewEvent()
        data object OnGetAuthenticationResult : TurnOnBiometricViewEvent()
        data object OnStartTurnOnBiometricWithVerifySMS : TurnOnBiometricViewEvent()
        data class OnVerifyOtpBiometric(val data: CompositeOtpResDMO) : TurnOnBiometricViewEvent()
        data class OnStartTurnOnBiometricError(val errorMessage: String?) :
            TurnOnBiometricViewEvent()
        data class OnVerifyOtpBiometricSuccess(val authenticationResult: AuthenticationResult?) :
            TurnOnBiometricViewEvent()
        data object OnTurnOnBiometricSuccess : TurnOnBiometricViewEvent()
        data object OnTurnOffBiometric : TurnOnBiometricViewEvent()
        data object OnTurnOffBiometricSuccess : TurnOnBiometricViewEvent()
        data class OnTurnOffBiometricError(val errorMessage: String?) : TurnOnBiometricViewEvent()
        data object OnCreateBiometricOnRequest: TurnOnBiometricViewEvent()
        data class OnCreateBiometricOnRequestSuccess(val biometricOnRequestDMO: BiometricOnRequestDMO): TurnOnBiometricViewEvent()

    }

    @Immutable
    sealed class TurnOnBiometricViewEffect(val errMessage: String? = "") : SideEffect {
        data object InitTurnOnBiometric : TurnOnBiometricViewEffect()
        data object CheckBiometricAvailable : TurnOnBiometricViewEffect()
        data class BiometricNotAvailable(val errorCode: String?) : TurnOnBiometricViewEffect(),
            UIEffect

        data object GetAuthenticationResult : TurnOnBiometricViewEffect(), UIEffect
        data object StartTurnOnBiometricWithVerifySMS : TurnOnBiometricViewEffect()
        data class StartTurnOnBiometricError(val errorMessage: String?) :
            TurnOnBiometricViewEffect(errorMessage), UIEffect

        data class VerifyOtpBiometric(val userInfo: UserResDMO, val data: CompositeOtpResDMO) :
            TurnOnBiometricViewEffect(), UIEffect

        data class VerifyOtpBiometricSuccess(val authenticationResult: AuthenticationResult?) :
            TurnOnBiometricViewEffect()

        data object TurnOffBiometric : TurnOnBiometricViewEffect()

        data object TurnOnBiometricSuccess : TurnOnBiometricViewEffect(), UIEffect
        data object TurnOffBiometricSuccess : TurnOnBiometricViewEffect(), UIEffect
        data class TurnOffBiometricError(val errorMessage: String?) : TurnOnBiometricViewEffect(errorMessage), UIEffect
        data object CreateBiometricOnRequest: TurnOnBiometricViewEffect()
        data class CreateBiometricOnRequestSuccess(val biometricOnRequestDMO: BiometricOnRequestDMO): TurnOnBiometricViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: TurnOnBiometricViewState,
        event: TurnOnBiometricViewEvent,
    ): Pair<TurnOnBiometricViewState, TurnOnBiometricViewEffect?> {
        return when (event) {

            is TurnOnBiometricViewEvent.OnInitTurnOnBiometric -> {
                previousState.copy(isTurnOnBiometric = event.isTurnOnBiometric) to TurnOnBiometricViewEffect.InitTurnOnBiometric
            }

            is TurnOnBiometricViewEvent.OnInitTurnOnBiometricSuccess -> {
                previousState.copy(
                    isInitSuccess = true,
                    userInfo = event.userInfo
                ) to null
            }

            is TurnOnBiometricViewEvent.OnCheckBiometricAvailable -> {
                previousState to TurnOnBiometricViewEffect.CheckBiometricAvailable
            }

            is TurnOnBiometricViewEvent.OnBiometricNotAvailable -> {
                previousState to TurnOnBiometricViewEffect.BiometricNotAvailable(event.errorCode)
            }

            is TurnOnBiometricViewEvent.OnGetAuthenticationResult -> {
                previousState to TurnOnBiometricViewEffect.GetAuthenticationResult
            }

            is TurnOnBiometricViewEvent.OnStartTurnOnBiometricWithVerifySMS -> {
                previousState to TurnOnBiometricViewEffect.StartTurnOnBiometricWithVerifySMS
            }

            is TurnOnBiometricViewEvent.OnStartTurnOnBiometricError -> {
                previousState to TurnOnBiometricViewEffect.StartTurnOnBiometricError(event.errorMessage)
            }

            is TurnOnBiometricViewEvent.OnVerifyOtpBiometric -> {
                previousState.copy(
                    compositeOtpResDMO = event.data
                ) to TurnOnBiometricViewEffect.VerifyOtpBiometric(
                    previousState.userInfo,
                    event.data
                )
            }

            is TurnOnBiometricViewEvent.OnVerifyOtpBiometricSuccess -> {
                previousState to TurnOnBiometricViewEffect.VerifyOtpBiometricSuccess(event.authenticationResult)
            }

            is TurnOnBiometricViewEvent.OnTurnOnBiometricSuccess -> {
                previousState.copy(
                    compositeOtpResDMO = CompositeOtpResDMO()
                ) to TurnOnBiometricViewEffect.TurnOnBiometricSuccess
            }

            is TurnOnBiometricViewEvent.OnTurnOffBiometricSuccess -> {
                previousState to TurnOnBiometricViewEffect.TurnOffBiometricSuccess
            }

            is TurnOnBiometricViewEvent.OnTurnOffBiometricError -> {
                previousState to TurnOnBiometricViewEffect.TurnOffBiometricError(event.errorMessage)
            }

            is TurnOnBiometricViewEvent.OnTurnOffBiometric -> {
                previousState.copy(isInitSuccess = true) to TurnOnBiometricViewEffect.TurnOffBiometric
            }

            is TurnOnBiometricViewEvent.OnCreateBiometricOnRequest -> {
                previousState to TurnOnBiometricViewEffect.CreateBiometricOnRequest
            }

            is TurnOnBiometricViewEvent.OnCreateBiometricOnRequestSuccess -> {
                previousState to TurnOnBiometricViewEffect.CreateBiometricOnRequestSuccess(event.biometricOnRequestDMO)
            }

            else -> {
                previousState to null
            }
        }
    }
}
