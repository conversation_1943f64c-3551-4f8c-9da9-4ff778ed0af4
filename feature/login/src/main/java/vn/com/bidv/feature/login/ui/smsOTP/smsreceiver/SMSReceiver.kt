package vn.com.bidv.feature.login.ui.smsOTP.smsreceiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.android.gms.common.api.Status
import vn.com.bidv.log.BLogUtil

class SMSReceiver : BroadcastReceiver() {

    var smsReceiverListener: SMSReceiverListener? = null

    override fun onReceive(context: Context?, intent: Intent?) {
        BLogUtil.d("thuanhm ${intent?.action}")
        if (SmsRetriever.SMS_RETRIEVED_ACTION == intent?.action) {
            try {
                val extract = intent.extras
                val smsReceiverStatus = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                    @Suppress("DEPRECATION")
                    extract?.get(SmsRetriever.EXTRA_STATUS) as? Status
                } else {
                    extract?.getParcelable(SmsRetriever.EXTRA_STATUS, Status::class.java)
                }
                if (smsReceiverStatus?.statusCode == CommonStatusCodes.SUCCESS) {
                    val smsInIntent = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                        @Suppress("DEPRECATION")
                        extract?.getParcelable(SmsRetriever.EXTRA_CONSENT_INTENT)
                    } else {
                        extract?.getParcelable(SmsRetriever.EXTRA_CONSENT_INTENT, Intent::class.java)
                    }
                    smsReceiverListener?.onReceiveMessage(smsInIntent)
                }
            } catch (ex: Exception) {
                BLogUtil.logException(ex)
            }
        }
    }
}

interface SMSReceiverListener {
    fun onReceiveMessage(smsInIntent: Intent?)
}