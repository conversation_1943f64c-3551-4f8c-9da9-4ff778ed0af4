package vn.com.bidv.feature.login.ui.manageusersmartotp

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import vn.com.bidv.designsystem.component.IBankEmptyState
import vn.com.bidv.feature.login.ui.manageusersmartotp.ManageUserSmartOtpReducer.ManageUserSmartOtpViewEvent
import vn.com.bidv.feature.login.ui.manageusersmartotp.ManageUserSmartOtpReducer.ManageUserSmartOtpViewState
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarConfig
import vn.com.bidv.designsystem.component.navigation.topappbar.TopAppBarType
import vn.com.bidv.designsystem.component.util.testTagIBank
import vn.com.bidv.designsystem.theme.IBSpacing
import vn.com.bidv.designsystem.theme.LocalColorScheme
import vn.com.bidv.designsystem.theme.LocalTypography
import vn.com.bidv.designsystem.ui.BaseScreen
import vn.com.bidv.feature.common.domain.data.UserSmartOtpDMO
import vn.com.bidv.feature.login.navigation.NavigationHelper
import vn.com.bidv.designsystem.R as RDesignSystem
import vn.com.bidv.localization.R as RLocalization
import vn.com.bidv.sdkbase.navigation.NavigationHelper as SdkBaseNavigationHelper

@Composable
fun ManageUserSmartOtpScreen(navController: NavHostController) {
    val vm: ManageUserSmartOtpViewModel = hiltViewModel()
    BaseScreen(
        navController = navController,
        viewModel = vm,
        renderContent = { uiState, onEvent ->
            if (!uiState.isInitSuccess) {
                onEvent(ManageUserSmartOtpViewEvent.OnInitScreen)
            } else {
                ManageUserSmartOtpContent(uiState, onEvent, navController, vm)
            }
        },
        handleSideEffect = { sideEffect ->

        },
        topAppBarType = TopAppBarType.Title,
        topAppBarConfig = TopAppBarConfig(titleTopAppBar = stringResource(RLocalization.string.quan_ly_nguoi_dung_smart_otp), onHomeClick = {
            SdkBaseNavigationHelper.navigationToLogin(navController)
        })
    )
}

@Composable
private fun ManageUserSmartOtpContent(
    uiState: ManageUserSmartOtpViewState,
    onEvent: (ManageUserSmartOtpViewEvent) -> Unit,
    navController: NavHostController,
    vm: ManageUserSmartOtpViewModel
) {
    var isShowConfirmDeleteItem by remember { mutableStateOf(false) }
    var itemSelected by remember { mutableStateOf<UserSmartOtpDMO?>(null) }

    if (isShowConfirmDeleteItem) {
        val supportingText = buildAnnotatedString {
            append(stringResource(RLocalization.string.ban_co_chac_chan_xoa_nguoi_dung_s, " "))
            withStyle(style = LocalTypography.current.labelLabel_l.toSpanStyle()) {
                append(itemSelected?.userName ?: "")
            }
        }
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Question,
            title = stringResource(RLocalization.string.xac_nhan_xoa),
            supportingText = "",
            annotatedSupportingText = supportingText,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(RLocalization.string.yes),
                    onClick = {
                        itemSelected?.let {
                            onEvent(ManageUserSmartOtpViewEvent.OnDeleteUserSmartOtp(it))
                        }
                    }
                ),
                DialogButtonInfo(
                    label = stringResource(RLocalization.string.huy),
                )
            ),
            onDismissRequest = {
                isShowConfirmDeleteItem = false
            }
        )
    }

    if (uiState.listUserSmartOtpDMO.isEmpty()) {
        IBankEmptyState(
            modifier = Modifier.fillMaxSize(),
            supportingText = stringResource(RLocalization.string.khong_co_du_lieu)
        )
    } else {
        LazyColumn(
            modifier = Modifier.padding(horizontal = IBSpacing.spacingM),
            verticalArrangement = Arrangement.spacedBy(IBSpacing.spacingS)
        ) {
            items(uiState.listUserSmartOtpDMO.size) { index ->
                UserSmartOtpItem(uiState.listUserSmartOtpDMO[index]) {
                    isShowConfirmDeleteItem = true
                    itemSelected = it
                }
            }
        }
    }
}

@Composable
fun UserSmartOtpItem(item: UserSmartOtpDMO, onItemClicked: (UserSmartOtpDMO) -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(IBSpacing.spacingXs))
            .background(LocalColorScheme.current.bgMainTertiary)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = IBSpacing.spacingS, horizontal = IBSpacing.spacingM),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = item.userName,
                modifier = Modifier.testTagIBank("login_text_userName").weight(1f),
                style = LocalTypography.current.titleTitle_s,
                color = LocalColorScheme.current.contentMainPrimary
            )
            Icon(
                painter = painterResource(RDesignSystem.drawable.trash_outline),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier
                    .testTagIBank("login_icon_trash_outline")
                    .size(IBSpacing.spacing2xl)
                    .clickable {
                        onItemClicked(item)
                    }
            )
        }

        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(1.dp)
                .background(LocalColorScheme.current.borderMainSecondary)
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = IBSpacing.spacingS, horizontal = IBSpacing.spacingM)
        ) {
            Text(
                modifier = Modifier
                    .fillMaxWidth(),
                text = stringResource(RLocalization.string.ten_cong_ty),
                style = LocalTypography.current.bodyBody_m,
                color = LocalColorScheme.current.contentMainTertiary
            )
            Text(
                modifier = Modifier
                    .testTagIBank("login_text_cifName")
                    .fillMaxWidth(),
                text = item.cifName,
                style = LocalTypography.current.titleTitle_s,
                color = LocalColorScheme.current.contentMainPrimary
            )
        }

    }
}
