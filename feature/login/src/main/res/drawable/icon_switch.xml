<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="18dp"
    android:height="18dp"
    android:viewportWidth="18"
    android:viewportHeight="18">
  <path
      android:pathData="M5.876,4.405C5.876,3.319 6.756,2.439 7.841,2.439C8.926,2.439 9.806,3.319 9.806,4.405C9.806,5.49 8.926,6.37 7.841,6.37C6.756,6.37 5.876,5.49 5.876,4.405ZM7.841,1.502C6.238,1.502 4.938,2.801 4.938,4.405C4.938,6.008 6.238,7.307 7.841,7.307C9.444,7.307 10.744,6.008 10.744,4.405C10.744,2.801 9.444,1.502 7.841,1.502ZM7.642,8.975C8.387,8.939 9.128,9.093 9.797,9.422C10.029,9.537 10.31,9.441 10.424,9.209C10.539,8.976 10.443,8.695 10.211,8.581C9.4,8.182 8.501,7.995 7.598,8.038C6.695,8.081 5.818,8.352 5.048,8.825C4.279,9.299 3.642,9.96 3.197,10.747C2.752,11.533 2.513,12.42 2.504,13.324V14.951C2.504,15.21 2.714,15.42 2.973,15.42L6.218,15.42C6.477,15.42 6.687,15.21 6.687,14.951C6.687,14.692 6.477,14.482 6.218,14.482L3.441,14.482L3.441,13.331C3.45,12.587 3.646,11.856 4.013,11.208C4.38,10.56 4.905,10.014 5.54,9.624C6.174,9.233 6.898,9.01 7.642,8.975Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <group>
    <clip-path
        android:pathData="M8.502,9.5h6.5v6.5h-6.5z"/>
    <path
        android:pathData="M11.753,10.047H11.753C12.31,10.048 12.854,10.222 13.309,10.545C13.764,10.868 14.108,11.323 14.294,11.849C14.343,11.987 14.27,12.139 14.132,12.188C13.993,12.237 13.842,12.164 13.793,12.026C13.644,11.604 13.367,11.238 13.002,10.978C12.636,10.719 12.2,10.58 11.752,10.578C11.302,10.578 10.834,10.772 10.432,11.085C10.19,11.274 9.98,11.5 9.818,11.741L10.063,11.68C10.205,11.644 10.349,11.731 10.385,11.873C10.42,12.015 10.334,12.16 10.191,12.195L9.382,12.398L11.753,10.047ZM11.753,10.047H11.752M11.753,10.047H11.752M11.752,10.047C11.154,10.047 10.574,10.3 10.105,10.667M11.752,10.047L10.105,10.667M10.105,10.667C9.835,10.877 9.596,11.13 9.404,11.405M10.105,10.667L9.404,11.405M9.404,11.405L9.369,11.264C9.333,11.121 9.189,11.035 9.047,11.07C8.905,11.106 8.818,11.25 8.854,11.392L9.056,12.202L9.056,12.202L9.117,12.187C9.118,12.193 9.12,12.199 9.122,12.204L9.062,12.224C9.074,12.26 9.093,12.292 9.117,12.318M9.404,11.405L9.207,12.384C9.207,12.384 9.207,12.384 9.207,12.384C9.172,12.368 9.141,12.345 9.117,12.318M9.117,12.318C9.117,12.318 9.117,12.318 9.117,12.318L9.163,12.276L9.117,12.318ZM14.38,13.175C14.41,13.205 14.433,13.244 14.445,13.287C14.445,13.287 14.445,13.287 14.445,13.287L14.448,13.297L14.448,13.297L14.448,13.297L14.65,14.108C14.686,14.25 14.599,14.394 14.457,14.43C14.315,14.465 14.17,14.379 14.135,14.236L14.105,14.117C13.917,14.399 13.681,14.649 13.407,14.855C12.931,15.212 12.36,15.42 11.767,15.453C11.762,15.453 11.757,15.453 11.752,15.453C11.197,15.453 10.655,15.283 10.201,14.964C9.746,14.646 9.401,14.195 9.211,13.674C9.161,13.536 9.232,13.383 9.37,13.333C9.508,13.283 9.66,13.354 9.71,13.492C9.863,13.911 10.14,14.273 10.506,14.529C10.869,14.783 11.301,14.92 11.745,14.922C12.231,14.893 12.698,14.722 13.088,14.43C13.336,14.244 13.545,14.014 13.707,13.754L13.441,13.82C13.299,13.856 13.155,13.769 13.119,13.627C13.084,13.485 13.17,13.34 13.313,13.305L14.122,13.102C14.147,13.096 14.174,13.093 14.2,13.094L14.38,13.175ZM14.38,13.175C14.38,13.175 14.38,13.175 14.38,13.175L14.335,13.218L14.38,13.175C14.38,13.175 14.38,13.175 14.38,13.175Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.125"
        android:fillColor="#ffffff"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
</vector>
