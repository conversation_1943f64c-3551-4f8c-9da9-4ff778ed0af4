:: Generates a complete module consisting of the following directories: data, di, domain, navigation, and ui

@echo off
set /p modulename="Enter the module name: "

:: Define the package structure based on the modulename
set package=vn\com\bidv\feature\%modulename%

:: Create the module directory and move into it
mkdir %modulename%
cd %modulename%

:: Create folder structure for the module
mkdir src\main\java\%package%\data
mkdir src\main\java\%package%\di
mkdir src\main\java\%package%\domain
mkdir src\main\java\%package%\navigation
mkdir src\main\java\%package%\ui
mkdir src\androidTest\java\%package%
mkdir src\test\java\%package%
if not exist "src\main" ( mkdir src\main )

:: Create a simple AndroidManifest.xml file
echo ^<?xml version="1.0" encoding="utf-8"?^> > src\main\AndroidManifest.xml
echo ^<manifest xmlns:android="http://schemas.android.com/apk/res/android"^> >> src\main\AndroidManifest.xml
echo ^</manifest^> >> src\main\AndroidManifest.xml

:: Create the build.gradle.kts file
echo @Suppress("DSL_SCOPE_VIOLATION") > build.gradle.kts
echo plugins { >> build.gradle.kts
echo     alias(libs.plugins.local.ibank.feature) >> build.gradle.kts
echo     alias(libs.plugins.local.android.openapi.generate) >> build.gradle.kts
echo } >> build.gradle.kts
echo. >> build.gradle.kts
echo android { >> build.gradle.kts
echo     namespace = "vn.com.bidv.feature.%modulename%" >> build.gradle.kts
echo } >> build.gradle.kts
echo. >> build.gradle.kts
echo dependencies { >> build.gradle.kts
echo     implementation(libs.gson) >> build.gradle.kts
echo     implementation(libs.swagger.annotations) >> build.gradle.kts
echo     implementation(libs.bundles.retrofit) >> build.gradle.kts
echo     testImplementation(libs.junit4) >> build.gradle.kts
echo     testImplementation(libs.mockito.core) >> build.gradle.kts
echo     testImplementation(libs.mockito.kotlin) >> build.gradle.kts
echo     implementation(project(":core:public:designsystem")) >> build.gradle.kts
echo } >> build.gradle.kts

:: Capitalize the first character of modulename
for /f %%i in ('powershell -command "[CultureInfo]::CurrentCulture.TextInfo.ToTitleCase('%modulename%')"') do set modulenameCapitalized=%%i

:: Create Repository file to create the Repository file
echo package vn.com.bidv.feature.%modulename%.data > src\main\java\%package%\data\%modulenameCapitalized%Repository.kt
echo. >> src\main\java\%package%\data\%modulenameCapitalized%Repository.kt
echo import javax.inject.Inject >> src\main\java\%package%\data\%modulenameCapitalized%Repository.kt
echo import vn.com.bidv.network.domain.BaseRepository >> src\main\java\%package%\data\%modulenameCapitalized%Repository.kt
echo. >> src\main\java\%package%\data\%modulenameCapitalized%Repository.kt
echo class %modulenameCapitalized%Repository @Inject constructor( ) : BaseRepository() { >> src\main\java\%package%\data\%modulenameCapitalized%Repository.kt
echo. >> src\main\java\%package%\data\%modulenameCapitalized%Repository.kt
echo } >> src\main\java\%package%\data\%modulenameCapitalized%Repository.kt

:: Create DI file in the di package
echo package vn.com.bidv.feature.%modulename%.di > src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo. >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo import dagger.Module >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo import dagger.Provides >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo import dagger.hilt.InstallIn >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo import dagger.hilt.components.SingletonComponent >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo import dagger.multibindings.IntoSet >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo import vn.com.bidv.feature.%modulename%.data.%modulenameCapitalized%Repository >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo import vn.com.bidv.feature.%modulename%.navigation.%modulenameCapitalized%Navigation >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo import javax.inject.Singleton >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo. >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo @Module >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo @InstallIn(SingletonComponent::class) >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo class %modulenameCapitalized%Module { >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo     @Provides >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo     @Singleton >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo     fun provide%modulenameCapitalized%Repository(): %modulenameCapitalized%Repository { >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo         return %modulenameCapitalized%Repository() >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo     } >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo     @Singleton >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo     @Provides >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo     @IntoSet >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo     fun provide%modulenameCapitalized%FeatureGraphBuilder(): FeatureGraphBuilder { >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo         return %modulenameCapitalized%Navigation() >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo     } >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt
echo } >> src\main\java\%package%\di\%modulenameCapitalized%Module.kt

:: Create Navigation file in the navigation package
echo package vn.com.bidv.feature.%modulename%.navigation > src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo. >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo import androidx.navigation.NavGraphBuilder >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo import androidx.navigation.NavHostController >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo import javax.inject.Inject >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo. >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo sealed class %modulenameCapitalized%Route(val route: String) { >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo } >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo. >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo class %modulenameCapitalized%Navigation @Inject constructor() : FeatureGraphBuilder { >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo. >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo     override fun buildGraph( >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo         navGraphBuilder: NavGraphBuilder, >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo         navController: NavHostController, >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo         registeredRoutes: ^(args: List^<String^>^) -^> Unit >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo     ) { >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo         registeredRoutes(listOf()) >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo     } >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt
echo } >> src\main\java\%package%\navigation\%modulenameCapitalized%Navigation.kt

echo Done! Module %modulename% created with package %package%
pause
