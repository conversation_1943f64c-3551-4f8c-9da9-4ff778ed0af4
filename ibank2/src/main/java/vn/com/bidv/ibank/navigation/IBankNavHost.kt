package vn.com.bidv.ibank.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.ui.DefaultNoRouteScreen

/**
 * Top-level navigation graph. Navigation is organized as explained at
 * https://d.android.com/jetpack/compose/nav-adaptive
 *
 * The navigation graph defined in this file defines the different top level routes. Navigation
 * within each route is handled using state and Back Handlers.
 */
@Composable
fun IBankNavHost(
    modifier: Modifier = Modifier,
    navController: NavHostController,
    startDestination: String = IBankMainRouting.AuthRoutes.AuthMainRoute.route,
    featureGraphBuilder: Set<FeatureGraphBuilder>,
) {
    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier,
    ) {

        val listRegisteredRoute = arrayListOf("")

        val registerRoute: (args: List<String>) -> Unit = { listRoute ->
            listRegisteredRoute += listRoute
        }

        featureGraphBuilder.forEach {
            it.buildGraph(
                this,
                navController,
                registerRoute
            )
        }

        updateDefaultDestinationForMissingRoute(listRegisteredRoute)
    }

}

/**
 * Update default destination for missing routes.
 * This function iterates over the list of public routes and checks if they are registered.
 * If a route is not registered, it adds a composable for that route which displays a default screen.
 *
 * @param listRegisteredRoute List of registered routes.
 */
private fun NavGraphBuilder.updateDefaultDestinationForMissingRoute(
    listRegisteredRoute: List<String>,
) {
    IBankMainRouting.allRoutes.forEach { route ->
        if (!listRegisteredRoute.contains(route)) {
            composable(
                route = route,
            ) {
                DefaultNoRouteScreen()
            }
        }
    }

}