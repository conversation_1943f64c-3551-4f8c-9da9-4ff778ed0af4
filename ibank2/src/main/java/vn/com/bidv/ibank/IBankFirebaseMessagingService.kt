package vn.com.bidv.ibank

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.graphics.Bitmap
import android.media.RingtoneManager
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import coil.ImageLoader
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.designsystem.R
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.utils.NotificationUtils
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants.NotificationConstants
import vn.com.bidv.sdkbase.utils.constants.SdkBaseConstants.NotificationConstants.FCM_TRIGGER_UPDATE_TOTAL_NOTIFICATION_UNREAD

const val TAG = "IBankFirebaseMessagingService"

class IBankFirebaseMessagingService : FirebaseMessagingService() {
    private val imageLoader by lazy { ImageLoader(this) }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        BLogUtil.i(TAG + "Token Refreshed $token")
        // TODO register Token On Server
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        remoteMessage.notification?.run {
            val data = remoteMessage.data
            val moreInfo = data[NotificationConstants.MORE_INFO] ?: NotificationConstants.BLANK
            if (imageUrl != null) {
                CoroutineScope(Dispatchers.Default).launch {
                    val imageBitmap = NotificationUtils.loadBitmapWithCoil(
                        context = applicationContext,
                        imageLoader = imageLoader,
                        imageUrl = imageUrl.toString()
                    )
                    sendNotification(title, body, moreInfo, imageBitmap)
                }
            } else {
                sendNotification(title, body, moreInfo, null)
            }

            sendTriggerUpdateTotalNotificationUnread(title, body)
        }
    }

    private fun sendTriggerUpdateTotalNotificationUnread(title: String?, body: String?) {
        val intent = Intent(FCM_TRIGGER_UPDATE_TOTAL_NOTIFICATION_UNREAD)
        if (title.isNotNullOrEmpty() && body.isNotNullOrEmpty()) {
            LocalBroadcastManager.getInstance(applicationContext).sendBroadcast(intent)
        }
    }

    private fun sendNotification(
        title: String?,
        body: String?,
        moreInfoString: String,
        bitmap: Bitmap?
    ) {
        val currentTime = System.currentTimeMillis()
        val intent = NotificationUtils.makeNotificationIntent(
            context = this,
            notificationData = moreInfoString
        )

        val requestCode = currentTime.toInt()
        val pendingIntent = PendingIntent.getActivity(
            this,
            requestCode,
            intent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT,
        )

        val channelId = NotificationConstants.CHANNEL_ID
        val channelTitle = getString(vn.com.bidv.localization.R.string.thong_bao_he_thong)
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        val style = bitmap?.let {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                NotificationCompat.BigPictureStyle()
                    .bigPicture(it)
                    .setSummaryText(body)
                    .showBigPictureWhenCollapsed(true)
            } else {
                NotificationCompat.BigPictureStyle()
                    .bigPicture(it)
                    .setSummaryText(body)
            }
        } ?: NotificationCompat.BigTextStyle().bigText(body)
        val builder: NotificationCompat.Builder = NotificationCompat
            .Builder(this, channelId)
            .setSmallIcon(R.drawable.logo_bidv_small)
            .setContentTitle(title)
            .setContentText(body)
            .setStyle(style)
            .setShowWhen(true)
            .setWhen(currentTime)
            .setAutoCancel(true)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setSound(defaultSoundUri)
            .setContentIntent(pendingIntent)
        val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                channelTitle,
                NotificationManager.IMPORTANCE_HIGH
            )

            channel.setShowBadge(true)
            channel.lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            manager.createNotificationChannel(channel)
        }

        val notificationId = currentTime.toInt()
        manager.notify(notificationId, builder.build())
    }
}