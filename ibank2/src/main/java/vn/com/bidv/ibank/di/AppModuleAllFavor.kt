package vn.com.bidv.ibank.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import retrofit2.Retrofit
import vn.com.bidv.common.di.IoDispatcher
import vn.com.bidv.common.utils.Utils
import vn.com.bidv.ibank.BuildConfig
import vn.com.bidv.ibank.IBankNetworkConfig
import vn.com.bidv.ibank.data.SyncTimeRepository
import vn.com.bidv.ibank.data.UpdateVersionRepository
import vn.com.bidv.ibank.data.synctime.apis.SyncTimeApi
import vn.com.bidv.ibank.data.updateversion.apis.UpdateVersionApi
import vn.com.bidv.network.NetworkConfig
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class AppModuleAllFavor {
    @Provides
    @Singleton
    @IoDispatcher
    fun provideIODispatcher(): CoroutineDispatcher = Dispatchers.IO

    @Provides
    @Singleton
    fun provideNetworkConfig(@ApplicationContext context: Context): NetworkConfig {

        val nonAuthEndpoints = mutableListOf<String>()
        nonAuthEndpoints.addAll(IBankNetworkConfig.NON_AUTH_END_POINT.split(","))

        val flowAuthenticationEndPoint = mutableListOf<String>()
        flowAuthenticationEndPoint.addAll(IBankNetworkConfig.FLOW_AUTHENTICATION_END_POINT.split(","))

        val apiRequestDeviceIdEndPoint = mutableListOf<String>()
        apiRequestDeviceIdEndPoint.addAll(IBankNetworkConfig.APIS_REQUIRING_DEVICE_ID_END_POINT.split(","))

        val apisUseDefaultKeyEndPoint = mutableListOf<String>()
        apisUseDefaultKeyEndPoint.addAll(IBankNetworkConfig.APIS_USE_DEFAULT_KEY_ENDPOINT.split(","))

        val apisUseSidKeyEndPoint = mutableListOf<String>()
        apisUseSidKeyEndPoint.addAll(IBankNetworkConfig.APIS_USE_SID_KEY_END_POINT.split(","))

        val apisLoginEndPoint = mutableListOf<String>()
        apisLoginEndPoint.addAll(IBankNetworkConfig.LOGIN_END_POINT.split(","))

        val apisGenKey = mutableListOf<String>()
        apisGenKey.addAll(IBankNetworkConfig.APIS_GEN_KEY.split(","))

        val supportUrls: Map<String, String> = IBankNetworkConfig.SUPPORT_URLS.split(",")
            .mapNotNull { it.split("=").takeIf { pair -> pair.size == 2 } }
            .associate { it[0] to it[1] }

        val apisNotEncryptEndPoint = mutableListOf<String>()
        apisNotEncryptEndPoint.addAll(IBankNetworkConfig.APIS_NOT_ENCRYPT_END_POINT.split(","))

        return NetworkConfig(
            debugMode = IBankNetworkConfig.DEBUG_MODE,
            timeOutInMiniSecond = IBankNetworkConfig.TIME_OUT_IN_MINI_SECOND.toLong(),
            maxRequestPerHost = IBankNetworkConfig.MAX_REQUEST_PER_HOST.toInt(),
            baseUrl = IBankNetworkConfig.BASE_URL,
            retryOnConnectFail = IBankNetworkConfig.RETRY_ON_CONNECT_FAIL,
            networkCodeSuccess = IBankNetworkConfig.NET_WORK_CODE_SUCCESS.split(","),
            useMockData = IBankNetworkConfig.USE_MOCK_DATA,
            appVersion = BuildConfig.VERSION_NAME,
            deviceId = Utils.getAndroidId(context),
            apisLoginEndPoint = apisLoginEndPoint,
            refreshTokenEndPoint = IBankNetworkConfig.REFRESH_TOKEN_END_POINT,
            headerMap = IBankNetworkConfig.headerMap,
            nonAuthEndPoint = nonAuthEndpoints,
            osVersion = Utils.getOSVersion(),
            deviceModel = Utils.getDeviceModel(),
            language = "vi-vn",
            flowAuthenticationEndPoint = flowAuthenticationEndPoint,
            apisRequiringDeviceIdEndPoint = apiRequestDeviceIdEndPoint,
            turnOnBiometricEndPoint = IBankNetworkConfig.TURN_ON_BIOMETRIC_END_POINT,
            verifyOtpBiometricEndPoint = IBankNetworkConfig.VERIFY_OTP_BIOMETRIC_END_POINT,
            appTimeoutMiniSecond = IBankNetworkConfig.APP_TIMEOUT_MINI_SECOND.toLong(),
            apisUseDefaultKeyEndPoint = apisUseDefaultKeyEndPoint,
            apisUseSidKeyEndPoint = apisUseSidKeyEndPoint,
            apisGenKey = apisGenKey,
            supportUrls = supportUrls,
            apisNotEncryptEndPoint = apisNotEncryptEndPoint
        )
    }

    @Singleton
    @Provides
    fun provideUpdateVersionApi(retrofit: Retrofit): UpdateVersionApi {
        return retrofit.create(UpdateVersionApi::class.java)
    }

    @Singleton
    @Provides
    fun provideUpdateVersionRepository(updateVersionApi: UpdateVersionApi): UpdateVersionRepository {
        return UpdateVersionRepository(updateVersionApi)
    }
    
    @Singleton
    @Provides
    fun provideSyncTimeRepository(syncTimeApi: SyncTimeApi): SyncTimeRepository {
        return SyncTimeRepository(syncTimeApi)
    }

    @Singleton
    @Provides
    fun provideSyncTimeApi(retrofit: Retrofit): SyncTimeApi {
        return retrofit.create(SyncTimeApi::class.java)
    }
}