package vn.com.bidv.ibank

import androidx.compose.runtime.Immutable
import vn.com.bidv.common.patterns.mvi.Reducer
import vn.com.bidv.common.patterns.mvi.SideEffect
import vn.com.bidv.common.patterns.mvi.UIEffect
import vn.com.bidv.common.patterns.mvi.ViewEvent
import vn.com.bidv.common.patterns.mvi.ViewState

class MainActivityReducer :
    Reducer<MainActivityReducer.MainActivityViewState, MainActivityReducer.MainActivityViewEvent, MainActivityReducer.MainActivityViewEffect> {

    data class MainActivityViewState(
        val isInitSuccess: Boolean = false,
    ) : ViewState

    @Immutable
    sealed class MainActivityViewEvent : ViewEvent {
        data class OnLogout(val isExistApp: Boolean) : MainActivityViewEvent()
        data class OnLogoutSuccess(val isExistApp: Boolean) : MainActivityViewEvent()
    }

    @Immutable
    sealed class MainActivityViewEffect : SideEffect {
        data class Logout(val isExistApp: Boolean) : MainActivityViewEffect()
        data class LogoutSuccess(val isExistApp: Boolean) : MainActivityViewEffect(), UIEffect
    }

    override fun reduce(
        previousState: MainActivityViewState,
        event: MainActivityViewEvent,
    ): Pair<MainActivityViewState, MainActivityViewEffect?> {
        return when(event) {
            is MainActivityViewEvent.OnLogout -> {
                previousState to MainActivityViewEffect.Logout(
                    isExistApp = event.isExistApp
                )
            }

            is MainActivityViewEvent.OnLogoutSuccess -> {
                previousState to MainActivityViewEffect.LogoutSuccess(
                    isExistApp = event.isExistApp
                )
            }
        }
    }
}
