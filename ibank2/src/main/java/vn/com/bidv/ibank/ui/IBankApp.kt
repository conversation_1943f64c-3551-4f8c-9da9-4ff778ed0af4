package vn.com.bidv.ibank.ui

import  android.Manifest
import android.app.Activity
import android.os.Build
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarDuration
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavHostController
import kotlinx.coroutines.flow.collectLatest
import vn.com.bidv.common.extenstion.isNotNullOrEmpty
import vn.com.bidv.common.utils.CollectSideEffect
import vn.com.bidv.designsystem.component.IBankLoaderIndicatorsDialog
import vn.com.bidv.designsystem.component.feedback.modalconfirm.DialogButtonInfo
import vn.com.bidv.designsystem.component.feedback.modalconfirm.IBankModalConfirm
import vn.com.bidv.designsystem.component.feedback.modalconfirm.ModalConfirmType
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBar
import vn.com.bidv.designsystem.component.feedback.snackbar.IBankSnackBarInfo
import vn.com.bidv.feature.common.domain.notificationcommon.TabNotiType
import vn.com.bidv.feature.login.navigation.isInLoginFlow
import vn.com.bidv.ibank.MainActivityReducer
import vn.com.bidv.ibank.MainActivityViewModel
import vn.com.bidv.ibank.navigation.IBankNavHost
import vn.com.bidv.localization.R
import vn.com.bidv.log.BLogUtil
import vn.com.bidv.sdkbase.navigation.FeatureGraphBuilder
import vn.com.bidv.sdkbase.navigation.IBankMainRouting
import vn.com.bidv.sdkbase.navigation.LocalIBankNavController
import vn.com.bidv.sdkbase.navigation.NavigationHelper
import vn.com.bidv.sdkbase.navigation.notifyroute.NotificationBuilderUtils
import vn.com.bidv.sdkbase.utils.PermissionUtils
import vn.com.bidv.sdkbase.utils.exts.ContextExt.openAppInGooglePlay

@Composable
fun IBankApp(
    viewModel: MainActivityViewModel,
    navController: NavHostController,
    modifier: Modifier = Modifier,
    featureGraphBuilder: Set<FeatureGraphBuilder>
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current
    val currentActivity = LocalContext.current as? Activity
    CompositionLocalProvider(LocalIBankNavController provides navController) {
        Scaffold(
            contentWindowInsets = WindowInsets(0, 0, 0, 0),
        ) { padding ->
            BLogUtil.d("app padding: $padding")
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
                    .pointerInput(Unit) {
                        detectTapGestures(
                            onTap = {
                                keyboardController?.hide()
                                // clear current focus
                                focusManager.clearFocus()
                            }
                        )
                    }
            ) {
                IBankNavHost(
                    navController = navController,
                    modifier = modifier,
                    startDestination = if (viewModel.isLoginSuccess()) {
                        IBankMainRouting.HomeRoute.HomeMainRoute.route
                    } else {
                        IBankMainRouting.AuthRoutes.AuthMainRoute.route
                    },
                    featureGraphBuilder = featureGraphBuilder
                )
                LoadingIndicator(viewModel.isLoading.collectAsState())

                HandleGeneralError(viewModel)
                HandleSessionExpired(navController, viewModel)
                HandleExitApp(navController, viewModel)
                HandleNavigateNotification(navController, viewModel)
                SnackBarBase(viewModel)
                RequestPermission(navController)
                CheckVersion(viewModel)
                SyncTimeSmartOTP(viewModel)

                LaunchedEffect(Unit) {
                    viewModel.effects.collect { effect ->
                        when (effect) {
                            is MainActivityReducer.MainActivityViewEffect.LogoutSuccess -> {
                                if (effect.isExistApp) {
                                    currentActivity?.finish()
                                } else {
                                    NavigationHelper.navigationToLogin(navController)
                                }
                                viewModel.clearEffect()
                            }
                            else -> {}
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun SyncTimeSmartOTP(viewModel: MainActivityViewModel) {
    var isNeedToSyncTime by remember { mutableStateOf(false) }
    CollectSideEffect(viewModel.isNeedToSyncTime) {
        isNeedToSyncTime = it
    }
    if (isNeedToSyncTime) {
        viewModel.syncTimeSmartOtp()
    }
}

@Composable
fun CheckVersion(viewModel: MainActivityViewModel) {
    viewModel.checkVersion()
    var isShowUpdateApp by remember { mutableStateOf(false) }
    CollectSideEffect(viewModel.currentVersionResponseDMO) {
        if (it?.isNewVersion == true) {
            isShowUpdateApp = true
        }
    }
    if (isShowUpdateApp) {
        val context = LocalContext.current
        val isForceUpdate =
            viewModel.currentVersionResponseDMO.collectAsState().value?.isForceUpdate ?: false
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Warning,
            supportingText = viewModel.currentVersionResponseDMO.collectAsState().value?.content
                ?: "",
            listDialogButtonInfo = if (isForceUpdate) {
                listOf(
                    DialogButtonInfo(
                        label = stringResource(R.string.cap_nhat),
                        onClick = {
                            context.openAppInGooglePlay()
                        }
                    ))
            } else {
                listOf(
                    DialogButtonInfo(
                        label = stringResource(R.string.cap_nhat),
                        onClick = {
                            context.openAppInGooglePlay()
                        }
                    ),
                    DialogButtonInfo(
                        label = stringResource(R.string.de_sau),
                    ))
            },
            isShowIconClose = !isForceUpdate,
            onDismissRequest = {
                if (!isForceUpdate) {
                    isShowUpdateApp = false
                    viewModel.resetCheckVersion()
                }
            }
        )
    }
}

@Composable
fun HandleNavigateNotification(
    navController: NavHostController,
    viewModel: MainActivityViewModel
) {
    LaunchedEffect(Unit) {
        viewModel.notificationModel.collectLatest { notificationModel ->
            notificationModel.notificationData?.let { notificationData ->
                notificationData.takeIf { _ -> notificationModel.isLogin }?.run {
                    viewModel.cleanNotificationData()
                    TabNotiType.safeValueOf(notiType)?.let { tabNotiType ->
                        viewModel.markNotificationAsRead(
                            notifyId = notifyId,
                            displayTab = displayTab,
                            tabNotiType = tabNotiType
                        )
                    }
                    NotificationBuilderUtils.routeToDestinationByNotyInfo(
                        navController = navController,
                        navigateId = navigateId,
                        displayTab = displayTab,
                        listParam = listParam,
                        isRedirectDefault = true
                    )
                } ?: run {
                    NavigationHelper.navigationToLogin(navController)
                }
            }
        }
    }
}

@Composable
private fun RequestPermission(navController: NavHostController) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        PermissionUtils.checkPermissionAndDoSomeThing(
            navController.context,
            Manifest.permission.POST_NOTIFICATIONS,
            onPermissionGranted = {
            }
        )
    }
}

@Composable
fun HandleSessionExpired(navController: NavHostController, viewModel: MainActivityViewModel) {
    var sessionExpiredMessage by remember { mutableStateOf<String?>(null) }
    CollectSideEffect(viewModel.sessionExpiredMessage) {
        sessionExpiredMessage = it
    }
    if (sessionExpiredMessage != null) {
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Warning,
            supportingText =
                sessionExpiredMessage?.takeIf { it.trim().isNotEmpty() }
                    ?: stringResource(R.string.phien_dang_nhap_het_hieu_luc_quy_khach_vui_long_thuc_hien_dang_nhap_lai),
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(R.string.dong),
                    onClick = {
                        navController.popBackStack(
                            navController.graph.startDestinationId,
                            inclusive = true
                        )
                        NavigationHelper.navigationToLogin(navController)
                    }
                )
            ),
            onDismissRequest = {
                sessionExpiredMessage = null
            }
        )
    }
}

@Composable
private fun HandleExitApp(navController: NavHostController, viewModel: MainActivityViewModel) {
    var showExitDialog by remember { mutableStateOf(false) }
    val currentActivity = LocalContext.current as? Activity
    BackHandler {
        if (navController.previousBackStackEntry == null) {
            if (isInLoginFlow(navController)) {
                currentActivity?.finish()
            } else {
                showExitDialog = true
            }
        } else {
            navController.popBackStack()
        }

    }

    if (showExitDialog) {
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Warning,
            supportingText = stringResource(R.string.ban_co_chac_chan_muon_thoat_ung_dung),
            onDismissRequest = {
                showExitDialog = false
            },
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(R.string.huy),
                    onClick = {
                        showExitDialog = false
                    }
                ),
                DialogButtonInfo(
                    label = stringResource(R.string.yes),
                    onClick = {
                        viewModel.sendEvent(MainActivityReducer.MainActivityViewEvent.OnLogout(true))
                    }
                )
            )

        )
    }
}

@Composable
private fun HandleGeneralError(viewModel: MainActivityViewModel) {
    var errorMessage by remember { mutableStateOf<String?>(null) }
    CollectSideEffect(viewModel.errorMessage) { error ->
        errorMessage = error
    }

    if (errorMessage.isNotNullOrEmpty()) {
        IBankModalConfirm(
            modalConfirmType = ModalConfirmType.Error,
            title = stringResource(R.string.loi),
            supportingText = errorMessage,
            listDialogButtonInfo = listOf(
                DialogButtonInfo(
                    label = stringResource(R.string.dong),
                )
            ),
            onDismissRequest = {
                errorMessage = null
            }
        )
    }
}

@Composable
private fun LoadingIndicator(isShowLoading: State<Boolean>) {
    if (isShowLoading.value) {
        IBankLoaderIndicatorsDialog(
            onDismissRequest = {
                //TODO handle when dismiss loading
            }
        )
    }
}

@Composable
private fun SnackBarBase(viewModel: MainActivityViewModel) {
    var snackBarInfo by remember { mutableStateOf<IBankSnackBarInfo?>(null) }
    CollectSideEffect(viewModel.snackBarMessage) { message ->
        snackBarInfo = message
    }

    snackBarInfo?.let {
        val snackBarOffset = viewModel.getSnackBarOffset()
        val density = LocalDensity.current
        val paddingHorizontal = with(density) { snackBarOffset.x.toDp() }
        val paddingTop = with(density) { snackBarOffset.y.toDp() }
        var isVisible by remember(it) { mutableStateOf(true) }

        Box(modifier = Modifier.fillMaxSize()) {
            IBankSnackBar(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(
                        start = paddingHorizontal,
                        end = paddingHorizontal,
                        top = paddingTop
                    ),
                onDismiss = {
                    isVisible = false
                    snackBarInfo = null
                },
                snackbarDuration = SnackbarDuration.Short,
                isVisible = isVisible,
                message = it.message,
                primaryButtonText = it.primaryButtonText,
                primaryButtonAction = { it.primaryButtonAction() },
                secondaryButtonText = it.secondaryButtonText,
                secondaryButtonAction = { it.secondaryButtonAction() }
            )
        }
    }
}






